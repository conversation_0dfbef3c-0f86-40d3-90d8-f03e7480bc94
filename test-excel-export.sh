#!/bin/bash

# Excel导出功能测试脚本
# 测试经营调度会看板Excel导出是否正常工作

echo "🧪 开始测试Excel导出功能..."

# 设置测试参数
YEAR="2025"
MONTH="1"
MIN_AMOUNT="100"
OUTPUT_FILE="test_export_$(date +%Y%m%d_%H%M%S).xlsx"

# 启动应用（如果未启动）
echo "📋 检查应用状态..."
if ! curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo "❌ 应用未启动，请先启动应用"
    echo "💡 运行: ./scripts/test-startup.sh"
    exit 1
fi

echo "✅ 应用运行正常"

# 测试导出接口
echo "📋 测试Excel导出接口..."
echo "   年份: $YEAR"
echo "   月份: $MONTH" 
echo "   最小金额: ${MIN_AMOUNT}万元"

HTTP_STATUS=$(curl -s -w "%{http_code}" \
    "http://localhost:8080/api/export/test/managementBoard?year=$YEAR&month=$MONTH&minAmount=$MIN_AMOUNT" \
    -o "$OUTPUT_FILE")

echo "📊 HTTP状态码: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "200" ]; then
    # 检查文件是否生成
    if [ -f "$OUTPUT_FILE" ]; then
        FILE_SIZE=$(ls -lh "$OUTPUT_FILE" | awk '{print $5}')
        echo "✅ Excel文件导出成功"
        echo "   文件名: $OUTPUT_FILE"
        echo "   文件大小: $FILE_SIZE"
        
        # 检查文件类型
        FILE_TYPE=$(file "$OUTPUT_FILE")
        echo "   文件类型: $FILE_TYPE"
        
        if echo "$FILE_TYPE" | grep -q "Excel"; then
            echo "✅ 文件格式正确 (Excel格式)"
        elif echo "$FILE_TYPE" | grep -q "Zip"; then
            echo "✅ 文件格式正确 (XLSX是ZIP格式)"
        else
            echo "⚠️  文件格式可能有问题: $FILE_TYPE"
        fi
        
        # 尝试用系统默认程序打开
        echo "🔍 尝试验证文件可读性..."
        if command -v unzip >/dev/null 2>&1; then
            if unzip -t "$OUTPUT_FILE" >/dev/null 2>&1; then
                echo "✅ XLSX文件结构完整"
            else
                echo "❌ XLSX文件结构损坏"
            fi
        fi
        
    else
        echo "❌ 文件生成失败"
    fi
else
    echo "❌ 导出请求失败"
    echo "📋 错误信息:"
    cat "$OUTPUT_FILE" 2>/dev/null || echo "   无错误信息"
fi

# 清理测试文件（可选）
read -p "🗑️  是否删除测试文件 $OUTPUT_FILE? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f "$OUTPUT_FILE"
    echo "✅ 测试文件已删除"
else
    echo "📁 测试文件保留: $OUTPUT_FILE"
fi

echo "🎯 Excel导出功能测试完成"