#!/bin/bash
# 功能保护测试脚本 - 快速验证核心功能

set -e

echo "🛡️ 运行功能保护测试..."
echo "========================"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 运行简化版保护测试
echo ""
echo "1️⃣ 运行基础功能检查..."
if [ -f "./scripts/simple-protection-test.sh" ]; then
    ./scripts/simple-protection-test.sh || {
        echo "❌ 基础功能检查失败"
        exit 1
    }
else
    # 如果简化版不存在，尝试启动测试
    if [ -f "./scripts/test-startup.sh" ]; then
        timeout 60 ./scripts/test-startup.sh > /dev/null 2>&1 || {
            echo "⚠️ 启动测试失败（可能是依赖问题）"
        }
    fi
fi

# 2. 运行功能保护测试（如果存在）
echo ""
echo "2️⃣ 测试核心功能..."
if mvn test -Dtest=FunctionProtectionTest -q > /dev/null 2>&1; then
    echo "✅ 功能保护测试通过"
else
    # 如果测试类不存在，运行基础测试
    echo "⚠️ 功能保护测试未找到，运行基础测试..."
    mvn test -q -DfailIfNoTests=false || {
        echo "❌ 测试失败"
        exit 1
    }
fi

# 3. 前端构建测试
echo ""
echo "3️⃣ 测试前端构建..."
cd FinancialSystem-web
if npm run build --silent > /dev/null 2>&1; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    echo "💡 运行 'npm run build' 查看详细错误"
    cd ..
    exit 1
fi
cd ..

# 计算总耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "========================"
echo "✅ 所有保护测试通过！"
echo "⏱️ 总耗时: ${DURATION}秒"
