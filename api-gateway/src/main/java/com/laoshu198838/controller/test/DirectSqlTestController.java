package com.laoshu198838.controller.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import com.laoshu198838.service.DebtManagementService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 直接执行SQL验证数据
 */
@RestController
@RequestMapping("/api/test/sql")
@CrossOrigin(origins = {"http://localhost:3000", "http://**********:3000"})
public class DirectSqlTestController {
    
    private static final Logger logger = LoggerFactory.getLogger(DirectSqlTestController.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private DebtManagementService debtManagementService;
    
    /**
     * 执行用户提供的原始SQL - 验证3.77亿元
     */
    @GetMapping("/user-original-sql")
    public Map<String, Object> executeUserOriginalSql() {
        logger.info("=== 执行用户提供的原始SQL - 验证3.77亿元 ===");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 用户提供的原始SQL
            String sql = """
                -- 存量债权筛选数据 - 2025年6月
                WITH
                  year_start_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 上年末余额,
                      GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
                    FROM 减值准备表
                    WHERE 年份 = 2024 AND 月份 = 12
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    HAVING SUM(本月末债权余额) <> 0
                  ),
                  new_debt AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月新增债权) AS 当年累计新增债权
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  disposal AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 期间,
                      SUM(每月处置金额) AS 当年累计处置金额,
                      SUM(现金处置) AS 现金处置,
                      SUM(资产抵债) AS 资产抵债,
                      SUM(分期还款) AS 分期还款,
                      SUM(其他方式) - SUM(分期还款) AS 其他方式
                    FROM 处置表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
                  ),
                  current_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 当前余额
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 = 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  base_data AS (
                    SELECT
                      k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
                      COALESCE(y.上年末余额, 0) AS 上年末余额,
                      COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
                      COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
                      COALESCE(cb.当前余额, 0) AS 当前余额,
                      COALESCE(d.现金处置, 0) AS 现金处置,
                      COALESCE(d.资产抵债, 0) AS 资产抵债,
                      COALESCE(d.分期还款, 0) AS 分期还款,
                      COALESCE(d.其他方式, 0) AS 其他方式,
                      COALESCE(y.备注, '') AS 备注
                    FROM (
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
                      UNION
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
                    ) k
                    LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
                      AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
                    LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
                      AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
                    LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
                      AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
                    LEFT JOIN current_balance cb ON k.管理公司 = cb.管理公司 AND k.债权人 = cb.债权人 AND k.债务人 = cb.债务人
                      AND k.是否涉诉 = cb.是否涉诉 AND k.科目名称 = cb.科目名称 AND k.期间 = cb.期间
                    WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
                  )
                SELECT
                  SUM(上年末余额) AS 期初金额_合计,
                  SUM(
                    CASE
                      WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 累计处置金额
                      WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0
                      WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN
                        CASE
                          WHEN 累计处置金额 <= 当年新增债权 THEN 0
                          ELSE 累计处置金额 - 当年新增债权
                        END
                      ELSE 0
                    END
                  ) AS 累计处置债权_合计,
                  SUM(
                    CASE
                      WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN
                        上年末余额 - 累计处置金额
                      WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN
                        0
                      WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN
                        CASE
                          WHEN 累计处置金额 <= 当年新增债权 THEN 上年末余额
                          ELSE 上年末余额 - (累计处置金额 - 当年新增债权)
                        END
                      ELSE 上年末余额
                    END
                  ) AS 债权余额_合计
                FROM base_data
                WHERE 上年末余额 > 0  -- 只统计有存量债权的记录
            """;
            
            Map<String, Object> sqlResult = jdbcTemplate.queryForMap(sql);
            
            // 获取债权余额
            BigDecimal debtBalance = new BigDecimal(sqlResult.get("债权余额_合计").toString());
            BigDecimal debtBalanceYi = debtBalance.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
            
            result.put("期初金额_万元", sqlResult.get("期初金额_合计"));
            result.put("累计处置债权_万元", sqlResult.get("累计处置债权_合计"));
            result.put("债权余额_万元", debtBalance);
            result.put("债权余额_亿元", debtBalanceYi);
            
            logger.info("用户原始SQL执行结果 - 债权余额: {} 万元 ({} 亿元)", debtBalance, debtBalanceYi);
            
            // 判断是否是3.77亿元
            BigDecimal expected = new BigDecimal("3.77");
            BigDecimal diff = debtBalanceYi.subtract(expected).abs();
            
            if (diff.compareTo(new BigDecimal("0.01")) < 0) {
                result.put("验证结果", "✅ 确认是3.77亿元");
            } else {
                result.put("验证结果", "❌ 不是3.77亿元，实际是 " + debtBalanceYi + " 亿元");
            }
            
        } catch (Exception e) {
            logger.error("执行用户原始SQL失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行用户提供的正确SQL
     */
    @GetMapping("/user-correct")
    public Map<String, Object> executeUserCorrectSql() {
        logger.info("=== 执行用户提供的正确SQL ===");
        
        String sql = """
            WITH
              year_start_balance AS (
                SELECT
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  SUM(本月末债权余额) AS 上年末余额
                FROM 减值准备表
                WHERE 年份 = 2024 AND 月份 = 12
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                HAVING SUM(本月末债权余额) <> 0
              ),
              new_debt AS (
                SELECT
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  SUM(本月新增债权) AS 当年累计新增债权
                FROM 减值准备表
                WHERE 年份 = 2025 AND 月份 <= 6
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
              ),
              disposal AS (
                SELECT
                  管理公司, 债权人, 债务人, 是否涉诉, 期间,
                  SUM(每月处置金额) AS 当年累计处置金额,
                  SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额
                FROM 处置表
                WHERE 年份 = 2025 AND 月份 <= 6
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
              )
            SELECT
              ROUND(SUM(y.上年末余额), 2) AS 期初金额,
              ROUND(SUM(
                CASE
                  -- 6月处置（当月）
                  WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN 
                    COALESCE(d.当月处置金额, 0)
                  WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN
                    CASE
                      WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0
                      WHEN (COALESCE(d.当年累计处置金额, 0) - COALESCE(d.当月处置金额, 0)) >= COALESCE(n.当年累计新增债权, 0) THEN 
                        COALESCE(d.当月处置金额, 0)  -- 本月全部归存量
                      ELSE 
                        GREATEST(0, COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0) - 
                                 GREATEST(0, COALESCE(d.当年累计处置金额, 0) - COALESCE(d.当月处置金额, 0) - COALESCE(n.当年累计新增债权, 0)))
                    END
                  ELSE 0
                END
              ), 2) AS 本月清收金额,
              ROUND(SUM(
                CASE
                  WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN 
                    COALESCE(d.当年累计处置金额, 0)
                  WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN
                    CASE
                      WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0
                      ELSE COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0)
                    END
                  ELSE 0
                END
              ), 2) AS 本年累计清收金额,
              ROUND(SUM(y.上年末余额) - SUM(
                CASE
                  WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN 
                    COALESCE(d.当年累计处置金额, 0)
                  WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN
                    CASE
                      WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0
                      ELSE COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0)
                    END
                  ELSE 0
                END
              ), 2) AS 期末余额
            FROM year_start_balance y
            LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
              AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
              AND y.科目名称 = n.科目名称
            LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
              AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
            WHERE y.上年末余额 > 0
        """;
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            if (!rows.isEmpty()) {
                result.putAll(rows.get(0));
            }
            logger.info("用户正确SQL执行结果: {}", result);
        } catch (Exception e) {
            logger.error("SQL执行失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行当前代码使用的SQL查看差异
     */
    @GetMapping("/current-code-sql")
    public Map<String, Object> executeCurrentCodeSql() {
        logger.info("=== 执行当前代码使用的SQL ==");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 当前代码中的SQL（从OverdueDebtDecreaseRepository复制）
            String sql = """
                WITH
                  year_start_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 上年末余额,
                      GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
                    FROM 减值准备表
                    WHERE 年份 = 2024 AND 月份 = 12
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    HAVING SUM(本月末债权余额) <> 0
                  ),
                  new_debt AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月新增债权) AS 当年累计新增债权
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  disposal AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 期间,
                      SUM(每月处置金额) AS 当年累计处置金额,
                      SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额,
                      SUM(现金处置) AS 现金处置,
                      SUM(资产抵债) AS 资产抵债,
                      SUM(分期还款) AS 分期还款,
                      SUM(其他方式) - SUM(分期还款) AS 其他方式
                    FROM 处置表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
                  ),
                  current_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 当前余额
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 = 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  base_data AS (
                    SELECT
                      k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
                      COALESCE(y.上年末余额, 0) AS 上年末余额,
                      COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
                      COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
                      COALESCE(cb.当前余额, 0) AS 当前余额,
                      COALESCE(d.现金处置, 0) AS 现金处置,
                      COALESCE(d.资产抵债, 0) AS 资产抵债,
                      COALESCE(d.分期还款, 0) AS 分期还款,
                      COALESCE(d.其他方式, 0) AS 其他方式,
                      COALESCE(y.备注, '') AS 备注
                    FROM (
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
                      UNION
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
                    ) k
                    LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
                      AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
                    LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
                      AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
                    LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
                      AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
                    LEFT JOIN current_balance cb ON k.管理公司 = cb.管理公司 AND k.债权人 = cb.债权人 AND k.债务人 = cb.债务人
                      AND k.是否涉诉 = cb.是否涉诉 AND k.科目名称 = cb.科目名称 AND k.期间 = cb.期间
                    WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
                  )
                SELECT
                  SUM(上年末余额) AS 期初金额_合计,
                  SUM(
                    CASE
                      WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 累计处置金额
                      WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0
                      WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN
                        CASE
                          WHEN 累计处置金额 <= 当年新增债权 THEN 0
                          ELSE 累计处置金额 - 当年新增债权
                        END
                      ELSE 0
                    END
                  ) AS 累计处置债权_合计,
                  SUM(
                    CASE
                      WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN
                        上年末余额 - 累计处置金额
                      WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN
                        0
                      WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN
                        CASE
                          WHEN 累计处置金额 <= 当年新增债权 THEN 上年末余额
                          ELSE 上年末余额 - (累计处置金额 - 当年新增债权)
                        END
                      ELSE 上年末余额
                    END
                  ) AS 债权余额_合计
                FROM base_data
                WHERE 上年末余额 > 0  -- 只统计有存量债权的记录
            """;
            
            Map<String, Object> sqlResult = jdbcTemplate.queryForMap(sql);
            
            // 获取债权余额
            BigDecimal debtBalance = new BigDecimal(sqlResult.get("债权余额_合计").toString());
            BigDecimal debtBalanceYi = debtBalance.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
            
            result.put("期初金额_万元", sqlResult.get("期初金额_合计"));
            result.put("累计处置债权_万元", sqlResult.get("累计处置债权_合计"));
            result.put("债权余额_万元", debtBalance);
            result.put("债权余额_亿元", debtBalanceYi);
            
            logger.info("当前代码SQL执行结果 - 债权余额: {} 万元 ({} 亿元)", debtBalance, debtBalanceYi);
            
            // 对比用户SQL结果
            BigDecimal userExpected = new BigDecimal("37747.90");
            BigDecimal diff = debtBalance.subtract(userExpected);
            
            result.put("与用户SQL差异_万元", diff);
            result.put("分析", diff.abs().compareTo(BigDecimal.ZERO) > 0 ? 
                "存在差异，需要进一步分析" : "结果一致");
            
        } catch (Exception e) {
            logger.error("执行当前代码SQL失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行Repository的SQL查看明细数据
     */
    @GetMapping("/repository-detail-sql")
    public Map<String, Object> executeRepositoryDetailSql() {
        logger.info("=== 执行Repository的明细SQL ===");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 从OverdueDebtDecreaseRepository复制的完整SQL
            String sql = """
                WITH
                  year_start_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 上年末余额,
                      GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
                    FROM 减值准备表
                    WHERE 年份 = 2024 AND 月份 = 12
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    HAVING SUM(本月末债权余额) <> 0
                  ),
                  new_debt AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月新增债权) AS 当年累计新增债权
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  disposal AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 期间,
                      SUM(每月处置金额) AS 当年累计处置金额,
                      SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额,
                      SUM(现金处置) AS 现金处置,
                      SUM(资产抵债) AS 资产抵债,
                      SUM(分期还款) AS 分期还款,
                      SUM(其他方式) - SUM(分期还款) AS 其他方式
                    FROM 处置表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
                  ),
                  current_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 当前余额
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 = 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  base_data AS (
                    SELECT
                      k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
                      COALESCE(y.上年末余额, 0) AS 上年末余额,
                      COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
                      COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
                      COALESCE(cb.当前余额, 0) AS 当前余额,
                      COALESCE(d.现金处置, 0) AS 现金处置,
                      COALESCE(d.资产抵债, 0) AS 资产抵债,
                      COALESCE(d.分期还款, 0) AS 分期还款,
                      COALESCE(d.其他方式, 0) AS 其他方式,
                      COALESCE(y.备注, '') AS 备注
                    FROM (
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
                      UNION
                      SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
                    ) k
                    LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
                      AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
                    LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
                      AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
                    LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
                      AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
                    LEFT JOIN current_balance cb ON k.管理公司 = cb.管理公司 AND k.债权人 = cb.债权人 AND k.债务人 = cb.债务人
                      AND k.是否涉诉 = cb.是否涉诉 AND k.科目名称 = cb.科目名称 AND k.期间 = cb.期间
                    WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
                  )
                SELECT
                  '存量债权' AS 数据类型,
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  上年末余额 AS 期初金额,
                  -- 存量债权累计处置拆分逻辑
                  CASE
                    WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 累计处置金额
                    WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0
                    WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN
                      CASE
                        WHEN 累计处置金额 <= 当年新增债权 THEN 0
                        ELSE 累计处置金额 - 当年新增债权
                      END
                    ELSE 0
                  END AS 累计处置债权,
                  -- 存量债权余额拆分逻辑
                  CASE
                    WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN
                      上年末余额 - 累计处置金额
                    WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN
                      0
                    WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN
                      CASE
                        WHEN 累计处置金额 <= 当年新增债权 THEN 上年末余额
                        ELSE 上年末余额 - (累计处置金额 - 当年新增债权)
                      END
                    ELSE 上年末余额
                  END AS 债权余额,
                  现金处置, 资产抵债, 分期还款, 其他方式, 备注
                FROM base_data
                WHERE 上年末余额 > 0  -- 只返回有存量债权的记录
                ORDER BY 管理公司, 债权人, 债务人, 期间
            """;
            
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            
            // 统计结果
            BigDecimal totalYearBegin = BigDecimal.ZERO;
            BigDecimal totalCumulative = BigDecimal.ZERO;
            BigDecimal totalEndBalance = BigDecimal.ZERO;
            int rowCount = 0;
            
            for (Map<String, Object> row : rows) {
                rowCount++;
                BigDecimal yearBegin = new BigDecimal(row.get("期初金额").toString());
                BigDecimal cumulative = new BigDecimal(row.get("累计处置债权").toString());
                BigDecimal balance = new BigDecimal(row.get("债权余额").toString());
                
                totalYearBegin = totalYearBegin.add(yearBegin);
                totalCumulative = totalCumulative.add(cumulative);
                totalEndBalance = totalEndBalance.add(balance);
            }
            
            result.put("明细行数", rowCount);
            result.put("期初金额合计_万元", totalYearBegin);
            result.put("累计处置债权合计_万元", totalCumulative);
            result.put("债权余额合计_万元", totalEndBalance);
            result.put("债权余额合计_亿元", totalEndBalance.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP));
            
            // 对比目标值
            BigDecimal target = new BigDecimal("37747.90");
            BigDecimal diff = totalEndBalance.subtract(target);
            result.put("与目标差异_万元", diff);
            result.put("验证结果", diff.abs().compareTo(new BigDecimal("1")) < 0 ? 
                "✅ 明细累加结果正确" : "❌ 明细累加结果有差异");
            
            logger.info("Repository明细SQL执行结果 - 债权余额合计: {} 万元", totalEndBalance);
            
        } catch (Exception e) {
            logger.error("执行Repository明细SQL失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 验证并对比SQL结果和API结果
     */
    @GetMapping("/verify-compare")
    public Map<String, Object> verifyAndCompare() {
        logger.info("=== 开始验证SQL并对比API结果 ===");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 1. 执行用户提供的SQL获取正确结果
            String userSql = """
                WITH
                  year_start_balance AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月末债权余额) AS 上年末余额
                    FROM 减值准备表
                    WHERE 年份 = 2024 AND 月份 = 12
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    HAVING SUM(本月末债权余额) <> 0
                  ),
                  new_debt AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                      SUM(本月新增债权) AS 当年累计新增债权
                    FROM 减值准备表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                  ),
                  disposal AS (
                    SELECT
                      管理公司, 债权人, 债务人, 是否涉诉, 期间,
                      SUM(每月处置金额) AS 当年累计处置金额
                    FROM 处置表
                    WHERE 年份 = 2025 AND 月份 <= 6
                    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
                  )
                SELECT
                  COALESCE(SUM(y.上年末余额), 0) AS 期初金额,
                  COALESCE(SUM(
                    CASE
                      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN COALESCE(d.当年累计处置金额, 0)
                      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN
                        CASE
                          WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0
                          ELSE COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0)
                        END
                      ELSE 0
                    END
                  ), 0) AS 累计处置债权,
                  COALESCE(SUM(
                    CASE
                      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN
                        y.上年末余额 - COALESCE(d.当年累计处置金额, 0)
                      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN
                        CASE
                          WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN y.上年末余额
                          ELSE y.上年末余额 - (COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0))
                        END
                      ELSE y.上年末余额
                    END
                  ), 0) AS 债权余额
                FROM year_start_balance y
                LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
                  AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
                  AND y.科目名称 = n.科目名称
                LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
                  AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
                WHERE y.上年末余额 > 0
            """;
            
            Map<String, Object> sqlResult = jdbcTemplate.queryForMap(userSql);
            response.put("用户SQL执行结果", sqlResult);
            
            // 转换为亿元
            BigDecimal debtBalance = new BigDecimal(sqlResult.get("债权余额").toString());
            response.put("用户SQL债权余额_万元", debtBalance);
            response.put("用户SQL债权余额_亿元", debtBalance.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 2. 调用现有API获取结果
            Map<String, Object> apiResult = debtManagementService.getDebtCollectionStatus("2025", "6", "全部");
            BigDecimal apiPeriodEnd = new BigDecimal(apiResult.get("periodEndAmount").toString());
            response.put("现有API期末余额_万元", apiPeriodEnd);
            response.put("现有API期末余额_亿元", apiPeriodEnd.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
            
            // 3. 计算差异
            BigDecimal diff = debtBalance.subtract(apiPeriodEnd);
            response.put("差异_万元", diff);
            response.put("差异_亿元", diff.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP));
            
            // 4. 分析结论
            if (diff.abs().compareTo(new BigDecimal("1")) < 0) {
                response.put("结论", "✅ 数据基本一致");
            } else {
                response.put("结论", "❌ 存在差异，需要修正");
                response.put("建议", "用户SQL显示3.77亿元是正确的，需要修正API逻辑");
            }
            
            logger.info("验证对比结果: {}", response);
            
        } catch (Exception e) {
            logger.error("验证对比失败", e);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
}