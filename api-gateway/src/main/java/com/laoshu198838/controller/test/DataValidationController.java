package com.laoshu198838.controller.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.math.BigDecimal;

/**
 * 数据验证控制器 - 用于诊断数据不一致问题
 */
@RestController
@RequestMapping("/api/test/validate")
@CrossOrigin(origins = {"http://localhost:3000", "http://**********:3000"})
public class DataValidationController {
    
    private static final Logger logger = LoggerFactory.getLogger(DataValidationController.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 直接执行用户提供的SQL查询，验证期望结果
     */
    @GetMapping("/user-sql")
    public Map<String, Object> executeUserSql() {
        logger.info("=== 执行用户提供的SQL查询 ===");
        
        String sql = """
            WITH 
              year_start_balance AS (
                SELECT 
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  SUM(本月末债权余额) AS 上年末余额
                FROM 减值准备表
                WHERE 年份 = 2024 AND 月份 = 12
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                HAVING SUM(本月末债权余额) <> 0
              ),
              new_debt AS (
                SELECT 
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  SUM(本月末债权余额) AS 当年新增债权
                FROM 减值准备表
                WHERE 年份 = 2025 AND 月份 BETWEEN 1 AND 6
                  AND (管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间) NOT IN (
                    SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    FROM 减值准备表
                    WHERE 年份 = 2024 AND 月份 = 12
                  )
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
              ),
              disposal AS (
                SELECT 
                  管理公司, 债权人, 债务人, 是否涉诉, 期间,
                  SUM(每月处置金额) AS 累计处置金额,
                  SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额
                FROM 处置表
                WHERE 年份 = 2025 AND 月份 <= 6
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
              )
            SELECT 
              '用户SQL-存量债权(n.当年新增=0)' AS 查询类型,
              COUNT(DISTINCT CONCAT(y.管理公司, '|', y.债权人, '|', y.债务人)) AS 记录数,
              COALESCE(SUM(y.上年末余额), 0) AS 期初金额,
              COALESCE(SUM(d.累计处置金额 * y.上年末余额 / (y.上年末余额 + COALESCE(n.当年新增债权, 0))), 0) AS 累计清收金额,
              COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(d.累计处置金额 * y.上年末余额 / (y.上年末余额 + COALESCE(n.当年新增债权, 0))), 0) AS 期末余额
            FROM year_start_balance y
            LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
              AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
              AND y.科目名称 = n.科目名称
            LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
              AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
            WHERE y.上年末余额 > 0 AND COALESCE(n.当年新增债权, 0) = 0
        """;
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            if (!rows.isEmpty()) {
                result.putAll(rows.get(0));
            }
            logger.info("用户SQL查询结果: {}", result);
        } catch (Exception e) {
            logger.error("用户SQL执行失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 执行修正后的SQL查询（不过滤新增债权）
     */
    @GetMapping("/modified-sql")
    public Map<String, Object> executeModifiedSql() {
        logger.info("=== 执行修正后的SQL查询 ===");
        
        String sql = """
            WITH 
              year_start_balance AS (
                SELECT 
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  SUM(本月末债权余额) AS 上年末余额
                FROM 减值准备表
                WHERE 年份 = 2024 AND 月份 = 12
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                HAVING SUM(本月末债权余额) <> 0
              ),
              new_debt AS (
                SELECT 
                  管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
                  SUM(本月末债权余额) AS 当年新增债权
                FROM 减值准备表
                WHERE 年份 = 2025 AND 月份 BETWEEN 1 AND 6
                  AND (管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间) NOT IN (
                    SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                    FROM 减值准备表
                    WHERE 年份 = 2024 AND 月份 = 12
                  )
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
              ),
              disposal AS (
                SELECT 
                  管理公司, 债权人, 债务人, 是否涉诉, 期间,
                  SUM(每月处置金额) AS 累计处置金额,
                  SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额
                FROM 处置表
                WHERE 年份 = 2025 AND 月份 <= 6
                GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
              )
            SELECT 
              '修正SQL-所有存量债权' AS 查询类型,
              COUNT(DISTINCT CONCAT(y.管理公司, '|', y.债权人, '|', y.债务人)) AS 记录数,
              COALESCE(SUM(y.上年末余额), 0) AS 期初金额,
              COALESCE(SUM(d.累计处置金额 * y.上年末余额 / (y.上年末余额 + COALESCE(n.当年新增债权, 0))), 0) AS 累计清收金额,
              COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(d.累计处置金额 * y.上年末余额 / (y.上年末余额 + COALESCE(n.当年新增债权, 0))), 0) AS 期末余额
            FROM year_start_balance y
            LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
              AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
              AND y.科目名称 = n.科目名称
            LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
              AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
            WHERE y.上年末余额 > 0
        """;
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            if (!rows.isEmpty()) {
                result.putAll(rows.get(0));
            }
            logger.info("修正SQL查询结果: {}", result);
        } catch (Exception e) {
            logger.error("修正SQL执行失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 对比两种SQL的差异
     */
    @GetMapping("/compare")
    public Map<String, Object> compareSqlResults() {
        logger.info("=== 对比两种SQL查询结果 ===");
        
        Map<String, Object> userResult = executeUserSql();
        Map<String, Object> modifiedResult = executeModifiedSql();
        
        Map<String, Object> comparison = new HashMap<>();
        comparison.put("userSql", userResult);
        comparison.put("modifiedSql", modifiedResult);
        
        // 计算差异
        if (!userResult.containsKey("error") && !modifiedResult.containsKey("error")) {
            BigDecimal userEndAmount = new BigDecimal(userResult.get("期末余额").toString());
            BigDecimal modifiedEndAmount = new BigDecimal(modifiedResult.get("期末余额").toString());
            BigDecimal difference = modifiedEndAmount.subtract(userEndAmount);
            
            comparison.put("difference", difference);
            comparison.put("differenceRatio", userEndAmount.compareTo(BigDecimal.ZERO) > 0 
                ? difference.divide(userEndAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)) 
                : BigDecimal.ZERO);
        }
        
        return comparison;
    }
}