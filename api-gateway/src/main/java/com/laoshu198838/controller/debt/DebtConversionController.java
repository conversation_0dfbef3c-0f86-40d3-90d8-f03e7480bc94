package com.laoshu198838.controller.debt;

import com.laoshu198838.dto.debt.DebtConversionRequestDTO;
import com.laoshu198838.dto.debt.DebtConversionResponseDTO;
import com.laoshu198838.dto.debt.DebtSearchResultDTO;
import com.laoshu198838.service.DebtConversionService;

import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 债权转换控制器
 * 提供诉讼与非诉讼债权相互转换的API接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/debts/conversion")
@CrossOrigin(origins = {"http://localhost:3000", "http://**********:3000"})
public class DebtConversionController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtConversionController.class);
    
    private final DebtConversionService debtConversionService;
    
    public DebtConversionController(DebtConversionService debtConversionService) {
        this.debtConversionService = debtConversionService;
    }
    
    /**
     * 搜索可转换的债权记录
     * 
     * @param creditor 债权人（可选）
     * @param debtor 债务人（可选）
     * @return 匹配的债权记录列表
     */
    @GetMapping("/search")
    public ResponseEntity<List<DebtSearchResultDTO>> searchConvertibleDebts(
            @RequestParam(required = false) String creditor,
            @RequestParam(required = false) String debtor) {
        
        logger.info("======== 债权转换搜索请求 ========");
        logger.info("债权人: {}", creditor);
        logger.info("债务人: {}", debtor);
        logger.info("================================");
        
        try {
            List<DebtSearchResultDTO> results = debtConversionService.searchConvertibleDebts(creditor, debtor);
            
            logger.info("搜索完成，返回 {} 条记录", results.size());
            
            return ResponseEntity.ok(results);
            
        } catch (Exception e) {
            logger.error("搜索可转换债权记录时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 诉讼转非诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @PostMapping("/litigation-to-non-litigation")
    public ResponseEntity<DebtConversionResponseDTO> convertLitigationToNonLitigation(
            @Valid @RequestBody DebtConversionRequestDTO request) {
        
        logger.info("======== 诉讼转非诉讼请求 ========");
        logger.info("债权人: {}", request.getCreditor());
        logger.info("债务人: {}", request.getDebtor());
        logger.info("期间: {}", request.getPeriod());
        logger.info("转换年月: {}-{}", request.getConversionYear(), request.getConversionMonth());
        logger.info("备注: {}", request.getRemark());
        logger.info("===============================");
        
        try {
            DebtConversionResponseDTO response = debtConversionService.convertLitigationToNonLitigation(request);
            
            if (response.isSuccess()) {
                logger.info("诉讼转非诉讼执行成功: {}", response.getMessage());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("诉讼转非诉讼执行失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("诉讼转非诉讼处理过程中发生错误", e);
            
            DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                "诉讼转非诉讼失败：" + e.getMessage(),
                e.getClass().getSimpleName()
            );
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 非诉讼转诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @PostMapping("/non-litigation-to-litigation")
    public ResponseEntity<DebtConversionResponseDTO> convertNonLitigationToLitigation(
            @Valid @RequestBody DebtConversionRequestDTO request) {
        
        logger.info("======== 非诉讼转诉讼请求 ========");
        logger.info("债权人: {}", request.getCreditor());
        logger.info("债务人: {}", request.getDebtor());
        logger.info("期间: {}", request.getPeriod());
        logger.info("转换年月: {}-{}", request.getConversionYear(), request.getConversionMonth());
        logger.info("诉讼案件: {}", request.getLitigationCase());
        logger.info("诉讼主张本金: {}", request.getLitigationOccurredPrincipal());
        logger.info("诉讼主张利息及罚金: {}", request.getLitigationInterestFee());
        logger.info("诉讼费: {}", request.getLitigationFee());
        logger.info("中介费: {}", request.getIntermediaryFee());
        logger.info("备注: {}", request.getRemark());
        logger.info("===============================");
        
        try {
            // 验证非诉讼转诉讼的必填字段
            if (request.getLitigationCase() == null || request.getLitigationCase().trim().isEmpty()) {
                DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                    "非诉讼转诉讼时诉讼案件名称为必填项",
                    "ValidationError"
                );
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            DebtConversionResponseDTO response = debtConversionService.convertNonLitigationToLitigation(request);
            
            if (response.isSuccess()) {
                logger.info("非诉讼转诉讼执行成功: {}", response.getMessage());
                return ResponseEntity.ok(response);
            } else {
                logger.warn("非诉讼转诉讼执行失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("非诉讼转诉讼处理过程中发生错误", e);
            
            DebtConversionResponseDTO errorResponse = DebtConversionResponseDTO.failure(
                "非诉讼转诉讼失败：" + e.getMessage(),
                e.getClass().getSimpleName()
            );
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}