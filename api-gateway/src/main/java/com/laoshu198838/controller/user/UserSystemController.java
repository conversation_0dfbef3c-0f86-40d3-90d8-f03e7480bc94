package com.laoshu198838.controller.user;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.laoshu198838.model.user.dto.UserRegistrationDTO;
import com.laoshu198838.model.user.dto.UserDTO;
import com.laoshu198838.service.AccountManagementService;

/**
 * 用户系统管理控制器
 * 提供用户系统的管理接口，包括用户管理、角色管理、数据库初始化等
 * 使用新的user_system数据库
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class UserSystemController {

    private static final Logger logger = LoggerFactory.getLogger(UserSystemController.class);

    @Autowired
    private AccountManagementService accountManagementService;




    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        logger.info("获取用户系统状态");

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "用户系统运行正常");
        response.put("version", "1.0.0");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 公开的健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        logger.info("用户系统健康检查");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "user-system");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 用户注册接口
     */
    @PostMapping("/users/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody UserRegistrationDTO registrationDTO) {
        logger.info("用户注册请求: {}", registrationDTO.getUsername());

        Map<String, Object> response = new HashMap<>();

        try {
            // 简单验证
            if (registrationDTO.getUsername() == null || registrationDTO.getUsername().trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                response.put("timestamp", System.currentTimeMillis());
                return ResponseEntity.badRequest().body(response);
            }

            if (registrationDTO.getPassword() == null || registrationDTO.getPassword().trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "密码不能为空");
                response.put("timestamp", System.currentTimeMillis());
                return ResponseEntity.badRequest().body(response);
            }

            // 调用账户管理服务进行注册
            accountManagementService.registerUser(registrationDTO);
            logger.info("用户注册请求处理完成: {}", registrationDTO.getUsername());

            response.put("success", true);
            response.put("message", "用户注册成功，等待管理员审核");
            response.put("username", registrationDTO.getUsername());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("用户注册失败", e);

            response.put("success", false);
            response.put("message", "用户注册失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取所有用户列表（仅管理员可访问）
     */
    @GetMapping("/users")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<UserDTO>> getAllUsers() {
        logger.info("🔥🔥🔥 UserSystemController.getAllUsers() 开始执行");
        logger.info("🔥🔥🔥 accountManagementService实例: {}", accountManagementService.getClass().getName());

        try {
            logger.info("🔥🔥🔥 准备调用 accountManagementService.getAllUsers()");
            List<UserDTO> users = accountManagementService.getAllUsers();
            logger.info("🔥🔥🔥 accountManagementService.getAllUsers() 调用完成，返回 {} 个用户", users.size());
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            logger.error("🔥🔥🔥 UserSystemController.getAllUsers() 执行失败", e);
            throw new RuntimeException("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户状态（仅管理员可访问）
     */
    @PutMapping("/users/{userId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> updateUserStatus(
            @PathVariable Long userId,
            @RequestBody Map<String, String> statusMap) {

        String status = statusMap.get("status");
        logger.info("更新用户系统用户状态: userId={}, status={}", userId, status);

        Map<String, Object> response = new HashMap<>();

        try {
            accountManagementService.updateUserStatus(userId, status);

            response.put("success", true);
            response.put("message", "用户状态更新成功");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);

            response.put("success", false);
            response.put("message", "更新用户状态失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新用户角色（仅管理员可访问）
     */
    @PutMapping("/users/{userId}/roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> updateUserRoles(
            @PathVariable Long userId,
            @RequestBody Map<String, String> roleMap) {

        String role = roleMap.get("role");
        logger.info("更新用户系统用户角色: userId={}, role={}", userId, role);

        Map<String, Object> response = new HashMap<>();

        try {
            accountManagementService.updateUserRoles(userId, role);

            response.put("success", true);
            response.put("message", "用户角色更新成功");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("更新用户角色失败", e);

            response.put("success", false);
            response.put("message", "更新用户角色失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除用户（仅管理员可访问）
     */
    @DeleteMapping("/users/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> deleteUser(@PathVariable Long userId) {
        logger.info("删除用户系统用户: userId={}", userId);

        Map<String, Object> response = new HashMap<>();

        try {
            accountManagementService.deleteUser(userId);

            response.put("success", true);
            response.put("message", "用户删除成功");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("删除用户失败", e);

            response.put("success", false);
            response.put("message", "删除用户失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.badRequest().body(response);
        }
    }
}
