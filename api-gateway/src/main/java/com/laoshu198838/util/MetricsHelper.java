package com.laoshu198838.util;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;

/**
 * 监控指标辅助工具类
 * 提供安全的指标访问方法，避免空指针异常
 */
public class MetricsHelper {
    
    /**
     * 安全获取计数器的值
     */
    public static double safeCount(Counter counter) {
        return counter != null ? counter.count() : 0.0;
    }
    
    /**
     * 安全获取计时器的计数
     */
    public static long safeCount(Timer timer) {
        return timer != null ? timer.count() : 0L;
    }
    
    /**
     * 计算成功率
     */
    public static double calculateSuccessRate(double total, double failures) {
        if (total <= 0) return 0.0;
        return ((total - failures) / total) * 100.0;
    }
    
    /**
     * 计算缓存命中率
     */
    public static double calculateHitRate(double hits, double misses) {
        double total = hits + misses;
        return total > 0 ? (hits / total) * 100.0 : 0.0;
    }
}