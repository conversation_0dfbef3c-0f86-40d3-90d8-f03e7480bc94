package com.laoshu198838.regression;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 功能保护测试 - 确保现有功能不被意外修改
 * 
 * 设计原则：
 * 1. 轻量级 - 只测试关键功能的响应结构
 * 2. 快速 - 5分钟内完成所有测试
 * 3. 易维护 - 使用简单的断言，不需要复杂的快照管理
 * 
 * <AUTHOR> Developer
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class FunctionProtectionTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 测试1：保护登录功能
     * 验证登录接口的响应结构保持稳定
     */
    @Test
    public void protectLoginFunctionality() {
        // 准备测试数据
        Map<String, String> loginRequest = Map.of(
            "username", "testuser",
            "password", "testpass123"
        );
        
        // 发送请求
        ResponseEntity<Map> response = restTemplate.postForEntity(
            "/api/auth/login", 
            loginRequest, 
            Map.class
        );
        
        // 验证响应状态
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应结构
        Map<String, Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body).containsKeys("code", "message", "data");
        
        // 验证code为200（成功）或具体的错误码
        Integer code = (Integer) body.get("code");
        assertThat(code).isNotNull();
        
        // 如果登录成功，验证data结构
        if (code == 200) {
            Map<String, Object> data = (Map<String, Object>) body.get("data");
            assertThat(data).containsKeys("token", "user");
            
            // 验证user对象结构
            Map<String, Object> user = (Map<String, Object>) data.get("user");
            assertThat(user).containsKeys("id", "username", "name");
        }
    }
    
    /**
     * 测试2：保护债权查询功能
     * 验证列表查询接口的分页结构
     */
    @Test
    public void protectDebtQueryFunctionality() {
        // 发送查询请求
        ResponseEntity<Map> response = restTemplate.getForEntity(
            "/api/overdue-debt/list?page=0&size=10", 
            Map.class
        );
        
        // 验证响应
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        Map<String, Object> body = response.getBody();
        assertThat(body).containsKeys("code", "data");
        
        // 验证分页数据结构
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        if (data != null) {
            assertThat(data).containsKeys("content", "totalElements", "totalPages", "size", "number");
            
            // 验证content是数组
            assertThat(data.get("content")).isInstanceOf(List.class);
        }
    }
    
    /**
     * 测试3：保护数据导出功能
     * 验证导出接口返回正确的文件格式
     */
    @Test
    public void protectExportFunctionality() {
        // 测试导出元数据接口
        ResponseEntity<Map> response = restTemplate.getForEntity(
            "/api/excel/export/metadata", 
            Map.class
        );
        
        // 验证响应
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        Map<String, Object> body = response.getBody();
        assertThat(body).containsKeys("code", "data");
        
        // 验证导出配置结构
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        if (data != null) {
            assertThat(data).containsKey("exportTypes");
        }
    }
    
    /**
     * 测试4：保护健康检查功能
     * 验证系统健康检查端点
     */
    @Test
    public void protectHealthCheckFunctionality() {
        // 发送健康检查请求
        ResponseEntity<Map> response = restTemplate.getForEntity(
            "/actuator/health", 
            Map.class
        );
        
        // 验证响应
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        Map<String, Object> body = response.getBody();
        assertThat(body).containsKey("status");
        
        // 验证状态值
        String status = (String) body.get("status");
        assertThat(status).isIn("UP", "DOWN", "UNKNOWN");
        
        // 如果有组件信息，验证结构
        if (body.containsKey("components")) {
            Map<String, Object> components = (Map<String, Object>) body.get("components");
            // 至少应该有数据库和磁盘空间检查
            assertThat(components).containsKeys("db", "diskSpace");
        }
    }
    
    /**
     * 测试5：保护用户权限查询功能
     * 验证权限接口的响应结构
     */
    @Test
    public void protectPermissionFunctionality() {
        // 查询用户权限
        ResponseEntity<Map> response = restTemplate.getForEntity(
            "/api/permissions/menu", 
            Map.class
        );
        
        // 验证响应
        assertThat(response.getStatusCode()).isIn(HttpStatus.OK, HttpStatus.UNAUTHORIZED);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> body = response.getBody();
            assertThat(body).containsKeys("code", "data");
            
            // 验证权限数据是数组
            Object data = body.get("data");
            if (data != null) {
                assertThat(data).isInstanceOf(List.class);
            }
        }
    }
    
    /**
     * 测试6：保护数据一致性检查功能
     * 验证一致性检查接口
     */
    @Test
    public void protectDataConsistencyCheck() {
        // 运行一致性检查
        ResponseEntity<Map> response = restTemplate.getForEntity(
            "/api/monitoring/consistency/check", 
            Map.class
        );
        
        // 验证响应
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        Map<String, Object> body = response.getBody();
        assertThat(body).containsKeys("code", "data");
        
        // 验证检查结果结构
        Map<String, Object> data = (Map<String, Object>) body.get("data");
        if (data != null) {
            assertThat(data).containsKeys("checkTime", "status", "details");
        }
    }
    
    /**
     * 工具方法：保存响应快照（可选功能）
     * 如果需要更严格的结构检查，可以启用此功能
     */
    private void saveSnapshot(String name, Object response) {
        try {
            Path snapshotDir = Paths.get("src/test/resources/snapshots");
            Files.createDirectories(snapshotDir);
            
            Path snapshotFile = snapshotDir.resolve(name + ".json");
            String json = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(response);
            
            Files.writeString(snapshotFile, json);
            System.out.println("快照已保存: " + snapshotFile);
        } catch (Exception e) {
            // 快照保存失败不影响测试
            System.err.println("保存快照失败: " + e.getMessage());
        }
    }
}