#!/bin/bash
# 生成简单的测试报告

echo "📊 测试报告"
echo "生成时间: $(date)"
echo "========================================"

# Java测试
echo ""
echo "🔹 后端测试结果:"
mvn test -q 2>&1 | grep -E "(Tests run:|ERROR|SUCCESS)" | tail -10

# 前端测试
echo ""
echo "🔹 前端测试结果:"
cd FinancialSystem-web
npm test -- --watchAll=false 2>&1 | grep -E "(Test Suites:|Tests:|PASS|FAIL)" | head -5
cd ..

echo ""
echo "========================================"
echo "报告生成完成"
