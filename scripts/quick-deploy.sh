#!/bin/bash
# quick-deploy.sh - 一键部署脚本（个人开发者版）
# 用于快速部署FinancialSystem到Linux服务器

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 部署配置（根据实际情况修改）
DEPLOY_USER="deploy"
DEPLOY_HOST="your-server.com"
DEPLOY_PATH="/opt/financial-system"
BACKUP_PATH="/opt/backups"
SERVICE_NAME="financial-system"

# 环境类型
ENV_TYPE=${1:-"production"}  # 默认生产环境

# 打印带颜色的消息
print_status() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GRE<PERSON>}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 检查环境
check_environment() {
    print_status "检查部署环境..."
    
    # 检查必要的工具
    command -v git >/dev/null 2>&1 || { print_error "需要安装git"; exit 1; }
    command -v mvn >/dev/null 2>&1 || { print_error "需要安装maven"; exit 1; }
    command -v npm >/dev/null 2>&1 || { print_error "需要安装npm"; exit 1; }
    
    # 检查当前分支
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    print_status "当前分支: $CURRENT_BRANCH"
    
    # 检查是否有未提交的更改
    if [[ -n $(git status -s) ]]; then
        print_warning "检测到未提交的更改"
        read -p "是否要先提交更改？(y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git add .
            git commit -m "chore: 部署前自动提交 - $(date +'%Y-%m-%d %H:%M:%S')"
        fi
    fi
    
    print_success "环境检查通过"
}

# 运行测试
run_tests() {
    print_status "运行测试套件..."
    
    # 后端测试
    print_status "运行后端测试..."
    mvn test || { print_error "后端测试失败"; exit 1; }
    
    # 前端测试
    print_status "运行前端测试..."
    cd FinancialSystem-web
    npm test -- --watchAll=false || { print_error "前端测试失败"; exit 1; }
    cd ..
    
    print_success "所有测试通过"
}

# 构建项目
build_project() {
    print_status "开始构建项目..."
    
    # 构建后端
    print_status "构建后端..."
    mvn clean package -DskipTests || { print_error "后端构建失败"; exit 1; }
    
    # 构建前端
    print_status "构建前端..."
    cd FinancialSystem-web
    npm run build || { print_error "前端构建失败"; exit 1; }
    cd ..
    
    print_success "项目构建成功"
}

# 创建版本标签
create_version_tag() {
    print_status "创建版本标签..."
    
    # 获取当前版本
    VERSION=$(date +'v%Y.%m.%d-%H%M')
    
    # 创建git标签
    git tag -a "$VERSION" -m "部署版本 $VERSION"
    git push origin "$VERSION"
    
    print_success "版本标签创建成功: $VERSION"
    echo "$VERSION" > .last-deploy-version
}

# 备份服务器
backup_server() {
    print_status "备份服务器当前版本..."
    
    ssh "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        if [ -d "$DEPLOY_PATH" ]; then
            BACKUP_NAME="backup-\$(date +'%Y%m%d-%H%M%S')"
            mkdir -p "$BACKUP_PATH"
            cp -r "$DEPLOY_PATH" "$BACKUP_PATH/\$BACKUP_NAME"
            echo "✅ 备份完成: \$BACKUP_NAME"
            
            # 只保留最近5个备份
            cd "$BACKUP_PATH"
            ls -t | tail -n +6 | xargs -r rm -rf
        fi
EOF
    
    print_success "服务器备份完成"
}

# 部署到服务器
deploy_to_server() {
    print_status "开始部署到服务器..."
    
    # 停止服务
    print_status "停止服务..."
    ssh "$DEPLOY_USER@$DEPLOY_HOST" "sudo systemctl stop $SERVICE_NAME" || true
    
    # 上传文件
    print_status "上传文件..."
    rsync -avz --delete \
        --exclude='.git' \
        --exclude='node_modules' \
        --exclude='target' \
        --exclude='*.log' \
        --exclude='.env.local' \
        ./ "$DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PATH/"
    
    # 在服务器上执行部署脚本
    ssh "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        cd $DEPLOY_PATH
        
        # 设置环境变量
        export SPRING_PROFILES_ACTIVE=$ENV_TYPE
        
        # 安装依赖
        cd FinancialSystem-web
        npm install --production
        cd ..
        
        # 启动服务
        sudo systemctl start $SERVICE_NAME
        
        # 等待服务启动
        sleep 10
EOF
    
    print_success "部署完成"
}

# 健康检查
health_check() {
    print_status "执行健康检查..."
    
    # 检查服务状态
    ssh "$DEPLOY_USER@$DEPLOY_HOST" "sudo systemctl is-active $SERVICE_NAME" || {
        print_error "服务未正常启动"
        return 1
    }
    
    # 检查API响应
    HEALTH_URL="http://$DEPLOY_HOST:8080/api/health"
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        print_success "健康检查通过"
        return 0
    else
        print_error "健康检查失败 (HTTP $HTTP_STATUS)"
        return 1
    fi
}

# 回滚
rollback() {
    print_status "执行回滚..."
    
    ssh "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        # 获取最新的备份
        LATEST_BACKUP=\$(ls -t "$BACKUP_PATH" | head -1)
        
        if [ -z "\$LATEST_BACKUP" ]; then
            echo "❌ 没有找到备份"
            exit 1
        fi
        
        # 停止服务
        sudo systemctl stop $SERVICE_NAME
        
        # 恢复备份
        rm -rf "$DEPLOY_PATH"
        cp -r "$BACKUP_PATH/\$LATEST_BACKUP" "$DEPLOY_PATH"
        
        # 启动服务
        sudo systemctl start $SERVICE_NAME
        
        echo "✅ 回滚到: \$LATEST_BACKUP"
EOF
    
    print_success "回滚完成"
}

# 主函数
main() {
    case "$ENV_TYPE" in
        "test")
            print_status "=== 部署到测试环境 ==="
            DEPLOY_HOST="test-server.com"
            ;;
        "production")
            print_status "=== 部署到生产环境 ==="
            
            # 生产环境需要确认
            print_warning "即将部署到生产环境！"
            read -p "确认部署到生产环境？(yes/no) " -r
            if [[ ! $REPLY =~ ^yes$ ]]; then
                print_error "部署已取消"
                exit 1
            fi
            ;;
        "rollback")
            print_status "=== 执行回滚操作 ==="
            rollback
            exit 0
            ;;
        *)
            print_error "未知的环境类型: $ENV_TYPE"
            echo "使用方法: $0 [test|production|rollback]"
            exit 1
            ;;
    esac
    
    # 执行部署流程
    check_environment
    run_tests
    build_project
    create_version_tag
    backup_server
    deploy_to_server
    
    # 健康检查
    if health_check; then
        print_success "🎉 部署成功完成！"
        print_status "版本: $(cat .last-deploy-version)"
        print_status "环境: $ENV_TYPE"
        print_status "时间: $(date)"
    else
        print_error "部署后健康检查失败"
        print_warning "建议执行回滚: $0 rollback"
        exit 1
    fi
}

# 执行主函数
main