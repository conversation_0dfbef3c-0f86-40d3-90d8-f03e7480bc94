#!/bin/bash

# AI检查点管理脚本
# 使用方法: 
# ./scripts/ai-checkpoint.sh create "完成xxx模块"
# ./scripts/ai-checkpoint.sh list
# ./scripts/ai-checkpoint.sh rollback [步数]

ACTION=${1:-"create"}
DESCRIPTION=${2:-"未命名检查点"}

case $ACTION in
    "create"|"c")
        echo "📸 创建检查点: $DESCRIPTION"
        
        # 检查是否有更改
        if git diff-index --quiet HEAD --; then
            echo "⚠️  没有检测到代码更改"
            exit 1
        fi
        
        # 添加所有更改
        git add .
        
        # 创建检查点提交
        CHECKPOINT_MSG="checkpoint: $DESCRIPTION - $(date '+%Y-%m-%d %H:%M:%S')"
        git commit -m "$CHECKPOINT_MSG"
        
        # 可选：运行快速测试
        if [[ -f "scripts/test-startup.sh" && "$3" == "--test" ]]; then
            echo "🧪 运行快速验证..."
            if ./scripts/test-startup.sh > "/tmp/checkpoint-test-$(date +%s).log" 2>&1; then
                echo "✅ 检查点测试通过"
            else
                echo "❌ 检查点测试失败，但检查点已创建"
            fi
        fi
        
        echo "✅ 检查点已创建: $(git rev-parse --short HEAD)"
        ;;
        
    "list"|"l")
        echo "📋 AI任务检查点列表:"
        echo ""
        git log --oneline --grep="checkpoint:" --grep="snapshot:" -10
        ;;
        
    "rollback"|"r")
        STEPS=${2:-1}
        echo "🔙 回滚 $STEPS 个检查点..."
        
        # 显示即将回滚到的提交
        TARGET_COMMIT=$(git rev-parse HEAD~$STEPS)
        TARGET_MSG=$(git log -1 --pretty=format:"%s" $TARGET_COMMIT)
        echo "目标提交: $(git rev-parse --short $TARGET_COMMIT) - $TARGET_MSG"
        
        read -p "确认回滚? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git reset --hard HEAD~$STEPS
            echo "✅ 已回滚到: $(git rev-parse --short HEAD)"
            echo "当前状态: $(git log -1 --pretty=format:'%s')"
        else
            echo "❌ 取消回滚"
        fi
        ;;
        
    "diff"|"d")
        STEPS=${2:-1}
        echo "📊 检查点对比 (当前 vs $STEPS 步前):"
        git diff HEAD~$STEPS --stat
        echo ""
        echo "详细差异:"
        git diff HEAD~$STEPS --name-only
        ;;
        
    "info"|"i")
        echo "📋 当前AI任务信息:"
        if [[ -f ".ai-task-info.md" ]]; then
            cat ".ai-task-info.md"
        else
            echo "❌ 未找到任务信息文件"
        fi
        ;;
        
    *)
        echo "🚀 AI检查点管理工具"
        echo ""
        echo "用法:"
        echo "  ./scripts/ai-checkpoint.sh create \"描述\" [--test]    # 创建检查点"
        echo "  ./scripts/ai-checkpoint.sh list                      # 列出检查点"
        echo "  ./scripts/ai-checkpoint.sh rollback [步数]           # 回滚检查点"
        echo "  ./scripts/ai-checkpoint.sh diff [步数]               # 对比差异"
        echo "  ./scripts/ai-checkpoint.sh info                     # 显示任务信息"
        echo ""
        echo "简写:"
        echo "  c, l, r, d, i"
        echo ""
        echo "示例:"
        echo "  ./scripts/ai-checkpoint.sh c \"完成用户认证模块\""
        echo "  ./scripts/ai-checkpoint.sh r 2  # 回滚2步"
        ;;
esac