#!/bin/bash

# AI任务自动启动脚本
# 使用方法: ./scripts/ai-task-start.sh [任务类型] [任务描述]
# 示例: ./scripts/ai-task-start.sh fix "debt-export-empty-table"

set -e

TASK_TYPE=${1:-"task"}  # fix/add/refactor/optimize
TASK_DESC=${2:-"unnamed"}
DATE=$(date +%Y%m%d)
BRANCH_NAME="feature/ai-${TASK_TYPE}-${TASK_DESC}-${DATE}"

echo "🚀 开始AI辅助开发任务..."
echo "任务类型: $TASK_TYPE"
echo "任务描述: $TASK_DESC"
echo "分支名称: $BRANCH_NAME"
echo ""

# 检查工作区状态
if ! git diff-index --quiet HEAD --; then
    echo "⚠️  工作区有未提交的更改，请先处理："
    git status --porcelain
    echo ""
    read -p "是否要暂存当前更改? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git stash push -m "自动暂存: 开始AI任务前 $(date)"
        echo "✅ 已暂存当前更改"
    else
        echo "❌ 请先处理未提交的更改"
        exit 1
    fi
fi

# 更新主分支（如果需要）
echo "🔄 确保主分支是最新的..."
CURRENT_BRANCH=$(git branch --show-current)
if [[ "$CURRENT_BRANCH" != "main" && "$CURRENT_BRANCH" != "master" ]]; then
    git checkout main 2>/dev/null || git checkout master 2>/dev/null || {
        echo "⚠️  无法确定主分支，继续使用当前分支 $CURRENT_BRANCH"
    }
fi

# 创建AI任务分支
echo "🌿 创建AI任务分支: $BRANCH_NAME"
git checkout -b "$BRANCH_NAME"

# 创建安全备份
echo "💾 创建安全备份..."
if [[ -f "scripts/ai-safe-backup.sh" ]]; then
    ./scripts/ai-safe-backup.sh "$TASK_TYPE-$TASK_DESC"
else
    echo "⚠️  备份脚本不存在，建议先创建 scripts/ai-safe-backup.sh"
fi

# 创建初始快照（这就是你问的自动化部分）
echo "📸 创建初始快照..."
# 确保有内容可以提交（即使没有更改）
touch ".ai-task-marker-$DATE"
git add ".ai-task-marker-$DATE"
git commit -m "snapshot: 开始AI任务【${TASK_TYPE}】${TASK_DESC} - $(date '+%Y-%m-%d %H:%M:%S')

任务类型: $TASK_TYPE
任务描述: $TASK_DESC  
分支名称: $BRANCH_NAME
创建时间: $(date)
基于提交: $(git rev-parse HEAD~1)"

# 运行基线测试
echo "🧪 运行基线测试..."
if [[ -f "scripts/test-startup.sh" ]]; then
    echo "运行启动测试以确保当前状态正常..."
    if ./scripts/test-startup.sh > "/tmp/ai-baseline-test-${DATE}.log" 2>&1; then
        echo "✅ 基线测试通过"
    else
        echo "❌ 基线测试失败，请检查日志: /tmp/ai-baseline-test-${DATE}.log"
        echo "建议解决问题后再开始AI任务"
    fi
else
    echo "⚠️  测试脚本不存在，建议先创建 scripts/test-startup.sh"
fi

# 创建任务信息文件
cat > ".ai-task-info.md" << EOF
# AI任务信息

- **任务类型**: $TASK_TYPE
- **任务描述**: $TASK_DESC
- **分支名称**: $BRANCH_NAME
- **开始时间**: $(date)
- **基于提交**: $(git rev-parse HEAD~1)

## 任务检查清单

- [ ] AI分析现有代码结构
- [ ] 制定详细实施方案
- [ ] 分步骤实现功能
- [ ] 每步完成后立即测试
- [ ] 运行完整测试套件
- [ ] 代码审查和清理
- [ ] 合并到主分支

## 回滚命令

如果需要回退到任务开始前的状态：
\`\`\`bash
git reset --hard HEAD~1
\`\`\`

如果需要回退到特定的快照：
\`\`\`bash  
git log --oneline  # 查看提交历史
git reset --hard <commit-id>
\`\`\`
EOF

git add ".ai-task-info.md"
git commit -m "docs: 添加AI任务信息和检查清单"

echo ""
echo "🎉 AI任务环境准备完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 使用标准化AI指令开始任务"
echo "2. 要求AI先分析现有代码，不要直接修改"
echo "3. 分步骤实施，每步完成后运行测试"
echo "4. 使用以下命令创建检查点："
echo "   git add . && git commit -m 'checkpoint: [描述]'"
echo ""
echo "📁 任务信息已保存到: .ai-task-info.md"
echo "🔙 回滚命令: git reset --hard HEAD~2"
echo "📊 基线测试日志: /tmp/ai-baseline-test-${DATE}.log"
echo ""
echo "🚀 准备就绪，可以开始AI协作了！"