#!/bin/bash
# 每日健康检查脚本

echo "🏥 每日健康检查 - $(date)"
echo "=============================="

# 1. 检查应用是否运行
echo ""
echo "1️⃣ 应用状态检查:"
if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo "✅ 应用运行正常"
    curl -s http://localhost:8080/actuator/health | grep -o '"status":"[^"]*"' || echo "状态信息获取失败"
else
    echo "❌ 应用未运行或无法访问"
fi

# 2. 检查数据库连接
echo ""
echo "2️⃣ 数据库检查:"
if mysql -h localhost -u root -p123456 -e "SELECT 1" > /dev/null 2>&1; then
    echo "✅ 数据库连接正常"
    mysql -h localhost -u root -p123456 -e "SELECT COUNT(*) as '债权记录数' FROM overdue_debt_db.overdue_debt_add;" 2>/dev/null || echo "查询失败"
else
    echo "❌ 数据库连接失败"
fi

# 3. 检查磁盘空间
echo ""
echo "3️⃣ 磁盘空间:"
df -h | grep -E "(Filesystem|/$|/Volumes)" | awk '{print $5 "\t" $6}' | column -t

# 4. 检查最近错误
echo ""
echo "4️⃣ 最近的错误日志:"
if [ -f "logs/application.log" ]; then
    grep -i error logs/application.log | tail -3 || echo "没有发现错误"
else
    echo "日志文件未找到"
fi

echo ""
echo "=============================="
echo "检查完成！"
