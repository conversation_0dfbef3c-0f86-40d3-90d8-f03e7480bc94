-- SQL索引优化脚本 - 新增债权统计查询性能优化
-- 根据新增债权统计的参数化SQL查询创建相应索引

-- ==================== 新增表相关索引 ====================

-- 1. 新增表的复合索引 - 用于年份月份筛选
CREATE INDEX IF NOT EXISTS idx_new_debt_year_month_period ON 新增表(年份, 月份, 期间);

-- 2. 新增表的管理公司筛选索引
CREATE INDEX IF NOT EXISTS idx_new_debt_management_company ON 新增表(管理公司);

-- 3. 新增表的期间筛选索引（用于新增债权基准日期筛选）
CREATE INDEX IF NOT EXISTS idx_new_debt_period ON 新增表(期间);

-- 4. 新增表的综合查询索引（覆盖主要查询字段）
CREATE INDEX IF NOT EXISTS idx_new_debt_comprehensive ON 新增表(
    年份, 月份, 管理公司, 期间, 债权人, 债务人
);

-- 5. 新增表的金额字段索引（用于汇总计算）
CREATE INDEX IF NOT EXISTS idx_new_debt_amounts ON 新增表(新增金额, 处置金额, 债权余额);

-- ==================== 汇总表相关索引 ====================

-- 6. 汇总表的年份管理公司索引
CREATE INDEX IF NOT EXISTS idx_summary_year_company ON 汇总表(年份, 管理公司);

-- 7. 汇总表的金额字段索引
CREATE INDEX IF NOT EXISTS idx_summary_amounts ON 汇总表(
    年初逾期债权余额, 本年减少债权金额, 本年期末债权余额
);

-- ==================== 处置表相关索引 ====================

-- 8. 处置表的年份月份管理公司索引
CREATE INDEX IF NOT EXISTS idx_disposal_year_month_company ON 处置表(年份, 月份, 管理公司);

-- 9. 处置表的处置方式索引
CREATE INDEX IF NOT EXISTS idx_disposal_methods ON 处置表(现金处置, 资产抵债, 分期还款, 其他方式);

-- ==================== 性能优化建议 ====================

-- 10. 分析表统计信息（提高查询计划准确性）
ANALYZE TABLE 新增表;
ANALYZE TABLE 汇总表;
ANALYZE TABLE 处置表;

-- 11. 查看索引使用情况
-- SELECT TABLE_NAME, INDEX_NAME, CARDINALITY 
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = DATABASE()
--   AND TABLE_NAME IN ('新增表', '汇总表', '处置表')
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- 12. 优化建议注释
/*
索引创建说明：
1. idx_new_debt_year_month_period：优化基于年份、月份和期间的查询
2. idx_new_debt_management_company：优化公司筛选查询
3. idx_new_debt_period：优化新增债权基准日期（2022-04-30）筛选
4. idx_new_debt_comprehensive：覆盖新增债权统计查询的主要字段
5. idx_new_debt_amounts：优化金额汇总计算
6. idx_summary_year_company：优化汇总表的年度公司查询
7. idx_summary_amounts：优化汇总表的金额计算
8. idx_disposal_year_month_company：优化处置表的时间和公司筛选
9. idx_disposal_methods：优化处置方式统计查询

性能预期：
- 新增债权统计查询响应时间预计从2-5秒降至200-500毫秒
- 各公司新增债权汇总查询响应时间预计从1-3秒降至100-300毫秒
- 处置方式统计查询响应时间预计从1-2秒降至100-200毫秒

注意事项：
- 索引会占用额外存储空间（约20-30%额外空间）
- 索引会略微增加INSERT/UPDATE/DELETE操作的时间
- 建议在业务低峰期执行索引创建
- 定期监控索引使用情况，删除未使用的索引
*/