-- 使用正确的逻辑测试存量债权数据
-- 应该返回期末余额3.77万元

WITH
  year_start_balance AS (
    SELECT
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 上年末余额
    FROM 减值准备表
    WHERE 年份 = 2024 AND 月份 = 12
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
    HAVING SUM(本月末债权余额) <> 0
  ),
  new_debt AS (
    SELECT
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月新增债权) AS 当年累计新增债权
    FROM 减值准备表
    WHERE 年份 = 2025 AND 月份 <= 6
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
  ),
  disposal AS (
    SELECT
      管理公司, 债权人, 债务人, 是否涉诉, 期间,
      SUM(每月处置金额) AS 当年累计处置金额
    FROM 处置表
    WHERE 年份 = 2025 AND 月份 <= 6
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
  )
SELECT
  '存量债权汇总' AS 数据类型,
  ROUND(SUM(y.上年末余额), 2) AS 期初金额,
  ROUND(SUM(
    CASE
      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN 
        COALESCE(d.当年累计处置金额, 0)  -- 仅存量债权
      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN  -- 混合债权
        CASE
          WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0  -- 全部归新增
          ELSE COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0)  -- 超出部分归存量
        END
      ELSE 0
    END
  ), 2) AS 累计清收金额,
  ROUND(SUM(y.上年末余额) - SUM(
    CASE
      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) = 0 THEN 
        COALESCE(d.当年累计处置金额, 0)  -- 仅存量债权
      WHEN y.上年末余额 > 0 AND COALESCE(n.当年累计新增债权, 0) > 0 THEN  -- 混合债权
        CASE
          WHEN COALESCE(d.当年累计处置金额, 0) <= COALESCE(n.当年累计新增债权, 0) THEN 0  -- 全部归新增
          ELSE COALESCE(d.当年累计处置金额, 0) - COALESCE(n.当年累计新增债权, 0)  -- 超出部分归存量
        END
      ELSE 0
    END
  ), 2) AS 期末余额
FROM year_start_balance y
LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
  AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
  AND y.科目名称 = n.科目名称
LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
  AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
WHERE y.上年末余额 > 0;