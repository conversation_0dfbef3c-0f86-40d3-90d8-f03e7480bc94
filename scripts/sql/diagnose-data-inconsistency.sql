-- 诊断数据不一致问题的SQL脚本
-- 问题：API返回37488.84万元，但用户直接SQL查询返回3.77万元

-- 1. 查看存量债权的基础数据（2024年末余额）
SELECT 
  '2024年末存量债权基础数据' AS 查询说明,
  COUNT(*) AS 记录数,
  SUM(本月末债权余额) AS 总金额
FROM 减值准备表
WHERE 年份 = 2024 AND 月份 = 12;

-- 2. 查看2025年新增债权情况
SELECT 
  '2025年新增债权情况' AS 查询说明,
  COUNT(DISTINCT CONCAT(管理公司, '|', 债权人, '|', 债务人)) AS 记录数,
  SUM(本月末债权余额) AS 总金额
FROM 减值准备表
WHERE 年份 = 2025 AND 月份 BETWEEN 1 AND 6
  AND (管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间) NOT IN (
    SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
    FROM 减值准备表
    WHERE 年份 = 2024 AND 月份 = 12
  );

-- 3. 用户提供的SQL（期望返回3.77万元）
WITH 
  year_start_balance AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 上年末余额
    FROM 减值准备表
    WHERE 年份 = 2024 AND 月份 = 12
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
    HAVING SUM(本月末债权余额) <> 0
  ),
  new_debt AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 当年新增债权
    FROM 减值准备表
    WHERE 年份 = 2025 AND 月份 BETWEEN 1 AND 6
      AND (管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间) NOT IN (
        SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
        FROM 减值准备表
        WHERE 年份 = 2024 AND 月份 = 12
      )
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
  ),
  disposal AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 期间,
      SUM(每月处置金额) AS 累计处置金额,
      SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额
    FROM 处置表
    WHERE 年份 = 2025 AND 月份 <= 6
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
  )
SELECT 
  '用户SQL-仅纯存量债权' AS 查询说明,
  COUNT(DISTINCT CONCAT(y.管理公司, '|', y.债权人, '|', y.债务人)) AS 记录数,
  ROUND(COALESCE(SUM(y.上年末余额), 0), 2) AS 期初金额,
  ROUND(COALESCE(SUM(
    CASE 
      WHEN COALESCE(n.当年新增债权, 0) = 0 THEN d.累计处置金额
      ELSE d.累计处置金额 * y.上年末余额 / (y.上年末余额 + n.当年新增债权)
    END
  ), 0), 2) AS 累计清收金额,
  ROUND(COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(
    CASE 
      WHEN COALESCE(n.当年新增债权, 0) = 0 THEN d.累计处置金额
      ELSE d.累计处置金额 * y.上年末余额 / (y.上年末余额 + n.当年新增债权)
    END
  ), 0), 2) AS 期末余额
FROM year_start_balance y
LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
  AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
  AND y.科目名称 = n.科目名称
LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
  AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
WHERE y.上年末余额 > 0 AND COALESCE(n.当年新增债权, 0) = 0;

-- 4. 当前系统实现的SQL（返回37488.84万元）
WITH 
  year_start_balance AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 上年末余额
    FROM 减值准备表
    WHERE 年份 = 2024 AND 月份 = 12
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
    HAVING SUM(本月末债权余额) <> 0
  ),
  new_debt AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 当年新增债权
    FROM 减值准备表
    WHERE 年份 = 2025 AND 月份 BETWEEN 1 AND 6
      AND (管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间) NOT IN (
        SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
        FROM 减值准备表
        WHERE 年份 = 2024 AND 月份 = 12
      )
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
  ),
  disposal AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 期间,
      SUM(每月处置金额) AS 累计处置金额,
      SUM(CASE WHEN 月份 = 6 THEN 每月处置金额 ELSE 0 END) AS 当月处置金额
    FROM 处置表
    WHERE 年份 = 2025 AND 月份 <= 6
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
  )
SELECT 
  '当前系统-所有存量债权' AS 查询说明,
  COUNT(DISTINCT CONCAT(y.管理公司, '|', y.债权人, '|', y.债务人)) AS 记录数,
  ROUND(COALESCE(SUM(y.上年末余额), 0), 2) AS 期初金额,
  ROUND(COALESCE(SUM(
    CASE 
      WHEN COALESCE(n.当年新增债权, 0) = 0 THEN d.累计处置金额
      ELSE d.累计处置金额 * y.上年末余额 / (y.上年末余额 + n.当年新增债权)
    END
  ), 0), 2) AS 累计清收金额,
  ROUND(COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(
    CASE 
      WHEN COALESCE(n.当年新增债权, 0) = 0 THEN d.累计处置金额
      ELSE d.累计处置金额 * y.上年末余额 / (y.上年末余额 + n.当年新增债权)
    END
  ), 0), 2) AS 期末余额
FROM year_start_balance y
LEFT JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
  AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
  AND y.科目名称 = n.科目名称
LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
  AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
WHERE y.上年末余额 > 0;

-- 5. 关键差异分析：有新增债权的存量债权记录
WITH 
  year_start_balance AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 上年末余额
    FROM 减值准备表
    WHERE 年份 = 2024 AND 月份 = 12
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
    HAVING SUM(本月末债权余额) <> 0
  ),
  new_debt AS (
    SELECT 
      管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
      SUM(本月末债权余额) AS 当年新增债权
    FROM 减值准备表
    WHERE 年份 = 2025 AND 月份 BETWEEN 1 AND 6
      AND (管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间) NOT IN (
        SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
        FROM 减值准备表
        WHERE 年份 = 2024 AND 月份 = 12
      )
    GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
  )
SELECT 
  '既有存量又有新增的债权' AS 查询说明,
  COUNT(DISTINCT CONCAT(y.管理公司, '|', y.债权人, '|', y.债务人)) AS 记录数,
  ROUND(SUM(y.上年末余额), 2) AS 存量金额,
  ROUND(SUM(n.当年新增债权), 2) AS 新增金额,
  ROUND(SUM(y.上年末余额 + n.当年新增债权), 2) AS 合计金额
FROM year_start_balance y
INNER JOIN new_debt n ON y.管理公司 = n.管理公司 AND y.债权人 = n.债权人 
  AND y.债务人 = n.债务人 AND y.是否涉诉 = n.是否涉诉 AND y.期间 = n.期间
  AND y.科目名称 = n.科目名称
WHERE y.上年末余额 > 0;