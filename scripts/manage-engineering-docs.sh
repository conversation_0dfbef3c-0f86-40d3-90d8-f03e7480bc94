#!/bin/bash

# 工程文档管理脚本
# Engineering Document Management Script
# 用于自动化管理需求、设计和计划文档的放置和状态流转

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 基础路径
DOCS_BASE="docs/engineering"
RULES_FILE="$DOCS_BASE/document-rules.yaml"

# 函数：显示使用帮助
show_help() {
    cat << EOF
工程文档管理工具 | Engineering Document Management Tool

使用方法 | Usage:
    $0 <command> [options]

命令 | Commands:
    create <type> <feature>     创建新文档 | Create new document
        type: requirements|designs|plans
        feature: 功能名称 (如: 批量导入)
        
    status <file> <new_status>  更新文档状态 | Update document status
        file: 文档文件名
        new_status: active|completed
        
    list [type] [status]        列出文档 | List documents
        type: requirements|designs|plans (可选)
        status: active|completed (可选)
        
    check <file>               检查文档状态 | Check document status
    
    workflow <file>            执行工作流流转 | Execute workflow transition
    
示例 | Examples:
    $0 create requirements "批量导入"
    $0 status REQ-20250105-批量导入.md completed
    $0 list requirements active
    $0 workflow REQ-20250105-批量导入.md

EOF
}

# 函数：获取当前日期（YYYYMMDD格式）
get_date() {
    date +"%Y%m%d"
}

# 函数：获取当前时间戳
get_timestamp() {
    date +"%Y-%m-%d %H:%M:%S"
}

# 函数：根据类型获取前缀
get_prefix() {
    local type=$1
    case $type in
        requirements) echo "REQ";;
        designs) echo "TD";;
        plans) echo "PLAN";;
        *) echo "UNKNOWN";;
    esac
}

# 函数：根据类型获取智能体名称
get_agent() {
    local type=$1
    case $type in
        requirements) echo "requirements-analyst";;
        designs) echo "technical-design-agent";;
        plans) echo "implementation-planner";;
        *) echo "unknown";;
    esac
}

# 函数：创建文档元数据
create_metadata() {
    local status=$1
    local agent=$2
    local feature=$3
    
    cat << EOF
---
status: $status
created: $(date +"%Y-%m-%d")
updated: $(date +"%Y-%m-%d")
author: $agent
feature: $feature
version: 1.0
---
EOF
}

# 函数：创建新文档
create_document() {
    local type=$1
    local feature=$2
    local prefix=$(get_prefix $type)
    local agent=$(get_agent $type)
    local date=$(get_date)
    local filename="${prefix}-${date}-${feature}.md"
    local filepath="$DOCS_BASE/$type/active/$filename"
    
    # 检查文件是否已存在
    if [ -f "$filepath" ]; then
        echo -e "${YELLOW}警告${NC}: 文档已存在: $filepath"
        return 1
    fi
    
    # 创建文档
    echo -e "${BLUE}创建文档${NC}: $filepath"
    
    # 写入元数据和基础模板
    create_metadata "active" "$agent" "$feature" > "$filepath"
    
    # 根据类型添加模板内容
    case $type in
        requirements)
            cat >> "$filepath" << 'EOF'

# [功能名称] 需求文档

## 1. 业务背景与目标

[描述当前的业务痛点和问题]

## 2. 用户故事

As a [角色], I want [功能], so that [价值]

### 2.1 验收标准 (EARS)

- Given [前置条件]
- When [触发动作]
- Then [预期结果]

## 3. 功能需求

| 编号 | 功能模块 | 功能描述 | 优先级 | 复杂度 | 工作量 |
|------|----------|----------|--------|--------|--------|
| FR01 |          |          |        |        |        |

## 4. 非功能需求

- 性能要求: 
- 安全要求: 
- 可用性要求: 

## 5. 数据模型

[涉及的数据表和关联关系]

## 6. 接口设计

[API端点和数据格式]

## 7. 风险与依赖

[技术和业务风险分析]

## 8. 测试策略

[测试计划和验收标准]

## 9. 实施建议

[开发顺序和资源需求]
EOF
            ;;
            
        designs)
            cat >> "$filepath" << 'EOF'

# [功能名称] 技术设计文档

## 1. 概述

### 1.1 设计目标
### 1.2 设计原则
### 1.3 约束条件

## 2. 架构设计

### 2.1 系统架构图
### 2.2 模块设计
### 2.3 技术选型

## 3. 详细设计

### 3.1 API设计
### 3.2 数据模型
### 3.3 业务流程

## 4. 安全设计

### 4.1 认证授权
### 4.2 数据保护

## 5. 性能优化

### 5.1 缓存策略
### 5.2 异步处理

## 6. 部署方案

### 6.1 部署架构
### 6.2 监控方案

## 7. 测试策略

### 7.1 单元测试
### 7.2 集成测试

## 8. 风险评估

## 9. 迁移计划
EOF
            ;;
            
        plans)
            cat >> "$filepath" << 'EOF'

# [功能名称] 实施计划文档

## 1. 执行摘要

- 项目概述: 
- 预计时间: 
- 资源需求: 

## 2. 任务列表

### Sprint 1

| 任务ID | 任务描述 | 类别 | 预估时间 | 依赖 | 负责人 |
|--------|----------|------|----------|------|--------|
| BE-001 |          |      |          |      |        |

### Sprint 2

| 任务ID | 任务描述 | 类别 | 预估时间 | 依赖 | 负责人 |
|--------|----------|------|----------|------|--------|
| FE-001 |          |      |          |      |        |

## 3. 依赖关系图

```mermaid
graph TD
    A[任务1] --> B[任务2]
```

## 4. 风险管理

| 风险 | 严重程度 | 概率 | 缓解措施 |
|------|----------|------|----------|
|      |          |      |          |

## 5. 里程碑

| 里程碑 | 预计日期 | 交付物 |
|--------|----------|--------|
|        |          |        |

## 6. 质量保证

### 6.1 测试计划
### 6.2 代码审查
### 6.3 部署清单
EOF
            ;;
    esac
    
    # 替换功能名称
    sed -i '' "s/\[功能名称\]/$feature/g" "$filepath"
    
    echo -e "${GREEN}成功${NC}: 文档已创建"
    echo "路径: $filepath"
    
    # 显示后续步骤
    echo -e "\n${YELLOW}后续步骤${NC}:"
    case $type in
        requirements)
            echo "1. 完善需求文档内容"
            echo "2. 与用户确认需求"
            echo "3. 需求确认后，执行: $0 status $filename completed"
            echo "4. 然后执行: $0 workflow $filename 开始技术设计"
            ;;
        designs)
            echo "1. 完善技术设计内容"
            echo "2. 进行设计评审"
            echo "3. 设计定稿后，执行: $0 status $filename completed"
            echo "4. 然后执行: $0 workflow $filename 开始实施规划"
            ;;
        plans)
            echo "1. 完善任务分解"
            echo "2. 分配开发资源"
            echo "3. 所有任务完成后，执行: $0 status $filename completed"
            ;;
    esac
}

# 函数：更新文档状态
update_status() {
    local filename=$1
    local new_status=$2
    local found=false
    local source_file=""
    local target_dir=""
    
    # 查找文件
    for type in requirements designs plans; do
        for status in active completed; do
            if [ -f "$DOCS_BASE/$type/$status/$filename" ]; then
                source_file="$DOCS_BASE/$type/$status/$filename"
                
                # 确定目标目录
                if [ "$new_status" = "active" ]; then
                    target_dir="$DOCS_BASE/$type/active"
                else
                    target_dir="$DOCS_BASE/$type/completed"
                fi
                
                found=true
                break 2
            fi
        done
    done
    
    if [ "$found" = false ]; then
        echo -e "${RED}错误${NC}: 找不到文档: $filename"
        return 1
    fi
    
    # 更新元数据
    echo -e "${BLUE}更新状态${NC}: $filename -> $new_status"
    
    # 创建临时文件
    temp_file=$(mktemp)
    
    # 更新status和updated字段
    awk -v new_status="$new_status" -v updated="$(date +"%Y-%m-%d")" '
    BEGIN { in_metadata = 0 }
    /^---$/ { 
        if (in_metadata == 0) {
            in_metadata = 1
        } else {
            in_metadata = 0
        }
        print
        next
    }
    in_metadata && /^status:/ { print "status: " new_status; next }
    in_metadata && /^updated:/ { print "updated: " updated; next }
    { print }
    ' "$source_file" > "$temp_file"
    
    # 移动文件
    target_file="$target_dir/$filename"
    mv "$temp_file" "$target_file"
    
    # 如果源文件和目标文件不同，删除源文件
    if [ "$source_file" != "$target_file" ]; then
        rm "$source_file"
    fi
    
    echo -e "${GREEN}成功${NC}: 状态已更新"
    echo "新位置: $target_file"
}

# 函数：列出文档
list_documents() {
    local filter_type=$1
    local filter_status=$2
    
    echo -e "${BLUE}工程文档列表${NC}\n"
    
    for type in requirements designs plans; do
        # 如果指定了类型过滤，跳过不匹配的
        if [ -n "$filter_type" ] && [ "$type" != "$filter_type" ]; then
            continue
        fi
        
        echo -e "${YELLOW}=== $type ===${NC}"
        
        for status in active completed; do
            # 如果指定了状态过滤，跳过不匹配的
            if [ -n "$filter_status" ] && [ "$status" != "$filter_status" ]; then
                continue
            fi
            
            echo -e "\n${GREEN}[$status]${NC}"
            
            dir="$DOCS_BASE/$type/$status"
            if [ -d "$dir" ]; then
                files=$(ls -1 "$dir" 2>/dev/null | grep -E "\.md$" || true)
                if [ -n "$files" ]; then
                    echo "$files" | while read -r file; do
                        # 读取文档元数据
                        if [ -f "$dir/$file" ]; then
                            created=$(grep "^created:" "$dir/$file" 2>/dev/null | cut -d' ' -f2 || echo "unknown")
                            feature=$(grep "^feature:" "$dir/$file" 2>/dev/null | cut -d' ' -f2- || echo "unknown")
                            echo "  - $file (创建: $created, 功能: $feature)"
                        fi
                    done
                else
                    echo "  (空)"
                fi
            fi
        done
        echo ""
    done
}

# 函数：检查文档状态
check_document() {
    local filename=$1
    local found=false
    
    # 查找文件
    for type in requirements designs plans; do
        for status in active completed; do
            filepath="$DOCS_BASE/$type/$status/$filename"
            if [ -f "$filepath" ]; then
                echo -e "${BLUE}文档信息${NC}: $filename"
                echo "类型: $type"
                echo "当前状态: $status"
                echo "路径: $filepath"
                echo -e "\n${YELLOW}元数据${NC}:"
                
                # 提取元数据
                awk '/^---$/ { if (++count == 2) exit } count == 1 && !/^---$/ { print "  " $0 }' "$filepath"
                
                found=true
                break 2
            fi
        done
    done
    
    if [ "$found" = false ]; then
        echo -e "${RED}错误${NC}: 找不到文档: $filename"
        return 1
    fi
}

# 函数：执行工作流流转
execute_workflow() {
    local filename=$1
    local prefix="${filename%%-*}"
    local feature=$(echo "$filename" | sed 's/^[^-]*-[^-]*-\(.*\)\.md$/\1/')
    local date=$(get_date)
    
    case $prefix in
        REQ)
            # 需求 -> 设计
            echo -e "${BLUE}工作流${NC}: 需求分析 -> 技术设计"
            
            # 检查需求文档是否已完成
            if [ ! -f "$DOCS_BASE/requirements/completed/$filename" ]; then
                echo -e "${YELLOW}警告${NC}: 需求文档尚未完成，请先完成需求文档"
                return 1
            fi
            
            # 创建设计文档
            create_document "designs" "$feature"
            
            # 在设计文档中添加对需求文档的引用
            design_file="TD-${date}-${feature}.md"
            echo -e "\n## 相关文档\n\n- 需求文档: [${filename}](../../requirements/completed/${filename})" >> "$DOCS_BASE/designs/active/$design_file"
            ;;
            
        TD)
            # 设计 -> 计划
            echo -e "${BLUE}工作流${NC}: 技术设计 -> 实施计划"
            
            # 检查设计文档是否已完成
            if [ ! -f "$DOCS_BASE/designs/completed/$filename" ]; then
                echo -e "${YELLOW}警告${NC}: 设计文档尚未完成，请先完成设计文档"
                return 1
            fi
            
            # 创建计划文档
            create_document "plans" "$feature"
            
            # 在计划文档中添加对设计文档的引用
            plan_file="PLAN-${date}-${feature}.md"
            echo -e "\n## 相关文档\n\n- 设计文档: [${filename}](../../designs/completed/${filename})" >> "$DOCS_BASE/plans/active/$plan_file"
            ;;
            
        PLAN)
            echo -e "${GREEN}信息${NC}: 实施计划是最后一个阶段，无需流转"
            ;;
            
        *)
            echo -e "${RED}错误${NC}: 无法识别的文档类型"
            return 1
            ;;
    esac
}

# 主程序
main() {
    # 确保基础目录存在
    mkdir -p "$DOCS_BASE"/{requirements,designs,plans}/{active,completed}
    
    # 解析命令
    case "${1:-}" in
        create)
            if [ $# -lt 3 ]; then
                echo -e "${RED}错误${NC}: 缺少参数"
                echo "用法: $0 create <type> <feature>"
                exit 1
            fi
            create_document "$2" "$3"
            ;;
            
        status)
            if [ $# -lt 3 ]; then
                echo -e "${RED}错误${NC}: 缺少参数"
                echo "用法: $0 status <file> <new_status>"
                exit 1
            fi
            update_status "$2" "$3"
            ;;
            
        list)
            list_documents "$2" "$3"
            ;;
            
        check)
            if [ $# -lt 2 ]; then
                echo -e "${RED}错误${NC}: 缺少参数"
                echo "用法: $0 check <file>"
                exit 1
            fi
            check_document "$2"
            ;;
            
        workflow)
            if [ $# -lt 2 ]; then
                echo -e "${RED}错误${NC}: 缺少参数"
                echo "用法: $0 workflow <file>"
                exit 1
            fi
            execute_workflow "$2"
            ;;
            
        help|--help|-h)
            show_help
            ;;
            
        *)
            echo -e "${RED}错误${NC}: 未知命令: ${1:-}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"