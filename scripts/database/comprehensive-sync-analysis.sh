#!/bin/bash

# FinancialSystem 数据库同步深度分析和解决方案
# 作者: 高级数据库架构师
# 功能: 全面分析三层数据库同步架构并提供解决方案

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
LINUX_IP="**********"
LOCAL_IP=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | grep -v '**********' | awk '{print $2}' | head -1)
MYSQL_CONTAINER="financial-mysql"
ENGLISH_DB="overdue_debt_db"
CHINESE_DB="overdue_debt_db"
USER_DB="user_system"
KINGDEE_DB="kingdee"

# 日志函数
log_header() {
    echo -e "\n${PURPLE}==== $1 ====${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查系统环境
check_system_environment() {
    log_header "系统环境检查"
    
    # 检查网络连接
    log_info "检查网络连接..."
    if ping -c 1 $LINUX_IP >/dev/null 2>&1; then
        log_success "网络连接正常 (ping $LINUX_IP)"
    else
        log_error "网络连接失败 (ping $LINUX_IP)"
        return 1
    fi
    
    # 检查本地MySQL
    log_info "检查本地MySQL服务..."
    if mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" >/dev/null 2>&1; then
        local version=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" --skip-column-names --batch 2>/dev/null)
        log_success "本地MySQL连接正常 (版本: $version)"
    else
        log_error "本地MySQL连接失败"
        return 1
    fi
    
    # 检查Docker状态
    log_info "检查Docker环境..."
    if command -v docker >/dev/null 2>&1; then
        if docker ps >/dev/null 2>&1; then
            log_success "Docker环境可用"
        else
            log_warning "Docker命令可用但服务未启动"
        fi
    else
        log_warning "Docker环境不可用"
    fi
    
    # 检查SSH连接
    log_info "检查SSH连接到Linux服务器..."
    if ssh -o ConnectTimeout=5 -o BatchMode=yes $LINUX_SERVER "echo 'SSH连接成功'" >/dev/null 2>&1; then
        log_success "SSH连接正常"
    else
        log_error "SSH连接失败"
        return 1
    fi
}

# 分析当前数据库状态
analyze_database_status() {
    log_header "数据库状态分析"
    
    # 本地数据库分析
    log_info "分析本地数据库状态..."
    
    # 获取本地数据库列表
    local local_dbs=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW DATABASES;" --skip-column-names --batch 2>/dev/null)
    
    echo "本地数据库列表:"
    echo "$local_dbs" | while read db; do
        if [[ "$db" =~ ^(overdue_debt_db|user_system|kingdee)$ ]]; then
            echo "  ✅ $db"
        else
            echo "  ⚪ $db"
        fi
    done
    
    # Linux服务器数据库分析
    log_info "分析Linux服务器数据库状态..."
    
    # 检查Linux服务器MySQL连接
    if ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SELECT VERSION();'" >/dev/null 2>&1; then
        local linux_dbs=$(ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW DATABASES;' --skip-column-names --batch" 2>/dev/null)
        
        echo "Linux服务器数据库列表:"
        echo "$linux_dbs" | while read db; do
            if [[ "$db" =~ ^(overdue_debt_db|user_system|kingdee)$ ]]; then
                echo "  ✅ $db"
            else
                echo "  ⚪ $db"
            fi
        done
    else
        log_error "无法连接到Linux服务器MySQL"
    fi
}

# 分析复制配置
analyze_replication_config() {
    log_header "MySQL复制配置分析"
    
    # 本地MySQL复制状态
    log_info "分析本地MySQL复制状态..."
    
    # 主服务器状态
    echo "本地MySQL主服务器状态:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW MASTER STATUS;" 2>/dev/null
    
    # 从服务器状态
    echo "本地MySQL从服务器状态:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)" || echo "未配置从服务器"
    
    # 复制用户
    echo "本地MySQL复制用户:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT User, Host, plugin FROM mysql.user WHERE User LIKE '%repl%' OR User LIKE '%slave%';" 2>/dev/null
    
    # 二进制日志配置
    echo "本地MySQL二进制日志配置:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW VARIABLES LIKE 'log_bin%';" 2>/dev/null
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW VARIABLES LIKE 'server_id';" 2>/dev/null
    
    # Linux服务器复制状态
    if ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SELECT VERSION();'" >/dev/null 2>&1; then
        log_info "分析Linux服务器MySQL复制状态..."
        
        echo "Linux服务器MySQL主服务器状态:"
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW MASTER STATUS;'" 2>/dev/null
        
        echo "Linux服务器MySQL从服务器状态:"
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW SLAVE STATUS\G'" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)" || echo "未配置从服务器"
        
        echo "Linux服务器MySQL复制用户:"
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SELECT User, Host, plugin FROM mysql.user WHERE User LIKE \"%repl%\" OR User LIKE \"%slave%\";'" 2>/dev/null
        
        echo "Linux服务器MySQL二进制日志配置:"
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW VARIABLES LIKE \"log_bin%\";'" 2>/dev/null
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW VARIABLES LIKE \"server_id\";'" 2>/dev/null
    fi
}

# 诊断同步问题
diagnose_sync_issues() {
    log_header "同步问题诊断"
    
    local issues_found=0
    
    # 检查复制链路状态
    log_info "检查复制链路状态..."
    
    # 检查本地从服务器状态
    local slave_status=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
    
    if [ -n "$slave_status" ]; then
        local io_running=$(echo "$slave_status" | grep "Slave_IO_Running:" | awk '{print $2}')
        local sql_running=$(echo "$slave_status" | grep "Slave_SQL_Running:" | awk '{print $2}')
        local last_error=$(echo "$slave_status" | grep "Last_Error:" | cut -d' ' -f2-)
        
        if [ "$io_running" != "Yes" ]; then
            log_error "本地MySQL IO线程未运行: $io_running"
            issues_found=$((issues_found + 1))
        fi
        
        if [ "$sql_running" != "Yes" ]; then
            log_error "本地MySQL SQL线程未运行: $sql_running"
            issues_found=$((issues_found + 1))
        fi
        
        if [ -n "$last_error" ] && [ "$last_error" != "" ]; then
            log_error "本地MySQL复制错误: $last_error"
            issues_found=$((issues_found + 1))
        fi
    else
        log_warning "本地MySQL未配置从服务器"
    fi
    
    # 检查网络连接性
    log_info "检查网络连接性..."
    if ! nc -z $LINUX_IP 3306 2>/dev/null; then
        log_error "无法连接到Linux服务器MySQL端口3306"
        issues_found=$((issues_found + 1))
    fi
    
    # 检查认证问题
    log_info "检查认证配置..."
    local repl_user_exists=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT COUNT(*) FROM mysql.user WHERE User='repl_user';" --skip-column-names --batch 2>/dev/null)
    
    if [ "$repl_user_exists" -eq 0 ]; then
        log_error "本地MySQL缺少复制用户repl_user"
        issues_found=$((issues_found + 1))
    fi
    
    # 检查防火墙
    log_info "检查防火墙设置..."
    if command -v ufw >/dev/null 2>&1; then
        local firewall_status=$(ufw status 2>/dev/null | grep -c "Status: active" || echo 0)
        if [ "$firewall_status" -gt 0 ]; then
            log_warning "防火墙已启用，可能影响MySQL复制"
        fi
    fi
    
    echo "诊断总结: 发现 $issues_found 个问题"
    return $issues_found
}

# 创建修复方案
create_repair_plan() {
    log_header "修复方案制定"
    
    cat << EOF

🔧 FinancialSystem 数据库同步修复方案

基于当前分析，以下是推荐的修复步骤：

1. 【紧急修复】解决认证问题
   - 检查并修复MySQL复制用户认证方式
   - 确保使用mysql_native_password认证插件
   - 验证用户权限配置

2. 【网络配置】优化网络连接
   - 确保3306端口在防火墙中开放
   - 检查网络延迟和稳定性
   - 配置SSL连接（可选）

3. 【复制链路】重建双向复制
   - 停止现有复制配置
   - 清理并重新配置复制用户
   - 重新建立主从关系

4. 【数据一致性】确保数据同步
   - 比较本地和远程数据库结构
   - 执行数据一致性检查
   - 必要时重新同步数据

5. 【Docker环境】建立本地Docker测试环境
   - 创建本地Docker MySQL实例
   - 配置Docker与本地MySQL同步
   - 建立三层同步测试环境

6. 【监控机制】建立监控和告警
   - 配置复制状态监控
   - 设置数据一致性检查
   - 建立故障自动恢复机制

EOF
}

# 实施自动修复
implement_auto_repair() {
    log_header "自动修复实施"
    
    local repair_steps=0
    local successful_repairs=0
    
    # 修复1: 复制用户认证
    log_info "修复复制用户认证..."
    repair_steps=$((repair_steps + 1))
    
    if mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
CREATE USER IF NOT EXISTS 'repl_user'@'$LINUX_IP' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'$LINUX_IP';
FLUSH PRIVILEGES;
EOF
    then
        log_success "本地复制用户认证修复成功"
        successful_repairs=$((successful_repairs + 1))
    else
        log_error "本地复制用户认证修复失败"
    fi
    
    # 修复2: 停止有问题的复制
    log_info "停止有问题的复制连接..."
    repair_steps=$((repair_steps + 1))
    
    if mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "STOP SLAVE; RESET SLAVE ALL;" 2>/dev/null; then
        log_success "复制连接已重置"
        successful_repairs=$((successful_repairs + 1))
    else
        log_warning "复制连接重置失败（可能未配置）"
    fi
    
    # 修复3: 优化MySQL配置
    log_info "优化MySQL配置..."
    repair_steps=$((repair_steps + 1))
    
    if mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
SET GLOBAL binlog_format = 'ROW';
SET GLOBAL binlog_checksum = 'CRC32';
SET GLOBAL sync_binlog = 1;
SET GLOBAL innodb_flush_log_at_trx_commit = 1;
EOF
    then
        log_success "MySQL配置优化成功"
        successful_repairs=$((successful_repairs + 1))
    else
        log_error "MySQL配置优化失败"
    fi
    
    echo "自动修复总结: $successful_repairs/$repair_steps 个修复成功"
}

# 创建本地Docker测试环境
create_local_docker_environment() {
    log_header "创建本地Docker测试环境"
    
    # 创建测试用的Docker Compose文件
    cat > /tmp/test-mysql-sync.yml << EOF
version: '3.8'

services:
  test-mysql:
    image: mysql:8.0
    container_name: test-mysql-sync
    environment:
      MYSQL_ROOT_PASSWORD: $MYSQL_ROOT_PASSWORD
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3307:3306"
    volumes:
      - test_mysql_data:/var/lib/mysql
    command: >
      --server-id=99
      --log-bin=mysql-bin
      --binlog-format=ROW
      --binlog-do-db=$ENGLISH_DB
      --binlog-do-db=$USER_DB
      --binlog-do-db=$KINGDEE_DB
      --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

volumes:
  test_mysql_data:
EOF
    
    log_info "Docker测试环境配置文件已创建: /tmp/test-mysql-sync.yml"
    log_info "启动测试环境: docker-compose -f /tmp/test-mysql-sync.yml up -d"
    log_info "连接测试环境: mysql -h 127.0.0.1 -P 3307 -u root -p$MYSQL_ROOT_PASSWORD"
}

# 生成监控脚本
generate_monitoring_script() {
    log_header "生成监控脚本"
    
    cat > /tmp/mysql-sync-monitor.sh << 'EOF'
#!/bin/bash

# MySQL同步监控脚本

MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

check_replication_status() {
    echo "=== 复制状态检查 ==="
    
    # 本地状态
    echo "本地MySQL从服务器状态:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_Error)" || echo "未配置复制"
    
    # 远程状态
    echo "Linux服务器MySQL从服务器状态:"
    ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW SLAVE STATUS\G'" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_Error)" || echo "未配置复制"
}

check_data_consistency() {
    echo "=== 数据一致性检查 ==="
    
    # 检查表数量
    local_tables=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='overdue_debt_db';" --skip-column-names --batch 2>/dev/null)
    remote_tables=$(ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=\"overdue_debt_db\";' --skip-column-names --batch" 2>/dev/null)
    
    echo "本地表数量: $local_tables"
    echo "远程表数量: $remote_tables"
    
    if [ "$local_tables" = "$remote_tables" ]; then
        echo "✅ 表数量一致"
    else
        echo "❌ 表数量不一致"
    fi
}

# 运行监控
echo "MySQL同步监控报告 - $(date)"
check_replication_status
check_data_consistency
EOF
    
    chmod +x /tmp/mysql-sync-monitor.sh
    log_success "监控脚本已生成: /tmp/mysql-sync-monitor.sh"
}

# 生成详细报告
generate_comprehensive_report() {
    log_header "生成综合分析报告"
    
    local report_file="/tmp/mysql-sync-comprehensive-report-$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# FinancialSystem 数据库同步深度分析报告

## 报告信息
- 生成时间: $(date)
- 分析师: 高级数据库架构师
- 项目: FinancialSystem
- 目标: 三层数据库同步架构优化

## 执行摘要

### 当前状态
- **本地MySQL**: 运行正常，版本 $(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" --skip-column-names --batch 2>/dev/null)
- **Linux服务器**: 网络连接正常，SSH可达
- **Docker环境**: 启动存在问题，需要修复
- **同步状态**: 部分配置，存在认证问题

### 主要发现
1. **认证问题**: MySQL复制用户认证方式不兼容
2. **网络配置**: 连接正常但可能存在防火墙限制
3. **复制配置**: 部分配置但存在错误
4. **Docker环境**: 启动问题影响本地测试

### 风险评估
- **高风险**: 认证失败可能导致数据不同步
- **中风险**: Docker环境问题影响开发测试
- **低风险**: 网络延迟可能影响同步性能

## 技术分析

### 当前架构
\`\`\`
Linux服务器(**********) <---> 本地MySQL($(echo $LOCAL_IP)) <---> Docker MySQL(未启动)
\`\`\`

### 数据库状态
$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW DATABASES;" --skip-column-names --batch 2>/dev/null | grep -E "(overdue_debt_db|user_system|kingdee)" | sed 's/^/- /')

### 复制配置分析
- **Server ID**: $(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW VARIABLES LIKE 'server_id';" --skip-column-names --batch 2>/dev/null | awk '{print $2}')
- **Binary Log**: $(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW VARIABLES LIKE 'log_bin';" --skip-column-names --batch 2>/dev/null | awk '{print $2}')
- **复制用户**: $(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT COUNT(*) FROM mysql.user WHERE User='repl_user';" --skip-column-names --batch 2>/dev/null)个

## 解决方案

### 1. 立即修复方案
- 修复MySQL复制用户认证方式
- 重新配置双向复制
- 验证数据一致性

### 2. Docker环境修复
- 重启Docker Desktop
- 配置本地测试环境
- 建立三层同步测试

### 3. 监控和维护
- 部署监控脚本
- 建立告警机制
- 定期一致性检查

## 实施建议

### 优先级1 (立即执行)
1. 修复认证问题
2. 重建复制链路
3. 数据一致性验证

### 优先级2 (短期内完成)
1. Docker环境修复
2. 监控系统部署
3. 故障恢复机制

### 优先级3 (长期优化)
1. 性能优化
2. 安全加固
3. 自动化运维

## 附录

### 相关脚本
- 综合修复脚本: $(realpath $0)
- 监控脚本: /tmp/mysql-sync-monitor.sh
- Docker测试配置: /tmp/test-mysql-sync.yml

### 技术文档
- MySQL复制配置: scripts/database/migration/
- Docker配置: docker-compose.yml
- 项目文档: docs/

EOF
    
    log_success "综合分析报告已生成: $report_file"
    echo "报告位置: $report_file"
}

# 主函数
main() {
    log_header "FinancialSystem 数据库同步深度分析"
    
    echo -e "${PURPLE}🚀 开始执行数据库同步深度分析...${NC}\n"
    
    # 环境检查
    if ! check_system_environment; then
        log_error "系统环境检查失败，请修复后重试"
        exit 1
    fi
    
    # 数据库状态分析
    analyze_database_status
    
    # 复制配置分析
    analyze_replication_config
    
    # 问题诊断
    local issues_count
    issues_count=$(diagnose_sync_issues)
    
    # 制定修复方案
    create_repair_plan
    
    # 询问是否执行自动修复
    echo ""
    read -p "是否执行自动修复？(y/N): " auto_repair
    if [ "$auto_repair" = "y" ] || [ "$auto_repair" = "Y" ]; then
        implement_auto_repair
    fi
    
    # 生成测试环境
    create_local_docker_environment
    
    # 生成监控脚本
    generate_monitoring_script
    
    # 生成综合报告
    generate_comprehensive_report
    
    echo ""
    log_success "🎉 数据库同步深度分析完成!"
    log_info "📊 检查生成的报告和脚本以了解详细信息"
    log_info "🔧 按照修复方案执行相应操作"
    log_info "📈 使用监控脚本定期检查同步状态"
}

# 显示帮助信息
show_help() {
    cat << EOF
FinancialSystem 数据库同步深度分析工具

功能:
- 全面分析三层数据库同步架构
- 诊断同步问题并提供解决方案
- 生成修复脚本和监控工具
- 创建测试环境配置

用法:
  $0                    # 执行完整分析
  $0 --help             # 显示帮助信息
  $0 --quick-check      # 快速状态检查
  $0 --repair-only      # 仅执行修复操作

环境要求:
- MySQL 8.0+
- Docker (可选)
- SSH访问Linux服务器
- 网络连接正常

EOF
}

# 快速检查模式
quick_check() {
    log_header "快速状态检查"
    
    # 基本连接检查
    echo "MySQL连接: $(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 'OK';" --skip-column-names --batch 2>/dev/null || echo 'FAILED')"
    echo "Linux服务器: $(ping -c 1 $LINUX_IP >/dev/null 2>&1 && echo 'OK' || echo 'FAILED')"
    echo "SSH连接: $(ssh -o ConnectTimeout=5 -o BatchMode=yes $LINUX_SERVER "echo 'OK'" 2>/dev/null || echo 'FAILED')"
    
    # 复制状态
    local slave_status=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "Slave_IO_Running|Slave_SQL_Running" | wc -l)
    echo "复制状态: $([ $slave_status -gt 0 ] && echo 'CONFIGURED' || echo 'NOT_CONFIGURED')"
}

# 参数解析
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --quick-check)
        quick_check
        exit 0
        ;;
    --repair-only)
        implement_auto_repair
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac