#!/bin/bash
# 快速问题诊断工具

echo "🔍 问题诊断助手"
echo "==============="

case "$1" in
    "startup")
        echo "检查启动问题..."
        echo ""
        echo "1. 检查端口占用:"
        lsof -i :8080 || echo "8080端口未被占用"
        echo ""
        echo "2. 检查Java进程:"
        ps aux | grep java | grep -v grep || echo "没有Java进程运行"
        echo ""
        echo "3. 尝试启动测试:"
        ./scripts/test-startup.sh
        ;;
    
    "api")
        echo "检查API问题..."
        echo ""
        echo "1. 健康检查:"
        curl -v http://localhost:8080/actuator/health
        echo ""
        echo "2. 测试登录接口:"
        curl -X POST http://localhost:8080/api/auth/login \
             -H "Content-Type: application/json" \
             -d '{"username":"admin","password":"admin123"}'
        ;;
    
    "db")
        echo "检查数据库问题..."
        echo ""
        echo "1. 测试连接:"
        mysql -h localhost -u root -p123456 -e "SELECT VERSION();" || echo "连接失败"
        echo ""
        echo "2. 查看进程列表:"
        mysql -h localhost -u root -p123456 -e "SHOW PROCESSLIST;" || echo "查询失败"
        ;;
    
    "test")
        echo "运行快速测试..."
        mvn test -Dtest=FunctionProtectionTest
        ;;
    
    *)
        echo "用法: $0 [startup|api|db|test]"
        echo ""
        echo "  startup - 检查启动问题"
        echo "  api     - 检查API问题"
        echo "  db      - 检查数据库问题"
        echo "  test    - 运行功能保护测试"
        ;;
esac
