#!/bin/bash
# 简化版功能保护测试 - 不需要完整编译

echo "🛡️ 运行简化版功能保护测试..."
echo "================================"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 检查关键文件是否存在
echo ""
echo "1️⃣ 检查核心文件完整性..."
CRITICAL_FILES=(
    "api-gateway/src/main/java/com/laoshu198838/Main.java"
    "api-gateway/src/main/java/com/laoshu198838/controller/auth/AuthController.java"
    "api-gateway/src/main/java/com/laoshu198838/controller/debt/OverdueDebtGetController.java"
    "FinancialSystem-web/src/App.js"
    "FinancialSystem-web/src/components/AppRoutes.js"
)

ALL_FILES_EXIST=true
for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ 缺失: $file"
        ALL_FILES_EXIST=false
    fi
done

if [ "$ALL_FILES_EXIST" = false ]; then
    echo "❌ 关键文件缺失，请检查项目完整性"
    exit 1
fi

# 2. 检查API接口结构
echo ""
echo "2️⃣ 检查API接口定义..."

# 检查登录接口
if grep -q "@PostMapping.*login" api-gateway/src/main/java/com/laoshu198838/controller/auth/AuthController.java; then
    echo "✅ 登录接口存在"
else
    echo "❌ 登录接口被修改或删除"
fi

# 检查债权查询接口
if grep -q "@GetMapping" api-gateway/src/main/java/com/laoshu198838/controller/debt/OverdueDebtGetController.java; then
    echo "✅ 债权查询接口存在"
else
    echo "❌ 债权查询接口被修改或删除"
fi

# 3. 检查数据库配置
echo ""
echo "3️⃣ 检查数据库配置..."
if grep -q "overdue_debt_db" api-gateway/src/main/resources/application.yml; then
    echo "✅ 主数据库配置存在"
else
    echo "❌ 数据库配置被修改"
fi

# 4. 检查前端路由
echo ""
echo "4️⃣ 检查前端路由配置..."
if [ -f "FinancialSystem-web/src/routes.jsx" ]; then
    echo "✅ 路由文件存在"
else
    echo "⚠️ 路由文件位置可能已更改"
fi

# 5. 语法快速检查（如果javac可用）
echo ""
echo "5️⃣ 快速语法检查..."
if command -v javac &> /dev/null; then
    # 只检查Main.java的语法
    javac -cp "api-gateway/target/classes:api-gateway/lib/*" -d /tmp api-gateway/src/main/java/com/laoshu198838/Main.java 2>/dev/null && {
        echo "✅ Java语法检查通过"
        rm -f /tmp/com/laoshu198838/Main.class
    } || {
        echo "⚠️ Java语法检查失败（可能是依赖问题，不影响基础功能）"
    }
else
    echo "⚠️ javac未安装，跳过语法检查"
fi

# 6. 检查package.json脚本
echo ""
echo "6️⃣ 检查前端构建脚本..."
if grep -q '"build":' FinancialSystem-web/package.json; then
    echo "✅ 前端构建脚本存在"
else
    echo "❌ 前端构建脚本被修改"
fi

# 计算总耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "================================"
echo "✅ 基础功能保护测试完成！"
echo "⏱️ 耗时: ${DURATION}秒"
echo ""
echo "💡 提示："
echo "- 这是简化版测试，主要检查文件和接口完整性"
echo "- 如需完整测试，请先解决Maven依赖问题"
echo "- 可以继续使用Git提交和推送"