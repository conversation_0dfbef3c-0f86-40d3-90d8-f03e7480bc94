#!/bin/bash

# 安全版本切换脚本
# 使用方法: ./scripts/safe-version-switch.sh [commit-id]
# 或者: ./scripts/safe-version-switch.sh back

COMMIT_ID="$1"
SWITCH_LOG="/tmp/version_switch_log"

# 如果是回到原分支
if [[ "$COMMIT_ID" == "back" || "$COMMIT_ID" == "current" ]]; then
    if [[ -f "$SWITCH_LOG" ]]; then
        ORIGINAL_BRANCH=$(cat "$SWITCH_LOG")
        echo "🔙 回到原分支: $ORIGINAL_BRANCH"
        git checkout "$ORIGINAL_BRANCH"
        rm -f "$SWITCH_LOG"
        echo "✅ 已成功回到原来的开发状态"
        
        # 显示当前状态
        echo ""
        echo "📋 当前状态:"
        echo "分支: $(git branch --show-current)"
        echo "最新提交: $(git log -1 --oneline)"
    else
        echo "❌ 未找到切换记录，无法自动回到原分支"
        echo "当前分支: $(git branch --show-current)"
    fi
    exit 0
fi

# 验证commit-id
if [[ -z "$COMMIT_ID" ]]; then
    echo "❌ 请提供commit-id"
    echo "用法: $0 <commit-id>"
    echo "或者: $0 back  # 回到原分支"
    exit 1
fi

# 验证commit-id是否存在
if ! git rev-parse --verify "$COMMIT_ID" >/dev/null 2>&1; then
    echo "❌ 无效的commit-id: $COMMIT_ID"
    exit 1
fi

echo "🕰️ 准备安全切换到版本: $COMMIT_ID"
echo ""

# 1. 检查工作区状态
echo "📋 步骤1: 检查工作区状态..."
if ! git diff-index --quiet HEAD --; then
    echo "⚠️  发现未提交的更改"
    echo ""
    echo "📝 未提交的更改:"
    git status --porcelain | head -10
    echo ""
    
    read -p "是否自动创建快照保存当前更改? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "📸 创建版本切换前的自动快照..."
        git add .
        git commit -m "checkpoint: 版本切换前自动快照 - $(date '+%Y-%m-%d %H:%M:%S')"
        echo "✅ 快照创建完成"
    else
        echo "❌ 请先处理未提交的更改，或者使用 'git stash' 暂存"
        exit 1
    fi
else
    echo "✅ 工作区干净，可以安全切换"
fi

# 2. 记录当前分支
echo ""
echo "📋 步骤2: 记录当前分支信息..."
CURRENT_BRANCH=$(git branch --show-current)
echo "$CURRENT_BRANCH" > "$SWITCH_LOG"
echo "✅ 已记录当前分支: $CURRENT_BRANCH"

# 3. 获取目标版本信息
echo ""
echo "📋 步骤3: 获取目标版本信息..."
TARGET_MSG=$(git log -1 --pretty=format:"%s" "$COMMIT_ID" 2>/dev/null)
TARGET_DATE=$(git log -1 --pretty=format:"%cd" --date=short "$COMMIT_ID" 2>/dev/null)
TARGET_AUTHOR=$(git log -1 --pretty=format:"%an" "$COMMIT_ID" 2>/dev/null)

echo "🎯 目标版本信息:"
echo "  提交ID: $COMMIT_ID"
echo "  提交信息: $TARGET_MSG"
echo "  提交日期: $TARGET_DATE"
echo "  提交作者: $TARGET_AUTHOR"

# 4. 确认切换
echo ""
read -p "确认切换到此版本? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 取消版本切换"
    rm -f "$SWITCH_LOG"
    exit 0
fi

# 5. 执行切换
echo ""
echo "📋 步骤4: 执行版本切换..."
if git checkout "$COMMIT_ID" 2>/dev/null; then
    echo "✅ 成功切换到版本 $COMMIT_ID"
else
    echo "❌ 版本切换失败"
    rm -f "$SWITCH_LOG"
    exit 1
fi

# 6. 显示切换后状态
echo ""
echo "🎉 版本切换完成！"
echo ""
echo "📋 当前状态:"
echo "  版本: $COMMIT_ID"
echo "  提交信息: $TARGET_MSG"
echo "  提交日期: $TARGET_DATE"
echo "  原分支已保存: $CURRENT_BRANCH"
echo ""

echo "⚠️  重要提醒:"
echo "  • 当前处于分离HEAD状态，仅用于查看代码"
echo "  • 请勿在此状态下进行开发工作"
echo "  • 查看完成后请使用 './scripts/safe-version-switch.sh back' 回到原分支"
echo "  • 或者直接告诉Claude '回到当前'"
echo ""

echo "🔍 可用操作:"
echo "  • 查看代码结构: ls -la"
echo "  • 运行旧版本测试: ./scripts/test-startup.sh"
echo "  • 查看特定文件: cat <file-path>"
echo "  • 回到原分支: ./scripts/safe-version-switch.sh back"
echo ""

# 显示与原分支的差异统计
echo "📊 与原分支的差异统计:"
git diff --stat "$CURRENT_BRANCH" | head -10
echo ""
echo "💡 使用 'git diff $CURRENT_BRANCH' 查看详细差异"