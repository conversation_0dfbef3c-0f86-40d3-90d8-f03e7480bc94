#!/bin/bash
# auto-branch.sh - 智能分支管理脚本
# 根据输入智能创建和切换分支

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取参数
BRANCH_INPUT="$1"
BASE_BRANCH="$2"

# 辅助函数
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 转换中文到英文（简单映射）
translate_branch_name() {
    local input="$1"
    local result=""
    
    # 常见中文到英文的映射
    result=$(echo "$input" | sed -e '
        s/登录/login/g
        s/注册/register/g
        s/用户/user/g
        s/权限/permission/g
        s/管理/management/g
        s/优化/optimize/g
        s/修复/fix/g
        s/崩溃/crash/g
        s/性能/performance/g
        s/导出/export/g
        s/导入/import/g
        s/数据/data/g
        s/接口/api/g
        s/界面/ui/g
        s/功能/feature/g
        s/错误/error/g
        s/更新/update/g
        s/删除/delete/g
        s/添加/add/g
        s/查询/query/g
        s/搜索/search/g
        s/报表/report/g
        s/统计/statistics/g
        s/配置/config/g
        s/测试/test/g
        s/文档/docs/g
        s/安全/security/g
        s/认证/auth/g
        s/授权/authorization/g
        s/缓存/cache/g
        s/数据库/database/g
        s/日志/log/g
        s/监控/monitor/g
        s/通知/notification/g
        s/消息/message/g
        s/邮件/email/g
        s/支付/payment/g
        s/订单/order/g
        s/商品/product/g
        s/库存/inventory/g
        s/财务/finance/g
        s/报告/report/g
        s/分析/analysis/g
        s/仪表盘/dashboard/g
        s/设置/settings/g
        s/个人资料/profile/g
        s/密码/password/g
        s/重置/reset/g
        s/忘记/forgot/g
        s/记住/remember/g
        s/自动/auto/g
        s/手动/manual/g
        s/批量/batch/g
        s/单个/single/g
        s/列表/list/g
        s/详情/detail/g
        s/编辑/edit/g
        s/保存/save/g
        s/取消/cancel/g
        s/确认/confirm/g
        s/提交/submit/g
        s/审核/review/g
        s/通过/approve/g
        s/拒绝/reject/g
        s/撤销/revoke/g
        s/恢复/restore/g
        s/备份/backup/g
        s/迁移/migrate/g
        s/升级/upgrade/g
        s/降级/downgrade/g
        s/回滚/rollback/g
        s/部署/deploy/g
        s/发布/release/g
        s/版本/version/g
        s/标签/tag/g
        s/分支/branch/g
        s/合并/merge/g
        s/冲突/conflict/g
        s/解决/resolve/g
        s/问题/issue/g
        s/漏洞/vulnerability/g
        s/补丁/patch/g
        s/更新/update/g
        s/改进/improve/g
        s/重构/refactor/g
        s/清理/cleanup/g
        s/格式化/format/g
        s/规范/standard/g
        s/文档/documentation/g
        s/注释/comment/g
        s/说明/description/g
        s/示例/example/g
        s/演示/demo/g
        s/教程/tutorial/g
        s/指南/guide/g
        s/帮助/help/g
        s/关于/about/g
        s/联系/contact/g
        s/反馈/feedback/g
        s/建议/suggestion/g
        s/投诉/complaint/g
        s/举报/report/g
    ')
    
    # 转换为小写并替换空格为连字符
    result=$(echo "$result" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')
    
    # 移除特殊字符，只保留字母、数字和连字符
    result=$(echo "$result" | sed 's/[^a-z0-9-]//g')
    
    # 移除多余的连字符
    result=$(echo "$result" | sed 's/--*/-/g' | sed 's/^-//' | sed 's/-$//')
    
    echo "$result"
}

# 判断分支类型
determine_branch_type() {
    local input="$1"
    local branch_type="feature"
    
    # 检查是否包含修复相关的关键词
    if echo "$input" | grep -qiE '修复|fix|修正|解决|bug|错误|崩溃|crash|问题|issue'; then
        # 检查是否是紧急修复
        if echo "$input" | grep -qiE '紧急|urgent|立即|immediately|严重|critical|崩溃|crash'; then
            branch_type="hotfix"
        else
            branch_type="bugfix"
        fi
    # 检查是否是发布相关
    elif echo "$input" | grep -qiE '发布|release|版本|version|上线|deploy'; then
        branch_type="release"
    # 检查是否是文档相关
    elif echo "$input" | grep -qiE '文档|docs|documentation|说明|readme'; then
        branch_type="docs"
    # 检查是否是测试相关
    elif echo "$input" | grep -qiE '测试|test|单元测试|unit|集成测试|integration'; then
        branch_type="test"
    # 检查是否是重构
    elif echo "$input" | grep -qiE '重构|refactor|优化|optimize|改进|improve'; then
        branch_type="refactor"
    # 检查是否是配置相关
    elif echo "$input" | grep -qiE '配置|config|设置|settings|环境|environment'; then
        branch_type="chore"
    fi
    
    echo "$branch_type"
}

# 确定基础分支
determine_base_branch() {
    local branch_type="$1"
    local base=""
    
    case "$branch_type" in
        "hotfix")
            base="main"
            ;;
        "release")
            base="develop"
            ;;
        *)
            base="develop"
            ;;
    esac
    
    echo "$base"
}

# 检查分支是否存在
branch_exists() {
    local branch="$1"
    git show-ref --verify --quiet "refs/heads/$branch"
}

# 保存当前工作
stash_changes() {
    if [[ -n $(git status -s) ]]; then
        print_warning "检测到未提交的更改，正在暂存..."
        git stash push -m "auto-branch: 切换分支前的自动暂存 - $(date +'%Y-%m-%d %H:%M:%S')"
        return 0
    fi
    return 1
}

# 主函数
main() {
    # 检查参数
    if [ -z "$BRANCH_INPUT" ]; then
        print_error "请提供分支名称或描述"
        echo "使用方法: $0 <分支描述> [基础分支]"
        echo "示例: $0 '优化登录性能'"
        echo "示例: $0 'feature/new-api' main"
        exit 1
    fi
    
    # 检查是否是完整的分支名
    if [[ "$BRANCH_INPUT" =~ ^(feature|hotfix|bugfix|release|docs|test|refactor|chore)/ ]]; then
        # 用户提供了完整的分支名
        BRANCH_NAME="$BRANCH_INPUT"
        BRANCH_TYPE=$(echo "$BRANCH_NAME" | cut -d'/' -f1)
    else
        # 需要智能转换
        print_status "分析分支描述: $BRANCH_INPUT"
        
        # 确定分支类型
        BRANCH_TYPE=$(determine_branch_type "$BRANCH_INPUT")
        print_status "识别为: $BRANCH_TYPE 类型"
        
        # 转换分支名
        TRANSLATED_NAME=$(translate_branch_name "$BRANCH_INPUT")
        BRANCH_NAME="${BRANCH_TYPE}/${TRANSLATED_NAME}"
        
        print_status "生成分支名: $BRANCH_NAME"
    fi
    
    # 确定基础分支
    if [ -z "$BASE_BRANCH" ]; then
        BASE_BRANCH=$(determine_base_branch "$BRANCH_TYPE")
    fi
    print_status "基础分支: $BASE_BRANCH"
    
    # 检查基础分支是否存在
    if ! branch_exists "$BASE_BRANCH"; then
        print_error "基础分支 $BASE_BRANCH 不存在"
        exit 1
    fi
    
    # 暂存当前更改
    STASHED=false
    if stash_changes; then
        STASHED=true
    fi
    
    # 检查分支是否已存在
    if branch_exists "$BRANCH_NAME"; then
        print_warning "分支 $BRANCH_NAME 已存在"
        print_status "切换到现有分支..."
        git checkout "$BRANCH_NAME"
    else
        print_status "创建新分支..."
        
        # 更新基础分支
        print_status "更新基础分支 $BASE_BRANCH..."
        git checkout "$BASE_BRANCH"
        git pull origin "$BASE_BRANCH" 2>/dev/null || true
        
        # 创建并切换到新分支
        git checkout -b "$BRANCH_NAME"
        print_success "创建并切换到新分支: $BRANCH_NAME"
        
        # 设置上游分支
        git push -u origin "$BRANCH_NAME" 2>/dev/null || print_warning "无法推送到远程（可能没有网络连接）"
    fi
    
    # 恢复暂存的更改
    if [ "$STASHED" = true ]; then
        print_status "恢复之前的工作..."
        git stash pop || print_warning "恢复暂存时出现冲突，请手动解决"
    fi
    
    # 显示当前状态
    print_success "操作完成！"
    echo -e "\n${BLUE}当前状态:${NC}"
    echo "  分支: $(git rev-parse --abbrev-ref HEAD)"
    echo "  基于: $BASE_BRANCH"
    echo "  类型: $BRANCH_TYPE"
    
    # 提供后续建议
    echo -e "\n${BLUE}后续步骤:${NC}"
    case "$BRANCH_TYPE" in
        "feature")
            echo "  1. 开发新功能"
            echo "  2. 提交更改: git commit -m 'feat: 描述'"
            echo "  3. 推送分支: git push"
            echo "  4. 完成后合并到develop"
            ;;
        "hotfix"|"bugfix")
            echo "  1. 修复问题"
            echo "  2. 提交更改: git commit -m 'fix: 描述'"
            echo "  3. 推送分支: git push"
            echo "  4. 完成后合并到main和develop"
            ;;
        "release")
            echo "  1. 准备发布"
            echo "  2. 更新版本号"
            echo "  3. 完成后合并到main并打标签"
            ;;
    esac
}

# 执行主函数
main