#!/bin/bash

# AI智能指令处理脚本
# 这个脚本将被Claude调用来处理用户的快照/回滚等指令

COMMAND="$1"
PARAM="$2"

# 检查是否在AI任务分支
check_ai_branch() {
    CURRENT_BRANCH=$(git branch --show-current)
    if [[ "$CURRENT_BRANCH" =~ ^feature/ai- ]]; then
        return 0
    else
        echo "⚠️  当前不在AI任务分支 (当前: $CURRENT_BRANCH)"
        echo "建议先运行: ./scripts/ai-task-start.sh [类型] [描述]"
        return 1
    fi
}

# 智能快照处理
handle_snapshot() {
    local description="$1"
    
    if ! check_ai_branch; then
        return 1
    fi
    
    echo "📸 AI自动快照: $description"
    
    # 检查是否有更改
    if git diff-index --quiet HEAD --; then
        echo "⚠️  没有检测到代码更改"
        echo "当前工作区是干净的，确定要创建空快照吗？"
        read -p "继续? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ 取消快照创建"
            return 1
        fi
        # 创建一个标记文件以便提交
        echo "# 快照标记: $description" > ".snapshot-$(date +%s)"
        git add ".snapshot-$(date +%s)"
    fi
    
    # 创建快照
    ./scripts/ai-checkpoint.sh create "$description"
    
    # 可选：运行快速验证
    echo "🤔 是否需要运行快速测试验证？"
    read -p "运行测试? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🧪 运行快速验证..."
        if ./scripts/test-startup.sh > "/tmp/auto-snapshot-test-$(date +%s).log" 2>&1; then
            echo "✅ 快照验证通过"
        else
            echo "❌ 快照验证失败，但快照已创建"
            echo "建议检查测试日志"
        fi
    fi
}

# 智能回滚处理
handle_rollback() {
    local steps="${1:-1}"
    
    if ! check_ai_branch; then
        return 1
    fi
    
    echo "🔙 AI自动回滚: $steps 步"
    ./scripts/ai-checkpoint.sh rollback "$steps"
}

# 智能状态查看
handle_status() {
    if ! check_ai_branch; then
        return 1
    fi
    
    echo "📋 AI任务状态："
    ./scripts/ai-checkpoint.sh info
    echo ""
    echo "📸 最近的检查点："
    ./scripts/ai-checkpoint.sh list
}

# 智能测试处理
handle_test() {
    echo "🧪 AI自动测试验证..."
    
    # 运行启动测试
    if ./scripts/test-startup.sh; then
        echo "✅ 所有测试通过"
        
        # 如果在AI分支，询问是否创建测试通过的快照
        if git branch --show-current | grep -q "^feature/ai-"; then
            echo ""
            read -p "测试通过，是否创建快照? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                handle_snapshot "测试验证通过"
            fi
        fi
    else
        echo "❌ 测试失败"
        
        # 如果在AI分支，询问是否创建故障快照用于调试
        if git branch --show-current | grep -q "^feature/ai-"; then
            echo ""
            read -p "是否创建故障快照用于调试? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                handle_snapshot "测试失败-待调试"
            fi
        fi
    fi
}

# 主处理逻辑
case "$COMMAND" in
    "snapshot"|"快照"|"checkpoint"|"检查点")
        handle_snapshot "$PARAM"
        ;;
    "rollback"|"回滚"|"undo"|"撤销")
        handle_rollback "$PARAM"
        ;;
    "status"|"状态"|"list"|"列表")
        handle_status
        ;;
    "test"|"测试"|"verify"|"验证")
        handle_test
        ;;
    *)
        echo "🤖 AI智能指令处理器"
        echo ""
        echo "支持的指令："
        echo "  snapshot|快照 [描述]     - 创建检查点"
        echo "  rollback|回滚 [步数]     - 回滚检查点" 
        echo "  status|状态             - 查看任务状态"
        echo "  test|测试               - 运行验证测试"
        echo ""
        echo "示例："
        echo "  ./scripts/ai-smart-handler.sh snapshot \"完成用户认证\""
        echo "  ./scripts/ai-smart-handler.sh rollback 2"
        echo "  ./scripts/ai-smart-handler.sh test"
        ;;
esac