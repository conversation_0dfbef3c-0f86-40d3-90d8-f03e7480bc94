#!/bin/bash
# organize-docs.sh - 文档自动整理脚本
# 用于整理、归档和维护项目文档

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
DOCS_DIR="docs"
ARCHIVE_DIR="docs/archive"
CURRENT_YEAR=$(date +%Y)
CURRENT_DATE=$(date +%Y-%m-%d)

# 参数处理
DRY_RUN=false
MONTHLY_CLEANUP=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --monthly-cleanup)
            MONTHLY_CLEANUP=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "使用方法: $0 [选项]"
            echo "选项:"
            echo "  --dry-run         预览模式，不执行实际操作"
            echo "  --monthly-cleanup 执行月度深度清理"
            echo "  --verbose         显示详细信息"
            echo "  -h, --help        显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 辅助函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# 创建必要的目录结构
create_directory_structure() {
    log "创建新的文档目录结构..."
    
    local dirs=(
        "$DOCS_DIR/01-quickstart"
        "$DOCS_DIR/02-architecture"
        "$DOCS_DIR/03-development"
        "$DOCS_DIR/04-business/debt-management"
        "$DOCS_DIR/04-business/user-system"
        "$DOCS_DIR/04-business/reporting"
        "$DOCS_DIR/05-operations/deployment"
        "$DOCS_DIR/05-operations/monitoring"
        "$DOCS_DIR/05-operations/maintenance"
        "$DOCS_DIR/06-api/endpoints"
        "$DOCS_DIR/06-api/examples"
        "$DOCS_DIR/07-troubleshooting/solutions"
        "$ARCHIVE_DIR/$CURRENT_YEAR/analysis"
        "$ARCHIVE_DIR/$CURRENT_YEAR/reports"
        "$ARCHIVE_DIR/$CURRENT_YEAR/planning"
        "$ARCHIVE_DIR/$CURRENT_YEAR/bug-fixes"
        "$ARCHIVE_DIR/$CURRENT_YEAR/features"
        "$ARCHIVE_DIR/$CURRENT_YEAR/restructuring"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            if [ "$DRY_RUN" = true ]; then
                echo "  [DRY RUN] 将创建目录: $dir"
            else
                mkdir -p "$dir"
                [ "$VERBOSE" = true ] && echo "  创建目录: $dir"
            fi
        fi
    done
    
    success "目录结构准备完成"
}

# 检测过期文档（6个月未更新）
find_outdated_docs() {
    log "查找过期文档（超过6个月未更新）..."
    
    local outdated_count=0
    
    while IFS= read -r file; do
        if [[ "$file" == *"/archive/"* ]] || [[ "$file" == *"/03-development/"* ]]; then
            continue
        fi
        
        outdated_count=$((outdated_count + 1))
        [ "$VERBOSE" = true ] && warning "过期文档: $file"
    done < <(find "$DOCS_DIR" -name "*.md" -type f -mtime +180)
    
    if [ $outdated_count -gt 0 ]; then
        warning "发现 $outdated_count 个过期文档需要归档"
    else
        success "没有发现过期文档"
    fi
}

# 归档已完成的项目文档
archive_completed_docs() {
    log "归档已完成的项目文档..."
    
    # 定义需要归档的目录映射
    declare -A archive_map=(
        ["analysis"]="$ARCHIVE_DIR/$CURRENT_YEAR/analysis"
        ["reports"]="$ARCHIVE_DIR/$CURRENT_YEAR/reports"
        ["planning"]="$ARCHIVE_DIR/$CURRENT_YEAR/planning"
        ["bug-fixes"]="$ARCHIVE_DIR/$CURRENT_YEAR/bug-fixes"
        ["features"]="$ARCHIVE_DIR/$CURRENT_YEAR/features"
    )
    
    for src_dir in "${!archive_map[@]}"; do
        local src_path="$DOCS_DIR/$src_dir"
        local dst_path="${archive_map[$src_dir]}"
        
        if [ -d "$src_path" ]; then
            if [ "$DRY_RUN" = true ]; then
                echo "  [DRY RUN] 将归档: $src_path → $dst_path"
            else
                # 移动文件到归档目录
                find "$src_path" -name "*.md" -type f | while read -r file; do
                    local basename=$(basename "$file")
                    mv "$file" "$dst_path/$basename" 2>/dev/null || true
                    [ "$VERBOSE" = true ] && echo "  归档: $basename"
                done
                
                # 如果源目录为空，删除它
                rmdir "$src_path" 2>/dev/null || true
            fi
        fi
    done
    
    success "文档归档完成"
}

# 检查并修复断链
check_broken_links() {
    log "检查文档中的断链..."
    
    local broken_count=0
    local checked_count=0
    
    # 查找所有markdown文件
    while IFS= read -r file; do
        checked_count=$((checked_count + 1))
        
        # 提取文件中的相对链接
        grep -Eo '\[([^]]+)\]\(([^)]+)\)' "$file" 2>/dev/null | \
        grep -Eo '\]\([^)]+\)' | \
        sed 's/](\(.*\))/\1/' | \
        grep -E '^\.\./|^\.\/' | \
        while read -r link; do
            # 计算链接的实际路径
            local dir=$(dirname "$file")
            local target="$dir/$link"
            
            # 检查目标是否存在
            if [ ! -e "$target" ]; then
                broken_count=$((broken_count + 1))
                warning "断链: $file → $link"
                
                if [ "$DRY_RUN" = false ]; then
                    # TODO: 自动修复逻辑
                    :
                fi
            fi
        done
    done < <(find "$DOCS_DIR" -name "*.md" -type f)
    
    log "检查了 $checked_count 个文件"
    
    if [ $broken_count -gt 0 ]; then
        warning "发现 $broken_count 个断链"
    else
        success "没有发现断链"
    fi
}

# 生成文档索引
generate_doc_index() {
    log "生成文档索引..."
    
    local index_file="$DOCS_DIR/INDEX.md"
    
    if [ "$DRY_RUN" = true ]; then
        echo "  [DRY RUN] 将生成索引文件: $index_file"
        return
    fi
    
    cat > "$index_file" << EOF
# 文档索引

生成时间: $CURRENT_DATE

## 文档统计

EOF
    
    # 统计各目录的文档数量
    for dir in "$DOCS_DIR"/*; do
        if [ -d "$dir" ] && [[ "$(basename "$dir")" != "archive" ]]; then
            local count=$(find "$dir" -name "*.md" -type f | wc -l)
            echo "- $(basename "$dir"): $count 个文档" >> "$index_file"
        fi
    done
    
    echo "" >> "$index_file"
    echo "## 最近更新" >> "$index_file"
    echo "" >> "$index_file"
    
    # 列出最近更新的10个文档
    find "$DOCS_DIR" -name "*.md" -type f -mtime -30 | \
    grep -v "/archive/" | \
    head -10 | \
    while read -r file; do
        local relative_path=${file#$DOCS_DIR/}
        echo "- [$relative_path]($relative_path)" >> "$index_file"
    done
    
    success "文档索引已生成"
}

# 清理空目录
cleanup_empty_dirs() {
    log "清理空目录..."
    
    local removed_count=0
    
    # 查找并删除空目录
    find "$DOCS_DIR" -type d -empty | while read -r dir; do
        if [[ "$dir" != *"/archive/"* ]]; then
            if [ "$DRY_RUN" = true ]; then
                echo "  [DRY RUN] 将删除空目录: $dir"
            else
                rmdir "$dir" 2>/dev/null && removed_count=$((removed_count + 1))
                [ "$VERBOSE" = true ] && echo "  删除空目录: $dir"
            fi
        fi
    done
    
    if [ $removed_count -gt 0 ]; then
        success "清理了 $removed_count 个空目录"
    else
        success "没有发现空目录"
    fi
}

# 生成整理报告
generate_report() {
    log "生成文档整理报告..."
    
    local report_file="$DOCS_DIR/maintenance-report-$CURRENT_DATE.md"
    
    if [ "$DRY_RUN" = true ]; then
        echo "  [DRY RUN] 将生成报告: $report_file"
        return
    fi
    
    cat > "$report_file" << EOF
# 文档整理报告

日期: $CURRENT_DATE
执行模式: $([ "$MONTHLY_CLEANUP" = true ] && echo "月度深度清理" || echo "常规整理")

## 执行结果

### 文档统计
- 总文档数: $(find "$DOCS_DIR" -name "*.md" -type f | wc -l)
- 归档文档数: $(find "$ARCHIVE_DIR" -name "*.md" -type f | wc -l)

### 目录结构
\`\`\`
$(find "$DOCS_DIR" -type d | grep -v "/archive/" | sort)
\`\`\`

### 建议事项
1. 定期运行文档整理（建议每月一次）
2. 及时更新过期文档或归档
3. 保持文档命名规范一致
4. 定期检查并修复断链

---
*自动生成报告*
EOF
    
    success "报告已生成: $report_file"
}

# 月度深度清理
monthly_deep_cleanup() {
    log "执行月度深度清理..."
    
    # 1. 压缩超过2年的归档文档
    local old_year=$((CURRENT_YEAR - 2))
    if [ -d "$ARCHIVE_DIR/$old_year" ]; then
        if [ "$DRY_RUN" = true ]; then
            echo "  [DRY RUN] 将压缩: $ARCHIVE_DIR/$old_year"
        else
            tar -czf "$ARCHIVE_DIR/archive-$old_year.tar.gz" -C "$ARCHIVE_DIR" "$old_year"
            rm -rf "$ARCHIVE_DIR/$old_year"
            success "已压缩 $old_year 年的归档文档"
        fi
    fi
    
    # 2. 清理临时文件
    find "$DOCS_DIR" -name "*.tmp" -o -name "*.bak" -o -name "*~" | while read -r file; do
        if [ "$DRY_RUN" = true ]; then
            echo "  [DRY RUN] 将删除临时文件: $file"
        else
            rm -f "$file"
        fi
    done
    
    success "月度深度清理完成"
}

# 主函数
main() {
    log "开始文档整理..."
    
    if [ "$DRY_RUN" = true ]; then
        warning "运行在预览模式，不会执行实际操作"
    fi
    
    # 切换到项目根目录
    cd "$(dirname "$0")/.."
    
    # 执行整理步骤
    create_directory_structure
    find_outdated_docs
    archive_completed_docs
    check_broken_links
    cleanup_empty_dirs
    
    # 月度清理特有步骤
    if [ "$MONTHLY_CLEANUP" = true ]; then
        monthly_deep_cleanup
    fi
    
    # 生成索引和报告
    generate_doc_index
    generate_report
    
    success "文档整理完成！"
    
    if [ "$DRY_RUN" = true ]; then
        echo ""
        warning "这是预览模式的结果。使用不带 --dry-run 参数来执行实际操作。"
    fi
}

# 执行主函数
main