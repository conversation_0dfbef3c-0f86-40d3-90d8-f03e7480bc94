#!/bin/bash
# 个人开发者轻量级CI/CD设置脚本
# 一键安装，5分钟完成配置

set -e

echo "🚀 开始设置轻量级CI/CD环境..."
echo "================================"

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 1. 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p scripts
mkdir -p api-gateway/src/test/resources/snapshots

# 2. 安装Git Hooks
echo "🔧 安装Git Hooks..."

# Pre-commit hook - 快速检查
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# 轻量级提交前检查

echo "🔍 运行提交前检查..."

# 检查是否有Java文件修改
if git diff --cached --name-only | grep -q "\.java$"; then
    echo "📌 检查Java代码编译..."
    mvn compile -q -DskipTests || {
        echo "❌ Java编译失败，请修复错误后再提交"
        echo "💡 提示：运行 'mvn compile' 查看详细错误"
        exit 1
    }
fi

# 检查是否有JavaScript/TypeScript文件修改
if git diff --cached --name-only | grep -qE "\.(jsx?|tsx?)$"; then
    echo "📌 检查前端代码..."
    cd FinancialSystem-web
    
    # 只运行lint，不自动修复
    npm run lint --silent || {
        echo "❌ 前端代码检查失败"
        echo "💡 提示：运行 'npm run lint:fix' 自动修复"
        cd ..
        exit 1
    }
    cd ..
fi

echo "✅ 代码检查通过"
EOF

chmod +x .git/hooks/pre-commit

# Pre-push hook - 功能保护测试
cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash
# 推送前运行功能保护测试

echo "🚀 运行推送前验证..."
echo "这可能需要几分钟时间..."

# 运行功能保护测试
if [ -f "./scripts/run-protection-tests.sh" ]; then
    ./scripts/run-protection-tests.sh || {
        echo "❌ 功能保护测试失败，推送已取消"
        echo "💡 提示：检查是否意外修改了现有功能"
        exit 1
    }
else
    echo "⚠️ 功能保护测试脚本未找到，跳过测试"
fi

echo "✅ 所有测试通过，开始推送"
EOF

chmod +x .git/hooks/pre-push

# 3. 创建功能保护测试脚本
echo "📝 创建测试脚本..."

cat > scripts/run-protection-tests.sh << 'EOF'
#!/bin/bash
# 功能保护测试脚本 - 快速验证核心功能

set -e

echo "🛡️ 运行功能保护测试..."
echo "========================"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 快速启动测试（限时60秒）
echo ""
echo "1️⃣ 测试应用启动..."
if [ -f "./scripts/test-startup.sh" ]; then
    timeout 60 ./scripts/test-startup.sh > /dev/null 2>&1 || {
        echo "❌ 启动测试失败"
        echo "💡 运行 './scripts/test-startup.sh' 查看详细信息"
        exit 1
    }
    echo "✅ 启动测试通过"
else
    echo "⚠️ 启动测试脚本未找到，跳过"
fi

# 2. 运行功能保护测试（如果存在）
echo ""
echo "2️⃣ 测试核心功能..."
if mvn test -Dtest=FunctionProtectionTest -q > /dev/null 2>&1; then
    echo "✅ 功能保护测试通过"
else
    # 如果测试类不存在，运行基础测试
    echo "⚠️ 功能保护测试未找到，运行基础测试..."
    mvn test -q -DfailIfNoTests=false || {
        echo "❌ 测试失败"
        exit 1
    }
fi

# 3. 前端构建测试
echo ""
echo "3️⃣ 测试前端构建..."
cd FinancialSystem-web
if npm run build --silent > /dev/null 2>&1; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    echo "💡 运行 'npm run build' 查看详细错误"
    cd ..
    exit 1
fi
cd ..

# 计算总耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "========================"
echo "✅ 所有保护测试通过！"
echo "⏱️ 总耗时: ${DURATION}秒"
EOF

chmod +x scripts/run-protection-tests.sh

# 4. 创建日常维护脚本
echo "🔧 创建维护脚本..."

# 每日检查脚本
cat > scripts/daily-check.sh << 'EOF'
#!/bin/bash
# 每日健康检查脚本

echo "🏥 每日健康检查 - $(date)"
echo "=============================="

# 1. 检查应用是否运行
echo ""
echo "1️⃣ 应用状态检查:"
if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo "✅ 应用运行正常"
    curl -s http://localhost:8080/actuator/health | grep -o '"status":"[^"]*"' || echo "状态信息获取失败"
else
    echo "❌ 应用未运行或无法访问"
fi

# 2. 检查数据库连接
echo ""
echo "2️⃣ 数据库检查:"
if mysql -h localhost -u root -p123456 -e "SELECT 1" > /dev/null 2>&1; then
    echo "✅ 数据库连接正常"
    mysql -h localhost -u root -p123456 -e "SELECT COUNT(*) as '债权记录数' FROM overdue_debt_db.overdue_debt_add;" 2>/dev/null || echo "查询失败"
else
    echo "❌ 数据库连接失败"
fi

# 3. 检查磁盘空间
echo ""
echo "3️⃣ 磁盘空间:"
df -h | grep -E "(Filesystem|/$|/Volumes)" | awk '{print $5 "\t" $6}' | column -t

# 4. 检查最近错误
echo ""
echo "4️⃣ 最近的错误日志:"
if [ -f "logs/application.log" ]; then
    grep -i error logs/application.log | tail -3 || echo "没有发现错误"
else
    echo "日志文件未找到"
fi

echo ""
echo "=============================="
echo "检查完成！"
EOF

chmod +x scripts/daily-check.sh

# 问题诊断助手
cat > scripts/debug-helper.sh << 'EOF'
#!/bin/bash
# 快速问题诊断工具

echo "🔍 问题诊断助手"
echo "==============="

case "$1" in
    "startup")
        echo "检查启动问题..."
        echo ""
        echo "1. 检查端口占用:"
        lsof -i :8080 || echo "8080端口未被占用"
        echo ""
        echo "2. 检查Java进程:"
        ps aux | grep java | grep -v grep || echo "没有Java进程运行"
        echo ""
        echo "3. 尝试启动测试:"
        ./scripts/test-startup.sh
        ;;
    
    "api")
        echo "检查API问题..."
        echo ""
        echo "1. 健康检查:"
        curl -v http://localhost:8080/actuator/health
        echo ""
        echo "2. 测试登录接口:"
        curl -X POST http://localhost:8080/api/auth/login \
             -H "Content-Type: application/json" \
             -d '{"username":"admin","password":"admin123"}'
        ;;
    
    "db")
        echo "检查数据库问题..."
        echo ""
        echo "1. 测试连接:"
        mysql -h localhost -u root -p123456 -e "SELECT VERSION();" || echo "连接失败"
        echo ""
        echo "2. 查看进程列表:"
        mysql -h localhost -u root -p123456 -e "SHOW PROCESSLIST;" || echo "查询失败"
        ;;
    
    "test")
        echo "运行快速测试..."
        mvn test -Dtest=FunctionProtectionTest
        ;;
    
    *)
        echo "用法: $0 [startup|api|db|test]"
        echo ""
        echo "  startup - 检查启动问题"
        echo "  api     - 检查API问题"
        echo "  db      - 检查数据库问题"
        echo "  test    - 运行功能保护测试"
        ;;
esac
EOF

chmod +x scripts/debug-helper.sh

# 5. 创建测试报告脚本
cat > scripts/test-report.sh << 'EOF'
#!/bin/bash
# 生成简单的测试报告

echo "📊 测试报告"
echo "生成时间: $(date)"
echo "========================================"

# Java测试
echo ""
echo "🔹 后端测试结果:"
mvn test -q 2>&1 | grep -E "(Tests run:|ERROR|SUCCESS)" | tail -10

# 前端测试
echo ""
echo "🔹 前端测试结果:"
cd FinancialSystem-web
npm test -- --watchAll=false 2>&1 | grep -E "(Test Suites:|Tests:|PASS|FAIL)" | head -5
cd ..

echo ""
echo "========================================"
echo "报告生成完成"
EOF

chmod +x scripts/test-report.sh

# 6. 完成提示
echo ""
echo "✅ CI/CD环境设置完成！"
echo "================================"
echo ""
echo "🎯 快速开始指南:"
echo ""
echo "1. 测试Git Hooks是否工作:"
echo "   git add . && git commit -m 'test: 测试CI/CD'"
echo ""
echo "2. 运行功能保护测试:"
echo "   ./scripts/run-protection-tests.sh"
echo ""
echo "3. 每日健康检查:"
echo "   ./scripts/daily-check.sh"
echo ""
echo "4. 遇到问题时:"
echo "   ./scripts/debug-helper.sh [startup|api|db|test]"
echo ""
echo "💡 提示："
echo "- Git hooks已自动安装，每次提交和推送都会运行检查"
echo "- 如需临时跳过检查，使用: git commit --no-verify"
echo "- 建议将daily-check.sh加入cron定时任务"
echo ""
echo "祝您编码愉快！🚀"
EOF