<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>堆叠图表测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .data-preview {
            background-color: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>堆叠柱状图组件测试</h1>
        
        <div class="status success">
            ✅ 后端API已修复字段映射问题
        </div>
        
        <div class="status info">
            📊 前端组件已更新为堆叠图表样式
        </div>
        
        <div class="test-results">
            <h3>测试结果</h3>
            <div id="test-output">正在测试API...</div>
        </div>
        
        <div class="test-results">
            <h3>API数据预览</h3>
            <div class="data-preview" id="data-preview">正在加载数据...</div>
        </div>
        
        <div class="test-results">
            <h3>图表效果说明</h3>
            <ul>
                <li><strong>堆叠柱状图</strong>：每个公司一个柱子</li>
                <li><strong>柱子总高度</strong>：清收目标金额</li>
                <li><strong>绿色部分</strong>：累计清收金额</li>
                <li><strong>红色部分</strong>：未完成金额</li>
                <li><strong>绿色折线</strong>：完成进度百分比</li>
            </ul>
        </div>
        
        <div class="status info">
            💡 请访问 <a href="http://localhost:3000">http://localhost:3000</a> 查看实际效果
        </div>
    </div>

    <script>
        // 测试API数据
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8080/api/debts/statistics/company-progress?year=2025&month=6%E6%9C%88');
                const data = await response.json();
                
                document.getElementById('test-output').innerHTML = `
                    <strong>API测试成功！</strong><br>
                    📊 返回数据条数: ${data.length}<br>
                    📋 字段检查: 
                    ${['companyName', 'yearEndAmount', 'cumulativeRecovery', 'completionRate', 'collectionTargetAmount', 'hasTarget'].map(field => 
                        data[0][field] !== undefined ? `✅ ${field}` : `❌ ${field}`
                    ).join('<br>')}<br>
                    🎯 示例数据: ${data[0].companyName} - 目标${data[0].collectionTargetAmount}万，完成${data[0].completionRate}%
                `;
                
                // 显示前3条数据预览
                document.getElementById('data-preview').textContent = JSON.stringify(data.slice(0, 3), null, 2);
                
            } catch (error) {
                document.getElementById('test-output').innerHTML = `
                    <strong style="color: red;">API测试失败！</strong><br>
                    错误: ${error.message}
                `;
                document.getElementById('data-preview').textContent = '无法加载数据';
            }
        }
        
        // 页面加载后测试API
        window.onload = testAPI;
    </script>
</body>
</html>