{"scenario": {"name": "小规模负载测试", "description": "1000条记录的性能测试数据集，用于基准性能测试", "category": "performance", "expectedBehavior": "查询响应时间<100ms，批量导入<5s"}, "dataGenerator": {"recordCount": 1000, "distribution": {"creditors": ["中筑天佑", "万润智慧", "万融智达", "天恒永道", "金控投资"], "years": [2023, 2024, 2025], "amountRange": {"min": 1000.0, "max": 1000000.0}, "litigationRatio": 0.3, "disposalRatio": 0.4, "impairmentRatio": 0.6}, "generationRules": {"debtRecords": {"pattern": "PERF_SMALL_{index}", "fields": {"creditor": "random(creditors)", "debtor": "债务人_{index}", "amount": "random(amountRange)", "debtDate": "random(years)-random(01-12)-random(01-28)", "debtNature": "random(['普通债权', '担保债权', '抵押债权'])", "isLitigation": "probability(litigationRatio)", "subject": "其他应收款"}}, "disposalRecords": {"count": "recordCount * disposalRatio", "fields": {"debtRecordId": "random(debtRecords.id)", "disposalDate": "debtDate + random(30-365)days", "disposalMethod": "random(['现金清收', '资产抵债', '债务重组', '分期清收'])", "disposalAmount": "debtAmount * random(0.1-0.9)"}}, "impairmentRecords": {"count": "recordCount * impairmentRatio", "fields": {"debtRecordId": "random(debtRecords.id)", "impairmentRate": "random(5-50)", "recordDate": "lastDayOfMonth(debtDate)"}}}}, "performanceMetrics": {"targets": {"batchImport": {"maxDuration": 5000, "unit": "ms"}, "simpleQuery": {"maxDuration": 100, "unit": "ms"}, "complexQuery": {"maxDuration": 500, "unit": "ms"}, "statisticsCalculation": {"maxDuration": 1000, "unit": "ms"}, "excelExport": {"maxDuration": 3000, "unit": "ms"}}, "testCases": [{"name": "批量导入测试", "operation": "batchImport", "dataSize": 1000}, {"name": "简单查询测试", "operation": "simpleQuery", "query": "SELECT * FROM debt_records WHERE creditor = ?"}, {"name": "复杂查询测试", "operation": "<PERSON><PERSON><PERSON><PERSON>", "query": "多表关联查询with统计"}, {"name": "统计计算测试", "operation": "statisticsCalculation", "type": "月度汇总统计"}, {"name": "Excel导出测试", "operation": "excelExport", "recordCount": 1000}]}, "validationRules": {"dataIntegrity": {"checkUniqueIds": true, "checkReferentialIntegrity": true, "checkDataConsistency": true}, "performanceBaseline": {"avgResponseTime": 50, "p95ResponseTime": 100, "p99ResponseTime": 200, "throughput": 1000}}}