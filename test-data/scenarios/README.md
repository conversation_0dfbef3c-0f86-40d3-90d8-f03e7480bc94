# 场景测试数据集

本目录包含各种业务场景的测试数据，用于验证系统在不同业务场景下的正确性。

## 目录结构

```
scenarios/
├── boundary-cases/          # 边界条件测试数据
│   ├── extreme-values.json  # 极限值测试
│   ├── zero-values.json     # 零值测试
│   └── max-limits.json      # 最大限制测试
├── exception-cases/         # 异常场景测试数据
│   ├── invalid-data.json    # 无效数据
│   ├── constraint-violations.json  # 约束违反
│   └── edge-cases.json      # 边缘情况
├── business-scenarios/      # 业务场景测试数据
│   ├── high-volume.json     # 大批量处理
│   ├── complex-disposal.json # 复杂处置场景
│   └── year-end-closing.json # 年末结账场景
└── performance/             # 性能测试数据
    ├── load-test-small.json # 小规模负载测试
    ├── load-test-medium.json # 中等规模负载测试
    └── load-test-large.json  # 大规模负载测试
```

## 数据集说明

### 1. 边界条件测试数据 (boundary-cases)
- **extreme-values.json**: 包含极限值的测试数据，如最大金额、最小金额等
- **zero-values.json**: 包含零值的测试数据，测试系统对零值的处理
- **max-limits.json**: 测试系统的最大处理限制

### 2. 异常场景测试数据 (exception-cases)
- **invalid-data.json**: 包含各种无效数据格式，测试系统的容错性
- **constraint-violations.json**: 违反业务约束的数据，测试约束验证
- **edge-cases.json**: 各种边缘情况的组合

### 3. 业务场景测试数据 (business-scenarios)
- **high-volume.json**: 大批量债权处理场景
- **complex-disposal.json**: 复杂的债权处置场景（混合处置方式）
- **year-end-closing.json**: 年末结账的特殊场景

### 4. 性能测试数据 (performance)
- **load-test-small.json**: 1000条记录的性能测试数据
- **load-test-medium.json**: 10000条记录的性能测试数据
- **load-test-large.json**: 100000条记录的性能测试数据

## 使用方法

```java
// 加载场景测试数据
TestDataLoader loader = new TestDataLoader();
ScenarioData data = loader.loadScenario("boundary-cases/extreme-values.json");

// 执行测试
scenarioTest.execute(data);
```

## 数据格式

所有测试数据文件均采用JSON格式，包含以下基本结构：

```json
{
  "scenario": {
    "name": "场景名称",
    "description": "场景描述",
    "category": "场景分类",
    "expectedBehavior": "预期行为"
  },
  "data": {
    // 具体的测试数据
  },
  "assertions": [
    // 验证断言
  ]
}
```

## 维护说明

1. 新增场景时，请在相应的分类目录下创建JSON文件
2. 确保每个场景都有清晰的说明和预期行为描述
3. 定期更新测试数据以覆盖新的业务需求
4. 保持数据的版本控制，便于追踪变更

---
*创建时间: 2025-07-24*
*维护者: Claude*