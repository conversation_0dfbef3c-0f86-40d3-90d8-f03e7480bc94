{"scenario": {"name": "无效数据测试", "description": "测试系统对各种无效数据的处理和验证能力", "category": "exception-cases", "expectedBehavior": "系统应当拒绝无效数据并返回明确的错误信息"}, "data": {"invalidDebtRecords": [{"testCase": "NEGATIVE_AMOUNT", "description": "负金额测试", "data": {"creditor": "测试债权人", "debtor": "测试债务人", "amount": -1000.0, "debtDate": "2025-01-01", "debtNature": "普通债权"}, "expectedError": {"code": "INVALID_AMOUNT", "message": "债权金额不能为负数"}}, {"testCase": "FUTURE_DATE", "description": "未来日期测试", "data": {"creditor": "测试债权人", "debtor": "测试债务人", "amount": 10000.0, "debtDate": "2099-12-31", "debtNature": "普通债权"}, "expectedError": {"code": "INVALID_DATE", "message": "债权日期不能是未来日期"}}, {"testCase": "EMPTY_REQUIRED_FIELDS", "description": "必填字段为空", "data": {"creditor": "", "debtor": "", "amount": 10000.0, "debtDate": "2025-01-01", "debtNature": "普通债权"}, "expectedError": {"code": "REQUIRED_FIELD_EMPTY", "message": "债权人和债务人不能为空"}}, {"testCase": "INVALID_AMOUNT_FORMAT", "description": "金额格式错误", "data": {"creditor": "测试债权人", "debtor": "测试债务人", "amount": "abc123", "debtDate": "2025-01-01", "debtNature": "普通债权"}, "expectedError": {"code": "INVALID_FORMAT", "message": "金额格式不正确"}}, {"testCase": "EXCESSIVE_PRECISION", "description": "超过精度限制", "data": {"creditor": "测试债权人", "debtor": "测试债务人", "amount": 1000.999, "debtDate": "2025-01-01", "debtNature": "普通债权"}, "expectedError": {"code": "PRECISION_ERROR", "message": "金额精度不能超过2位小数"}}], "invalidDisposalRecords": [{"testCase": "DISPOSAL_EXCEEDS_DEBT", "description": "处置金额超过债权金额", "debtAmount": 10000.0, "data": {"debtRecordId": "TEST_DEBT_001", "disposalDate": "2025-06-30", "disposalMethod": "现金清收", "disposalAmount": 15000.0}, "expectedError": {"code": "DISPOSAL_EXCEEDS_DEBT", "message": "处置金额不能超过债权金额"}}, {"testCase": "DISPOSAL_BEFORE_DEBT", "description": "处置日期早于债权日期", "debtDate": "2025-01-01", "data": {"debtRecordId": "TEST_DEBT_002", "disposalDate": "2024-12-31", "disposalMethod": "现金清收", "disposalAmount": 5000.0}, "expectedError": {"code": "INVALID_DISPOSAL_DATE", "message": "处置日期不能早于债权日期"}}, {"testCase": "INVALID_DISPOSAL_METHOD", "description": "无效的处置方式", "data": {"debtRecordId": "TEST_DEBT_003", "disposalDate": "2025-06-30", "disposalMethod": "未知方式", "disposalAmount": 5000.0}, "expectedError": {"code": "INVALID_DISPOSAL_METHOD", "message": "处置方式不在允许的范围内"}}], "invalidImpairmentRecords": [{"testCase": "IMPAIRMENT_RATE_OVER_100", "description": "减值率超过100%", "data": {"debtRecordId": "TEST_DEBT_004", "impairmentRate": 150.0, "recordDate": "2025-01-31"}, "expectedError": {"code": "INVALID_IMPAIRMENT_RATE", "message": "减值率不能超过100%"}}, {"testCase": "NEGATIVE_IMPAIRMENT_RATE", "description": "负减值率", "data": {"debtRecordId": "TEST_DEBT_005", "impairmentRate": -10.0, "recordDate": "2025-01-31"}, "expectedError": {"code": "INVALID_IMPAIRMENT_RATE", "message": "减值率不能为负数"}}]}, "assertions": [{"type": "validation_enforcement", "description": "所有无效数据都应被拒绝", "expectAllRejected": true}, {"type": "error_message_clarity", "description": "错误信息应当清晰明确", "requireErrorCode": true, "requireErrorMessage": true}, {"type": "no_partial_save", "description": "无效数据不应被部分保存", "transactional": true}]}