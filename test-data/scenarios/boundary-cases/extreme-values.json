{"scenario": {"name": "极限值测试", "description": "测试系统对极限值的处理能力，包括最大金额、最小金额、最长字符串等", "category": "boundary-cases", "expectedBehavior": "系统应当正确处理所有极限值，不出现溢出或截断"}, "data": {"debtRecords": [{"id": "EXTREME_MAX_AMOUNT", "creditor": "极限测试债权人-最大金额", "debtor": "极限测试债务人-最大金额", "amount": 999999999999.99, "description": "测试最大金额处理", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": false, "subject": "其他应收款"}, {"id": "EXTREME_MIN_AMOUNT", "creditor": "极限测试债权人-最小金额", "debtor": "极限测试债务人-最小金额", "amount": 0.01, "description": "测试最小金额处理", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": false, "subject": "其他应收款"}, {"id": "EXTREME_LONG_STRING", "creditor": "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的债权人名称用于测试系统对长字符串的处理能力确保不会出现截断或显示问题", "debtor": "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的债务人名称用于测试系统对长字符串的处理能力确保不会出现截断或显示问题", "amount": 10000.0, "description": "测试超长字符串处理", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": false, "subject": "其他应收款"}, {"id": "EXTREME_PRECISION", "creditor": "极限测试债权人-精度测试", "debtor": "极限测试债务人-精度测试", "amount": 123456789.99, "description": "测试金额精度处理", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": true, "subject": "其他应收款", "monthlyAmounts": {"1": 10288899.16, "2": 10288899.16, "3": 10288899.16, "4": 10288899.16, "5": 10288899.16, "6": 10288899.16, "7": 10288899.16, "8": 10288899.16, "9": 10288899.16, "10": 10288899.16, "11": 10288899.16, "12": 10288899.19}}], "disposalRecords": [{"id": "DISPOSAL_MAX_AMOUNT", "debtRecordId": "EXTREME_MAX_AMOUNT", "disposalDate": "2025-06-30", "disposalMethod": "现金清收", "disposalAmount": 999999999999.99, "description": "最大金额处置测试"}, {"id": "DISPOSAL_MIN_AMOUNT", "debtRecordId": "EXTREME_MIN_AMOUNT", "disposalDate": "2025-06-30", "disposalMethod": "现金清收", "disposalAmount": 0.01, "description": "最小金额处置测试"}], "impairmentRecords": [{"id": "IMPAIRMENT_MAX_RATE", "debtRecordId": "EXTREME_MAX_AMOUNT", "impairmentRate": 100.0, "impairmentAmount": 999999999999.99, "recordDate": "2025-01-31", "description": "100%减值准备测试"}, {"id": "IMPAIRMENT_MIN_RATE", "debtRecordId": "EXTREME_MIN_AMOUNT", "impairmentRate": 0.01, "impairmentAmount": 0.01, "recordDate": "2025-01-31", "description": "最小减值率测试"}]}, "assertions": [{"type": "amount_precision", "description": "所有金额应保持2位小数精度", "field": "amount", "expectedPrecision": 2}, {"type": "string_length", "description": "超长字符串应完整保存", "field": "creditor", "minLength": 200}, {"type": "calculation_accuracy", "description": "月度金额累加应等于总金额", "tolerance": 0.01}, {"type": "no_overflow", "description": "大金额计算不应溢出", "maxValue": 999999999999.99}]}