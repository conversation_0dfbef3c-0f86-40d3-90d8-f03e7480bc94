{"scenario": {"name": "零值测试", "description": "测试系统对零值的处理，包括零金额债权、零减值、零处置等", "category": "boundary-cases", "expectedBehavior": "系统应当正确处理零值，不影响统计和计算"}, "data": {"debtRecords": [{"id": "ZERO_AMOUNT_DEBT", "creditor": "零值测试债权人", "debtor": "零值测试债务人", "amount": 0.0, "description": "零金额债权测试", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": false, "subject": "其他应收款", "monthlyAmounts": {"1": 0.0, "2": 0.0, "3": 0.0, "4": 0.0, "5": 0.0, "6": 0.0, "7": 0.0, "8": 0.0, "9": 0.0, "10": 0.0, "11": 0.0, "12": 0.0}}, {"id": "NORMAL_DEBT_FOR_ZERO_DISPOSAL", "creditor": "正常债权人", "debtor": "正常债务人", "amount": 50000.0, "description": "用于零处置测试的正常债权", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": true, "subject": "其他应收款"}, {"id": "NORMAL_DEBT_FOR_ZERO_IMPAIRMENT", "creditor": "正常债权人2", "debtor": "正常债务人2", "amount": 100000.0, "description": "用于零减值测试的正常债权", "debtDate": "2025-01-01", "debtNature": "普通债权", "isLitigation": false, "subject": "其他应收款"}], "disposalRecords": [{"id": "ZERO_DISPOSAL", "debtRecordId": "NORMAL_DEBT_FOR_ZERO_DISPOSAL", "disposalDate": "2025-06-30", "disposalMethod": "债务重组", "disposalAmount": 0.0, "description": "零金额处置（如债务豁免）"}, {"id": "DISPOSAL_OF_ZERO_DEBT", "debtRecordId": "ZERO_AMOUNT_DEBT", "disposalDate": "2025-06-30", "disposalMethod": "核销", "disposalAmount": 0.0, "description": "零债权的处置"}], "impairmentRecords": [{"id": "ZERO_IMPAIRMENT", "debtRecordId": "NORMAL_DEBT_FOR_ZERO_IMPAIRMENT", "impairmentRate": 0.0, "impairmentAmount": 0.0, "recordDate": "2025-01-31", "description": "零减值准备（优质债权）"}, {"id": "IMPAIRMENT_OF_ZERO_DEBT", "debtRecordId": "ZERO_AMOUNT_DEBT", "impairmentRate": 50.0, "impairmentAmount": 0.0, "recordDate": "2025-01-31", "description": "零债权的减值准备"}], "statistics": {"expectedTotalDebt": 150000.0, "expectedTotalDisposal": 0.0, "expectedTotalImpairment": 0.0, "expectedNetDebt": 150000.0}}, "assertions": [{"type": "zero_handling", "description": "零值不应导致除零错误", "field": "amount", "operation": "division"}, {"type": "statistics_accuracy", "description": "零值应正确参与统计计算", "includeZeros": true}, {"type": "display_format", "description": "零值应显示为0.00而不是空", "format": "0.00"}, {"type": "calculation_consistency", "description": "包含零值的计算应保持一致性", "tolerance": 0.0}]}