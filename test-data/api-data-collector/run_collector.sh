#!/bin/bash

# API数据采集器启动脚本
# 使用方法：
#   ./run_collector.sh              # 完整采集
#   ./run_collector.sh debt         # 采集债权模块
#   ./run_collector.sh --help       # 显示帮助

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，请先安装Python3"
        exit 1
    fi
    
    print_info "Python版本: $(python3 --version)"
}

# 检查依赖包
check_dependencies() {
    print_info "检查依赖包..."
    
    if [ -f "requirements.txt" ]; then
        # 检查是否需要安装依赖
        python3 -c "
import pkg_resources
import sys

try:
    with open('requirements.txt', 'r') as f:
        requirements = f.read().splitlines()
    
    missing = []
    for requirement in requirements:
        if requirement.strip() and not requirement.startswith('#'):
            try:
                pkg_resources.require(requirement)
            except (pkg_resources.DistributionNotFound, pkg_resources.VersionConflict):
                missing.append(requirement)
    
    if missing:
        print('Missing packages:', ', '.join(missing))
        sys.exit(1)
    else:
        print('All dependencies satisfied')
        sys.exit(0)

except Exception as e:
    print(f'Error checking dependencies: {e}')
    sys.exit(1)
" 2>/dev/null

        if [ $? -ne 0 ]; then
            print_warning "发现缺失的依赖包，正在安装..."
            python3 -m pip install -r requirements.txt
            
            if [ $? -ne 0 ]; then
                print_error "依赖包安装失败"
                exit 1
            fi
            print_success "依赖包安装完成"
        else
            print_success "所有依赖包已满足"
        fi
    else
        print_warning "未找到requirements.txt文件"
    fi
}

# 检查配置文件
check_config() {
    print_info "检查配置文件..."
    
    local config_files=("config/api-endpoints.json" "config/auth-config.json" "config/collector-config.json")
    local missing_files=()
    
    for config_file in "${config_files[@]}"; do
        if [ ! -f "$config_file" ]; then
            missing_files+=("$config_file")
        fi
    done
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        print_error "缺失配置文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    print_success "配置文件检查完成"
}

# 创建输出目录
setup_output_dir() {
    print_info "创建输出目录..."
    
    local output_dirs=("output" "output/snapshots" "output/schemas" "output/test-data")
    
    for dir in "${output_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    print_success "输出目录准备完成"
}

# 测试服务器连接
test_connection() {
    print_info "测试服务器连接..."
    
    python3 scripts/auth_handler.py > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "服务器连接正常"
    else
        print_warning "服务器连接测试失败，请检查配置"
        print_info "尝试继续执行采集..."
    fi
}

# 运行采集器
run_collector() {
    print_info "开始API数据采集..."
    
    local args=()
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                python3 scripts/collect_api_data.py --help
                exit 0
                ;;
            --module|-m)
                args+=("--module" "$2")
                shift 2
                ;;
            --endpoint|-e)
                args+=("--endpoint" "$2")
                shift 2
                ;;
            --mode)
                args+=("--mode" "$2")
                shift 2
                ;;
            --config-dir)
                args+=("--config-dir" "$2")
                shift 2
                ;;
            *)
                # 如果是单个参数且不以--开头，假设是模块名
                if [[ ! "$1" =~ ^-- ]] && [[ ${#args[@]} -eq 0 ]]; then
                    args+=("--module" "$1")
                else
                    args+=("$1")
                fi
                shift
                ;;
        esac
    done
    
    # 执行采集
    python3 scripts/collect_api_data.py "${args[@]}"
    
    if [ $? -eq 0 ]; then
        print_success "API数据采集完成！"
        print_info "查看结果:"
        print_info "  - 快照数据: output/snapshots/"
        print_info "  - 接口结构: output/schemas/"  
        print_info "  - 测试数据: output/test-data/"
        print_info "  - 日志文件: output/collector_*.log"
    else
        print_error "API数据采集失败"
        exit 1
    fi
}

# 显示使用帮助
show_usage() {
    echo "API数据采集器"
    echo ""
    echo "用法："
    echo "  $0                           # 完整采集所有接口"
    echo "  $0 debt                      # 采集债权模块"
    echo "  $0 --module auth             # 采集认证模块"
    echo "  $0 --endpoint /api/login     # 采集指定接口"
    echo "  $0 --mode incremental        # 增量采集模式"
    echo "  $0 --help                    # 显示详细帮助"
    echo ""
    echo "可用模块: auth, debt, export, system, monitoring, user"
    echo ""
    echo "注意事项："
    echo "  - 确保后端服务正在运行"
    echo "  - 检查config/auth-config.json中的认证信息"
    echo "  - 采集过程中保持网络连接稳定"
}

# 主函数
main() {
    echo "=========================================="
    echo "   FinancialSystem API数据采集器"
    echo "=========================================="
    echo ""
    
    # 检查是否需要显示帮助
    if [[ $# -eq 1 && ("$1" == "--help" || "$1" == "-h") ]]; then
        show_usage
        exit 0
    fi
    
    # 环境检查
    check_python
    check_dependencies
    check_config
    setup_output_dir
    test_connection
    
    echo ""
    
    # 运行采集器
    run_collector "$@"
}

# 执行主函数
main "$@"