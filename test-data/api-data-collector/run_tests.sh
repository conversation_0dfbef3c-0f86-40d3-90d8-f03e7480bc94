#!/bin/bash

# 统一测试管理器启动脚本
# 提供统一的接口来管理和执行各种测试任务

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${PURPLE}[HEADER]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo ""
    print_header "FinancialSystem 统一测试管理器"
    echo ""
    echo "用法："
    echo "  $0 [选项] [测试类型]"
    echo ""
    echo "测试类型："
    echo "  api              # API数据采集测试"
    echo "  performance      # 性能测试" 
    echo "  integration      # 集成测试"
    echo "  consistency      # 数据一致性测试"
    echo "  security         # 安全测试"
    echo "  all              # 运行所有测试"
    echo ""
    echo "选项："
    echo "  --module <名称>  # 指定API采集模块"
    echo "  --endpoint <路径># 指定API采集接口"
    echo "  --list           # 列出可用的测试类型"
    echo "  --status         # 查看测试状态"
    echo "  --report         # 查看最新报告"
    echo "  --help, -h       # 显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0 api                    # 采集所有API数据"
    echo "  $0 api --module debt      # 采集债权模块API"
    echo "  $0 performance           # 运行性能测试"
    echo "  $0 all                   # 运行完整测试套件"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_step "检查依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查后端服务
    if ! curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
        print_warning "后端服务似乎未启动，尝试连接..."
        sleep 2
        if ! curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
            print_error "无法连接到后端服务 (http://localhost:8080)"
            print_info "请确保后端服务正在运行"
            exit 1
        fi
    fi
    
    print_success "依赖检查完成"
}

# 运行测试
run_test() {
    local test_type="$1"
    shift
    local args=("$@")
    
    print_step "运行测试: $test_type"
    
    # 构建Python命令参数
    local python_args=()
    
    # 映射测试类型
    case "$test_type" in
        api)
            python_args+=("--test-type" "api_data_collection")
            ;;
        performance)
            python_args+=("--test-type" "performance_test")
            ;;
        integration)
            python_args+=("--test-type" "integration_test")
            ;;
        consistency)
            python_args+=("--test-type" "consistency_test")
            ;;
        security)
            python_args+=("--test-type" "security_test")
            ;;
        all)
            python_args+=("--test-type" "all_tests")
            ;;
        *)
            python_args+=("--test-type" "$test_type")
            ;;
    esac
    
    # 处理其他参数
    while [[ ${#args[@]} -gt 0 ]]; do
        case "${args[0]}" in
            --module)
                python_args+=("--module" "${args[1]}")
                args=("${args[@]:2}")
                ;;
            --endpoint)
                python_args+=("--endpoint" "${args[1]}")
                args=("${args[@]:2}")
                ;;
            *)
                args=("${args[@]:1}")
                ;;
        esac
    done
    
    # 执行测试
    print_info "执行命令: python3 scripts/test_manager.py ${python_args[*]}"
    
    if python3 scripts/test_manager.py "${python_args[@]}"; then
        print_success "测试执行完成: $test_type"
        
        # 显示结果位置
        print_info "查看结果:"
        if [[ -d "output" ]]; then
            local latest_session=$(ls -t output/session_* 2>/dev/null | head -1)
            if [[ -n "$latest_session" ]]; then
                print_info "  - 详细报告: $latest_session/test_report.html"
                print_info "  - JSON数据: $latest_session/test_report.json"
                print_info "  - 会话数据: $latest_session/test_session.json"
            fi
        fi
        
        return 0
    else
        print_error "测试执行失败: $test_type"
        return 1
    fi
}

# 列出测试类型
list_tests() {
    print_step "列出可用的测试类型..."
    python3 scripts/test_manager.py --list-tests
}

# 查看测试状态
show_status() {
    print_step "查看测试状态..."
    
    if [[ -d "output" ]]; then
        local sessions=($(ls -t output/session_* 2>/dev/null))
        if [[ ${#sessions[@]} -gt 0 ]]; then
            local latest_session="${sessions[0]}"
            print_info "最新测试会话: $(basename "$latest_session")"
            
            if [[ -f "$latest_session/test_session.json" ]]; then
                local status=$(python3 -c "
import json
try:
    with open('$latest_session/test_session.json', 'r') as f:
        data = json.load(f)
    print(f\"状态: {data.get('status', 'unknown')}\")
    print(f\"总测试数: {data.get('summary', {}).get('total', 0)}\")
    print(f\"完成: {data.get('summary', {}).get('completed', 0)}\")
    print(f\"失败: {data.get('summary', {}).get('failed', 0)}\")
except Exception as e:
    print(f'无法读取状态: {e}')
")
                echo "$status"
            fi
        else
            print_info "没有找到测试会话"
        fi
    else
        print_info "没有找到输出目录"
    fi
}

# 查看最新报告
show_report() {
    print_step "查看最新测试报告..."
    
    if [[ -d "output" ]]; then
        local latest_session=$(ls -t output/session_* 2>/dev/null | head -1)
        if [[ -n "$latest_session" ]]; then
            local html_report="$latest_session/test_report.html"
            if [[ -f "$html_report" ]]; then
                print_info "HTML报告位置: $html_report"
                
                # 尝试打开报告（macOS）
                if command -v open &> /dev/null; then
                    print_info "正在打开报告..."
                    open "$html_report"
                else
                    print_info "请手动打开报告文件"
                fi
            else
                print_warning "未找到HTML报告"
            fi
        else 
            print_warning "未找到测试会话"
        fi
    else
        print_warning "未找到输出目录"
    fi
}

# 清理旧结果
cleanup_old_results() {
    print_step "清理旧的测试结果..."
    
    if [[ -d "output" ]]; then
        # 保留最新的10个会话
        local sessions=($(ls -t output/session_* 2>/dev/null))
        if [[ ${#sessions[@]} -gt 10 ]]; then
            local to_remove=("${sessions[@]:10}")
            for session in "${to_remove[@]}"; do
                print_info "删除旧会话: $(basename "$session")"
                rm -rf "$session"
            done
            print_success "清理完成，删除了 ${#to_remove[@]} 个旧会话"
        else
            print_info "没有需要清理的旧结果"
        fi
    fi
}

# 主函数
main() {
    # 显示标题
    echo ""
    print_header "=========================================="
    print_header "   FinancialSystem 统一测试管理器"
    print_header "=========================================="
    echo ""
    
    # 解析参数
    local test_type=""
    local args=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --list)
                check_dependencies
                list_tests
                exit 0
                ;;
            --status)
                show_status
                exit 0
                ;;
            --report)
                show_report
                exit 0
                ;;
            --cleanup)
                cleanup_old_results
                exit 0
                ;;
            api|performance|integration|consistency|security|all)
                test_type="$1"
                shift
                ;;
            *)
                args+=("$1")
                shift
                ;;
        esac
    done
    
    # 如果没有指定测试类型，显示帮助
    if [[ -z "$test_type" ]]; then
        show_help
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    echo ""
    
    # 运行测试
    if run_test "$test_type" "${args[@]}"; then
        echo ""
        print_success "✨ 测试执行成功！"
        
        # 提示查看结果
        echo ""
        print_info "接下来你可以："
        print_info "  $0 --status      # 查看测试状态"
        print_info "  $0 --report      # 查看详细报告"
        print_info "  $0 --cleanup     # 清理旧结果"
        
    else
        echo ""
        print_error "❌ 测试执行失败"
        exit 1
    fi
}

# 执行主函数
main "$@"