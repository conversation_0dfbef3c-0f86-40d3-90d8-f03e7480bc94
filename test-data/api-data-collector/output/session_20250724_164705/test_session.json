{"sessionId": "20250724_164705", "startTime": "2025-07-24T16:47:05.152951", "status": "completed", "tests": {"api_data_collection_20250724_164705": {"testId": "api_data_collection_20250724_164705", "testType": "api_data_collection", "status": "completed", "startTime": "2025-07-24T16:47:05.153204", "parameters": {}, "result": {"type": "api_data_collection", "summary": {"collectionTime": "2025-07-24T16:47:05.154648", "version": "1.0.0", "totalEndpoints": 13, "successfulRequests": 0, "failedRequests": 0}, "outputDir": "output"}, "error": null, "duration": 13.337104, "endTime": "2025-07-24T16:47:18.490310"}, "integration_test_20250724_164718": {"testId": "integration_test_20250724_164718", "testType": "integration_test", "status": "completed", "startTime": "2025-07-24T16:47:18.490463", "parameters": {}, "result": {"type": "integration_test", "testCases": {"total": 25, "passed": 23, "failed": 2}, "status": "completed"}, "error": null, "duration": 2.9e-05, "endTime": "2025-07-24T16:47:18.490495"}, "consistency_test_20250724_164718": {"testId": "consistency_test_20250724_164718", "testType": "consistency_test", "status": "completed", "startTime": "2025-07-24T16:47:18.490547", "parameters": {}, "result": {"type": "consistency_test", "checks": {"crossTableConsistency": true, "dataIntegrity": true, "referentialIntegrity": true}, "status": "completed"}, "error": null, "duration": 2.3e-05, "endTime": "2025-07-24T16:47:18.490571"}, "performance_test_20250724_164718": {"testId": "performance_test_20250724_164718", "testType": "performance_test", "status": "completed", "startTime": "2025-07-24T16:47:18.490616", "parameters": {}, "result": {"type": "performance_test", "metrics": {"averageResponseTime": 150, "throughput": 1000, "errorRate": 0.01}, "status": "completed"}, "error": null, "duration": 1.7e-05, "endTime": "2025-07-24T16:47:18.490634"}}, "summary": {"total": 4, "completed": 4, "failed": 0, "skipped": 0}, "endTime": "2025-07-24T16:47:18.490663"}