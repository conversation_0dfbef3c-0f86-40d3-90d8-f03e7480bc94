[{"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:19.627629"}, {"parameters": {"username": "user", "password": "user123"}, "timestamp": "2025-07-24T16:46:19.905664", "responseTime": 194.04, "response": {"status": 401, "headers": {"Vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "0", "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate", "Pragma": "no-cache", "Expires": "0", "X-Frame-Options": "DENY", "Content-Type": "application/json", "Transfer-Encoding": "chunked", "Date": "Thu, 24 Jul 2025 08:46:19 GMT", "Keep-Alive": "timeout=60", "Connection": "keep-alive"}, "size": 40, "data": {"error": "Invalid username or password"}}, "validation": {"valid": true, "errors": [], "warnings": ["响应包含错误信息: Invalid username or password"], "metadata": {"type": "object", "keys": ["error"]}}, "responseSize": 40, "warning": "HTTP状态码: 401"}, {"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:21.644053"}, {"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:21.644603"}, {"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:28.812532"}, {"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:28.812718"}, {"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:30.828525"}, {"parameters": {}, "error": "请求异常: Expecting value: line 1 column 1 (char 0)", "timestamp": "2025-07-24T16:46:30.828660"}]