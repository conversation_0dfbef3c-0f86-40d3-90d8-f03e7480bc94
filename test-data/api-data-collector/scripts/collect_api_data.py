#!/usr/bin/env python3
"""
API数据采集器主程序
自动采集FinancialSystem所有API接口的数据并保存为测试数据集
"""

import json
import os
import sys
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 添加脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auth_handler import AuthHandler, AuthenticationError
from utils import (
    DataProcessor, FileManager, DataValidator, HashGenerator, 
    ProgressTracker, get_timestamp, format_file_size
)

class APIDataCollector:
    """API数据采集器"""
    
    def __init__(self, config_dir: str = "config"):
        """初始化采集器"""
        self.config_dir = config_dir
        self.config = self._load_config()
        self.endpoints_config = self._load_endpoints_config()
        self.auth_handler = AuthHandler(os.path.join(config_dir, "auth-config.json"))
        
        # 设置日志
        self._setup_logging()
        
        # 采集结果
        self.results = {
            'metadata': {
                'collectionTime': datetime.now().isoformat(),
                'version': '1.0.0',
                'totalEndpoints': 0,
                'successfulRequests': 0,
                'failedRequests': 0
            },
            'snapshots': {},
            'schemas': {},
            'errors': []
        }
        
        # 输出目录
        self.output_dir = "output"
        FileManager.ensure_directory(self.output_dir)
        FileManager.ensure_directory(os.path.join(self.output_dir, "snapshots"))
        FileManager.ensure_directory(os.path.join(self.output_dir, "schemas"))
        FileManager.ensure_directory(os.path.join(self.output_dir, "test-data"))
    
    def _load_config(self) -> Dict[str, Any]:
        """加载采集器配置"""
        config_path = os.path.join(self.config_dir, "collector-config.json")
        return FileManager.load_json(config_path) or {}
    
    def _load_endpoints_config(self) -> Dict[str, Any]:
        """加载接口配置"""
        config_path = os.path.join(self.config_dir, "api-endpoints.json")
        return FileManager.load_json(config_path) or {}
    
    def _setup_logging(self):
        """设置日志"""
        log_level = logging.INFO
        if self.config.get('reporting', {}).get('verboseLogging', False):
            log_level = logging.DEBUG
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'output/collector_{get_timestamp()}.log'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def collect_all(self) -> Dict[str, Any]:
        """采集所有接口数据"""
        self.logger.info("开始采集所有API接口数据")
        
        try:
            # 首先登录
            self.auth_handler.login()
            
            # 获取启用的模块
            enabled_modules = self._get_enabled_modules()
            
            # 计算总接口数
            total_endpoints = sum(
                len(module_config['endpoints']) 
                for module_config in enabled_modules.values()
            )
            
            self.results['metadata']['totalEndpoints'] = total_endpoints
            
            # 创建进度跟踪器
            progress = ProgressTracker(total_endpoints)
            
            # 按优先级顺序处理模块
            for module_name in sorted(enabled_modules.keys(), 
                                    key=lambda x: self.config.get('modules', {}).get(x, {}).get('priority', 999)):
                
                self.logger.info(f"开始采集模块: {module_name}")
                
                module_config = enabled_modules[module_name]
                module_results = self._collect_module(module_name, module_config, progress)
                
                # 保存模块结果
                self.results['snapshots'][module_name] = module_results
                
                self.logger.info(f"模块 {module_name} 采集完成")
            
            # 生成测试数据
            self._generate_test_data()
            
            # 保存结果
            self._save_results()
            
            # 生成报告
            self._generate_report()
            
            self.logger.info("所有API接口数据采集完成")
            
        except Exception as e:
            self.logger.error(f"采集过程异常: {str(e)}")
            self.results['errors'].append({
                'type': 'critical',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            })
        finally:
            # 登出
            self.auth_handler.logout()
        
        return self.results
    
    def collect_module(self, module_name: str) -> Dict[str, Any]:
        """采集指定模块的数据"""
        self.logger.info(f"开始采集模块: {module_name}")
        
        try:
            self.auth_handler.login()
            
            if module_name not in self.endpoints_config.get('endpoints', {}):
                raise ValueError(f"未找到模块配置: {module_name}")
            
            module_config = self.endpoints_config['endpoints'][module_name]
            
            # 计算模块接口数
            total_endpoints = len(module_config['endpoints'])
            progress = ProgressTracker(total_endpoints)
            
            # 采集模块数据
            results = self._collect_module(module_name, module_config, progress)
            
            # 保存单模块结果
            self._save_module_results(module_name, results)
            
            self.logger.info(f"模块 {module_name} 采集完成")
            
            return results
            
        except Exception as e:
            self.logger.error(f"采集模块 {module_name} 失败: {str(e)}")
            raise
        finally:
            self.auth_handler.logout()
    
    def collect_endpoint(self, endpoint_path: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """采集指定接口的数据"""
        self.logger.info(f"开始采集接口: {endpoint_path}")
        
        try:
            self.auth_handler.login()
            
            # 查找接口配置
            endpoint_config = self._find_endpoint_config(endpoint_path)
            if not endpoint_config:
                raise ValueError(f"未找到接口配置: {endpoint_path}")
            
            # 执行请求
            result = self._execute_request(endpoint_config, params or {})
            
            # 保存单接口结果
            timestamp = get_timestamp()
            filename = f"single_endpoint_{endpoint_path.replace('/', '_')}_{timestamp}.json"
            filepath = os.path.join(self.output_dir, "snapshots", filename)
            FileManager.save_json(result, filepath)
            
            self.logger.info(f"接口 {endpoint_path} 采集完成")
            
            return result
            
        except Exception as e:
            self.logger.error(f"采集接口 {endpoint_path} 失败: {str(e)}")
            raise
        finally:
            self.auth_handler.logout()
    
    def _get_enabled_modules(self) -> Dict[str, Any]:
        """获取启用的模块"""
        enabled_modules = {}
        
        for module_name, module_config in self.endpoints_config.get('endpoints', {}).items():
            if self.config.get('modules', {}).get(module_name, {}).get('enabled', True):
                enabled_modules[module_name] = module_config
        
        return enabled_modules
    
    def _collect_module(self, module_name: str, module_config: Dict[str, Any], progress: ProgressTracker) -> Dict[str, Any]:
        """采集单个模块的数据"""
        module_results = {
            'module': module_name,
            'description': module_config.get('module', ''),
            'baseUrl': module_config.get('baseUrl', ''),
            'endpoints': {},
            'summary': {
                'totalEndpoints': len(module_config['endpoints']),
                'successfulEndpoints': 0,
                'failedEndpoints': 0,
                'totalRequests': 0,
                'successfulRequests': 0,
                'failedRequests': 0
            }
        }
        
        # 并发采集接口数据
        max_workers = self.config.get('execution', {}).get('concurrency', 3)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_endpoint = {}
            
            for endpoint_config in module_config['endpoints']:
                future = executor.submit(self._collect_endpoint_data, endpoint_config, module_config)
                future_to_endpoint[future] = endpoint_config
            
            # 收集结果
            for future in as_completed(future_to_endpoint):
                endpoint_config = future_to_endpoint[future]
                endpoint_id = endpoint_config['id']
                
                try:
                    endpoint_result = future.result()
                    module_results['endpoints'][endpoint_id] = endpoint_result
                    
                    # 更新统计
                    module_results['summary']['successfulEndpoints'] += 1
                    module_results['summary']['totalRequests'] += endpoint_result['summary']['totalRequests']
                    module_results['summary']['successfulRequests'] += endpoint_result['summary']['successfulRequests']
                    module_results['summary']['failedRequests'] += endpoint_result['summary']['failedRequests']
                    
                    progress.update(1, has_error=False)
                    
                except Exception as e:
                    self.logger.error(f"采集接口 {endpoint_id} 失败: {str(e)}")
                    
                    module_results['endpoints'][endpoint_id] = {
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }
                    module_results['summary']['failedEndpoints'] += 1
                    
                    progress.update(1, has_error=True)
                
                # 显示进度
                progress.print_progress()
                
                # 速率限制
                rate_limit_delay = self.config.get('execution', {}).get('rateLimitDelay', 1.0)
                if rate_limit_delay > 0:
                    time.sleep(rate_limit_delay)
        
        return module_results
    
    def _collect_endpoint_data(self, endpoint_config: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """采集单个接口的数据"""
        endpoint_id = endpoint_config['id']
        
        endpoint_result = {
            'id': endpoint_id,
            'path': endpoint_config['path'],
            'method': endpoint_config['method'],
            'description': endpoint_config.get('description', ''),
            'testCases': [],
            'schema': None,
            'summary': {
                'totalRequests': 0,
                'successfulRequests': 0,
                'failedRequests': 0,
                'averageResponseTime': 0,
                'totalResponseSize': 0
            }
        }
        
        # 构建完整URL
        base_url = self.auth_handler.base_url
        module_base = module_config.get('baseUrl', '')
        endpoint_path = endpoint_config['path']  
        full_url = f"{base_url}{module_base}{endpoint_path}"
        
        # 执行测试用例
        test_cases = endpoint_config.get('testCases', [{}])
        response_times = []
        response_sizes = []
        schemas = []
        
        for i, test_case in enumerate(test_cases):
            try:
                case_result = self._execute_single_request(
                    full_url, 
                    endpoint_config['method'], 
                    test_case,
                    endpoint_config.get('requireAuth', True),
                    endpoint_config.get('responseType', 'json')
                )
                
                endpoint_result['testCases'].append(case_result)
                endpoint_result['summary']['successfulRequests'] += 1
                
                # 收集统计信息
                if case_result.get('responseTime'):
                    response_times.append(case_result['responseTime'])
                if case_result.get('responseSize'):
                    response_sizes.append(case_result['responseSize'])
                
                # 提取schema（仅对JSON响应）
                if case_result.get('response', {}).get('data') and endpoint_config.get('responseType', 'json') == 'json':
                    schema = DataProcessor.extract_schema(case_result['response']['data'])
                    schemas.append(schema)
                
            except Exception as e:
                self.logger.error(f"执行测试用例失败 {endpoint_id}[{i}]: {str(e)}")
                
                endpoint_result['testCases'].append({
                    'parameters': test_case,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
                endpoint_result['summary']['failedRequests'] += 1
            
            endpoint_result['summary']['totalRequests'] += 1
        
        # 计算汇总统计
        if response_times:
            endpoint_result['summary']['averageResponseTime'] = sum(response_times) / len(response_times)
        if response_sizes:
            endpoint_result['summary']['totalResponseSize'] = sum(response_sizes)
        
        # 合并schema
        if schemas:
            # 简单合并：使用第一个有效schema
            endpoint_result['schema'] = schemas[0]
        
        return endpoint_result
    
    def _execute_single_request(self, url: str, method: str, params: Dict[str, Any], 
                               require_auth: bool, response_type: str = 'json') -> Dict[str, Any]:
        """执行单个HTTP请求"""
        
        # 获取认证会话
        if require_auth:
            session = self.auth_handler.get_authenticated_session()
        else:
            session = requests.Session()
        
        # 准备请求参数
        request_kwargs = {
            'timeout': self.config.get('execution', {}).get('requestTimeout', 30)
        }
        
        # 根据请求方法设置参数
        if method.upper() == 'GET':
            request_kwargs['params'] = params
        elif method.upper() in ['POST', 'PUT', 'PATCH']:
            if params:
                request_kwargs['json'] = params
        
        # 执行请求
        start_time = time.time()
        
        try:
            response = session.request(method, url, **request_kwargs)
            end_time = time.time()
            
            response_time = round((end_time - start_time) * 1000, 2)  # 毫秒
            
            # 处理响应
            result = {
                'parameters': params,
                'timestamp': datetime.now().isoformat(),
                'responseTime': response_time,
                'response': {
                    'status': response.status_code,
                    'headers': dict(response.headers),
                    'size': len(response.content)
                }
            }
            
            # 根据响应类型处理数据
            if response_type == 'binary':
                # 二进制响应（如Excel文件）
                result['response']['dataType'] = 'binary'
                result['response']['contentType'] = response.headers.get('Content-Type', '')
                result['response']['filename'] = self._extract_filename(response.headers)
                # 不保存二进制内容，只记录元数据
            else:
                # JSON响应
                try:
                    data = response.json()
                    result['response']['data'] = DataProcessor.sanitize_data(
                        data, 
                        self.config.get('dataProcessing', {})
                    )
                    
                    # 验证响应
                    if self.config.get('execution', {}).get('validateResponses', True):
                        validation_result = DataValidator.validate_response(data)
                        result['validation'] = validation_result
                    
                except json.JSONDecodeError:
                    # 文本响应
                    result['response']['data'] = response.text[:1000]  # 截断长文本
                    result['response']['dataType'] = 'text'
            
            result['responseSize'] = result['response']['size']
            
            # 检查HTTP状态
            if response.status_code >= 400:
                result['warning'] = f"HTTP状态码: {response.status_code}"
            
            return result
            
        except requests.exceptions.Timeout:
            raise Exception("请求超时")
        except requests.exceptions.ConnectionError:
            raise Exception("连接错误")  
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求异常: {str(e)}")
    
    def _extract_filename(self, headers: Dict[str, str]) -> Optional[str]:
        """从响应头提取文件名"""
        content_disposition = headers.get('Content-Disposition', '')
        if 'filename=' in content_disposition:
            return content_disposition.split('filename=')[1].strip('"')
        return None
    
    def _find_endpoint_config(self, endpoint_path: str) -> Optional[Dict[str, Any]]:
        """查找接口配置"""
        for module_config in self.endpoints_config.get('endpoints', {}).values():
            for endpoint_config in module_config['endpoints']:
                if endpoint_config['path'] == endpoint_path:
                    return endpoint_config
        return None
    
    def _generate_test_data(self):
        """生成测试数据"""
        self.logger.info("生成测试数据集")
        
        test_data = {
            'normal': [],
            'boundary': [],
            'error': [],
            'performance': []
        }
        
        # 从采集的数据中提取测试数据
        for module_name, module_data in self.results['snapshots'].items():
            for endpoint_id, endpoint_data in module_data.get('endpoints', {}).items():
                
                # 正常场景数据
                successful_cases = [
                    case for case in endpoint_data.get('testCases', [])
                    if 'error' not in case and case.get('response', {}).get('status') == 200
                ]
                
                if successful_cases:
                    test_data['normal'].extend(successful_cases)
                
                # 错误场景数据
                error_cases = [
                    case for case in endpoint_data.get('testCases', [])
                    if 'error' in case or case.get('response', {}).get('status') >= 400
                ]
                
                if error_cases:
                    test_data['error'].extend(error_cases)
        
        # 保存测试数据
        timestamp = get_timestamp()
        for data_type, data_list in test_data.items():
            if data_list:
                filename = f"{data_type}_test_data_{timestamp}.json"
                filepath = os.path.join(self.output_dir, "test-data", filename)
                FileManager.save_json(data_list, filepath)
    
    def _save_results(self):
        """保存采集结果"""
        timestamp = get_timestamp()
        
        # 保存完整结果
        results_file = os.path.join(self.output_dir, f"collection_results_{timestamp}.json")
        FileManager.save_json(self.results, results_file)
        
        # 分别保存快照和schema
        snapshots_file = os.path.join(self.output_dir, "snapshots", f"api_snapshots_{timestamp}.json")
        FileManager.save_json(self.results['snapshots'], snapshots_file)
        
        schemas_file = os.path.join(self.output_dir, "schemas", f"api_schemas_{timestamp}.json")
        schemas = {}
        for module_name, module_data in self.results['snapshots'].items():
            schemas[module_name] = {}
            for endpoint_id, endpoint_data in module_data.get('endpoints', {}).items():
                if endpoint_data.get('schema'):
                    schemas[module_name][endpoint_id] = endpoint_data['schema']
        
        FileManager.save_json(schemas, schemas_file)
        
        self.logger.info(f"结果已保存到: {results_file}")
    
    def _save_module_results(self, module_name: str, results: Dict[str, Any]):
        """保存单模块结果"""
        timestamp = get_timestamp()
        filename = f"{module_name}_results_{timestamp}.json"
        filepath = os.path.join(self.output_dir, "snapshots", filename)
        FileManager.save_json(results, filepath)
    
    def _generate_report(self):
        """生成采集报告"""
        self.logger.info("生成采集报告")
        
        # 计算统计信息
        total_endpoints = self.results['metadata']['totalEndpoints']
        successful_requests = self.results['metadata']['successfulRequests']
        failed_requests = self.results['metadata']['failedRequests']
        
        # 生成报告
        report = {
            'title': 'API数据采集报告',
            'timestamp': self.results['metadata']['collectionTime'],
            'summary': {
                'totalEndpoints': total_endpoints,
                'totalRequests': successful_requests + failed_requests,
                'successfulRequests': successful_requests,
                'failedRequests': failed_requests,
                'successRate': round(successful_requests / (successful_requests + failed_requests) * 100, 2) if (successful_requests + failed_requests) > 0 else 0
            },
            'moduleDetails': {},
            'errors': self.results['errors']
        }
        
        # 添加模块详情
        for module_name, module_data in self.results['snapshots'].items():
            if isinstance(module_data, dict) and 'summary' in module_data:
                report['moduleDetails'][module_name] = module_data['summary']
        
        # 保存报告
        timestamp = get_timestamp()
        report_file = os.path.join(self.output_dir, f"collection_report_{timestamp}.json")
        FileManager.save_json(report, report_file)
        
        # 打印摘要
        print("\n" + "="*60)
        print("API数据采集完成")
        print("="*60)
        print(f"总接口数: {report['summary']['totalEndpoints']}")
        print(f"总请求数: {report['summary']['totalRequests']}")
        print(f"成功请求: {report['summary']['successfulRequests']}")
        print(f"失败请求: {report['summary']['failedRequests']}")
        print(f"成功率: {report['summary']['successRate']}%")
        print(f"报告文件: {report_file}")
        print("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='API数据采集器')
    parser.add_argument('--mode', choices=['full', 'incremental'], default='full',
                       help='采集模式: full=完整采集, incremental=增量采集')
    parser.add_argument('--module', type=str, help='指定采集的模块名称')
    parser.add_argument('--endpoint', type=str, help='指定采集的接口路径')
    parser.add_argument('--config-dir', type=str, default='config', help='配置文件目录')
    
    args = parser.parse_args()
    
    try:
        # 创建采集器
        collector = APIDataCollector(args.config_dir)
        
        # 根据参数执行不同的采集模式
        if args.endpoint:
            # 采集单个接口
            result = collector.collect_endpoint(args.endpoint)
            print(f"接口 {args.endpoint} 采集完成")
            
        elif args.module:
            # 采集指定模块
            result = collector.collect_module(args.module)
            print(f"模块 {args.module} 采集完成")
            
        else:
            # 完整采集
            result = collector.collect_all()
            print("所有接口采集完成")
        
    except KeyboardInterrupt:
        print("\n采集被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"采集失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()