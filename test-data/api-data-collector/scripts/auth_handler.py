#!/usr/bin/env python3
"""
认证处理器
负责处理JWT认证、token管理和会话维护
"""

import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import logging

logger = logging.getLogger(__name__)

class AuthenticationError(Exception):
    """认证异常"""
    pass

class AuthHandler:
    """认证处理器"""
    
    def __init__(self, config_path: str = "config/auth-config.json"):
        """初始化认证处理器"""
        self.config = self._load_config(config_path)
        self.session = requests.Session()
        self.token = None
        self.token_expires_at = None
        self.current_user = None
        
        # 配置会话
        self._setup_session()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载认证配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"认证配置文件未找到: {config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"认证配置文件格式错误: {e}")
    
    def _setup_session(self):
        """配置HTTP会话"""
        headers = self.config.get('request', {}).get('headers', {})
        self.session.headers.update(headers)
        
        # 设置超时
        timeout = self.config.get('request', {}).get('timeout', 30)
        self.session.timeout = timeout
        
        # 设置重试
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_config = self.config.get('request', {})
        retry_strategy = Retry(
            total=retry_config.get('retries', 3),
            backoff_factor=retry_config.get('retryDelay', 1),
            status_forcelist=[429, 500, 502, 503, 504]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    @property
    def base_url(self) -> str:
        """获取基础URL"""
        env = self.config.get('current', 'development')
        return self.config['environment'][env]['baseUrl']
    
    def login(self, username: str = None, password: str = None) -> Dict[str, Any]:
        """用户登录"""
        if not username or not password:
            # 使用默认用户
            default_user = self.config['credentials']['defaultUser']
            user_info = next(
                user for user in self.config['credentials']['testUsers'] 
                if user['username'] == default_user
            )
            username = user_info['username']
            password = user_info['password']
        
        login_url = self.base_url + self.config['authentication']['loginEndpoint']
        
        login_data = {
            'username': username,
            'password': password
        }
        
        logger.info(f"尝试登录用户: {username}")
        
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            data = response.json()
            
            # 提取token
            if 'token' in data:
                self.token = data['token']
            elif 'accessToken' in data:
                self.token = data['accessToken'] 
            elif 'jwt' in data:
                self.token = data['jwt']
            else:
                raise AuthenticationError("响应中未找到token")
            
            # 设置token过期时间
            expires_in = data.get('expiresIn', 3600)  # 默认1小时
            buffer = self.config['token'].get('expirationBuffer', 300)  # 5分钟缓冲
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - buffer)
            
            # 更新会话header
            token_header = f"{self.config['token']['headerPrefix']}{self.token}"
            self.session.headers[self.config['token']['headerName']] = token_header
            
            # 保存用户信息
            self.current_user = {
                'username': username,
                'loginTime': datetime.now().isoformat(),
                'role': data.get('role', 'USER')
            }
            
            logger.info(f"用户 {username} 登录成功")
            return data
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise AuthenticationError(f"登录失败: 用户名或密码错误")
            else:
                raise AuthenticationError(f"登录失败: HTTP {e.response.status_code}")
        except requests.exceptions.RequestException as e:
            raise AuthenticationError(f"登录失败: 网络错误 - {str(e)}")
        except Exception as e:
            raise AuthenticationError(f"登录失败: {str(e)}")
    
    def logout(self) -> bool:
        """用户登出"""
        if not self.token:
            logger.warning("用户未登录，无需登出")
            return True
        
        logout_url = self.base_url + self.config['authentication']['logoutEndpoint']
        
        try:
            response = self.session.post(logout_url)
            response.raise_for_status()
            
            # 清理认证信息
            self._clear_auth()
            
            logger.info("用户登出成功")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.warning(f"登出请求失败: {str(e)}, 继续清理本地认证信息")
            self._clear_auth()
            return False
    
    def _clear_auth(self):
        """清理认证信息"""
        self.token = None
        self.token_expires_at = None
        self.current_user = None
        
        # 清理会话header
        auth_header = self.config['token']['headerName']
        if auth_header in self.session.headers:
            del self.session.headers[auth_header]
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        if not self.token:
            return False
        
        if self.token_expires_at and datetime.now() >= self.token_expires_at:
            logger.info("Token已过期")
            return False
        
        return True
    
    def ensure_authenticated(self) -> bool:
        """确保已认证，如果需要则自动登录"""
        if self.is_authenticated():
            return True
        
        logger.info("需要重新认证")
        try:
            self.login()
            return True
        except AuthenticationError as e:
            logger.error(f"自动登录失败: {str(e)}")
            return False
    
    def get_authenticated_session(self) -> requests.Session:
        """获取已认证的会话"""
        if not self.ensure_authenticated():
            raise AuthenticationError("无法获取有效认证")
        
        return self.session
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        if not self.ensure_authenticated():
            return None
        
        user_info_url = self.base_url + self.config['authentication']['userInfoEndpoint']
        
        try:
            response = self.session.get(user_info_url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return self.current_user
    
    def test_connection(self) -> Dict[str, Any]:
        """测试连接和认证"""
        result = {
            'connection': False,
            'authentication': False,
            'userInfo': None,
            'error': None
        }
        
        try:
            # 测试连接
            response = self.session.get(self.base_url + '/actuator/health', timeout=5)
            if response.status_code in [200, 404]:  # 404也表示连接正常
                result['connection'] = True
            
            # 测试认证
            if self.ensure_authenticated():
                result['authentication'] = True
                result['userInfo'] = self.get_user_info()
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"连接测试失败: {str(e)}")
        
        return result
    
    def get_auth_status(self) -> Dict[str, Any]:
        """获取认证状态"""
        return {
            'isAuthenticated': self.is_authenticated(),
            'token': self.token[:20] + '...' if self.token else None,
            'tokenExpiresAt': self.token_expires_at.isoformat() if self.token_expires_at else None,
            'currentUser': self.current_user,
            'baseUrl': self.base_url
        }

def main():
    """测试认证处理器"""
    import sys
    import os
    
    # 添加父目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # 初始化日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # 创建认证处理器
        auth = AuthHandler()
        
        # 测试连接
        print("测试连接...")
        connection_result = auth.test_connection()
        print(f"连接结果: {json.dumps(connection_result, indent=2, ensure_ascii=False)}")
        
        # 获取认证状态
        print("\n认证状态:")
        status = auth.get_auth_status()
        print(f"{json.dumps(status, indent=2, ensure_ascii=False)}")
        
        # 登出
        auth.logout()
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()