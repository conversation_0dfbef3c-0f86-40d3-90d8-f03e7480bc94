#!/usr/bin/env python3
"""
工具函数模块
提供数据处理、文件操作、验证等通用功能
"""

import json
import os
import hashlib
import pandas as pd
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Any, Optional, Union
import logging
import gzip
import shutil

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器"""
    
    @staticmethod
    def sanitize_data(data: Any, config: Dict[str, Any] = None) -> Any:
        """清理和标准化数据"""
        if config is None:
            config = {
                'removeNullFields': False,
                'formatDates': True,
                'roundNumbers': 2,
                'maxStringLength': 1000
            }
        
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                # 跳过空值
                if config.get('removeNullFields', False) and value is None:
                    continue
                
                result[key] = DataProcessor.sanitize_data(value, config)
            return result
        
        elif isinstance(data, list):
            return [DataProcessor.sanitize_data(item, config) for item in data]
        
        elif isinstance(data, str):
            # 截断长字符串
            max_length = config.get('maxStringLength', 1000)
            if len(data) > max_length:
                return data[:max_length] + '...'
            return data
        
        elif isinstance(data, (int, float)):
            # 四舍五入数字
            round_digits = config.get('roundNumbers', 2)
            if isinstance(data, float) and round_digits is not None:
                return round(data, round_digits)
            return data
        
        elif isinstance(data, (datetime, date)):
            # 格式化日期
            if config.get('formatDates', True):
                return data.isoformat()
            return data
        
        elif isinstance(data, Decimal):
            return float(data)
        
        else:
            return data
    
    @staticmethod
    def extract_schema(data: Any, path: str = "") -> Dict[str, Any]:
        """提取数据结构模式"""
        if isinstance(data, dict):
            schema = {"type": "object", "properties": {}}
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                schema["properties"][key] = DataProcessor.extract_schema(value, current_path)
            return schema
        
        elif isinstance(data, list):
            if not data:
                return {"type": "array", "items": {"type": "unknown"}}
            
            # 分析数组中的元素类型
            item_schemas = [DataProcessor.extract_schema(item, path) for item in data[:5]]  # 只分析前5个
            
            # 如果所有元素类型相同，返回统一类型
            if all(schema == item_schemas[0] for schema in item_schemas):
                return {"type": "array", "items": item_schemas[0]}
            else:
                return {"type": "array", "items": {"type": "mixed"}}
        
        elif isinstance(data, str):
            return {"type": "string", "example": data[:50]}
        
        elif isinstance(data, bool):
            return {"type": "boolean"}
        
        elif isinstance(data, int):
            return {"type": "integer", "example": data}
        
        elif isinstance(data, float):
            return {"type": "number", "example": data}
        
        elif data is None:
            return {"type": "null"}
        
        else:
            return {"type": "unknown", "actualType": type(data).__name__}

class FileManager:
    """文件管理器"""
    
    @staticmethod
    def ensure_directory(path: str) -> str:
        """确保目录存在"""
        os.makedirs(path, exist_ok=True)
        return path
    
    @staticmethod
    def save_json(data: Any, filepath: str, pretty: bool = True, compress: bool = False) -> str:
        """保存JSON文件"""
        FileManager.ensure_directory(os.path.dirname(filepath))
        
        json_str = json.dumps(
            data, 
            indent=2 if pretty else None, 
            ensure_ascii=False,
            default=str
        )
        
        if compress:
            filepath += '.gz'
            with gzip.open(filepath, 'wt', encoding='utf-8') as f:
                f.write(json_str)
        else:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(json_str)
        
        logger.info(f"已保存JSON文件: {filepath}")
        return filepath
    
    @staticmethod
    def load_json(filepath: str) -> Any:
        """加载JSON文件"""
        try:
            if filepath.endswith('.gz'):
                with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                    return json.load(f)
            else:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except FileNotFoundError:
            logger.error(f"文件未找到: {filepath}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON格式错误: {filepath} - {str(e)}")
            return None
    
    @staticmethod
    def save_excel(data: Union[Dict[str, Any], pd.DataFrame], filepath: str, sheets: Dict[str, Any] = None) -> str:
        """保存Excel文件"""
        FileManager.ensure_directory(os.path.dirname(filepath))
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            if isinstance(data, pd.DataFrame):
                data.to_excel(writer, sheet_name='Data', index=False)
            elif isinstance(data, dict):
                # 将字典转换为DataFrame
                df = pd.json_normalize(data)
                df.to_excel(writer, sheet_name='Data', index=False)
            
            # 添加额外的工作表
            if sheets:
                for sheet_name, sheet_data in sheets.items():
                    if isinstance(sheet_data, pd.DataFrame):
                        sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                    elif isinstance(sheet_data, (list, dict)):
                        df = pd.json_normalize(sheet_data)
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        logger.info(f"已保存Excel文件: {filepath}")
        return filepath
    
    @staticmethod
    def create_backup(filepath: str) -> Optional[str]:
        """创建文件备份"""
        if not os.path.exists(filepath):
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{filepath}.backup_{timestamp}"
        
        try:
            shutil.copy2(filepath, backup_path)
            logger.info(f"已创建备份: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}")
            return None
    
    @staticmethod
    def cleanup_old_files(directory: str, pattern: str = "*.backup_*", max_files: int = 10):
        """清理旧文件"""
        import glob
        
        files = glob.glob(os.path.join(directory, pattern))
        files.sort(key=os.path.getmtime, reverse=True)
        
        # 删除超出限制的文件
        for file_to_delete in files[max_files:]:
            try:
                os.remove(file_to_delete)
                logger.info(f"已删除旧文件: {file_to_delete}")
            except Exception as e:
                logger.error(f"删除文件失败: {file_to_delete} - {str(e)}")

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_response(response_data: Any, endpoint_schema: Dict[str, Any] = None) -> Dict[str, Any]:
        """验证响应数据"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'metadata': {}
        }
        
        try:
            # 基本验证
            if response_data is None:
                result['errors'].append("响应数据为空")
                result['valid'] = False
                return result
            
            # 数据类型验证
            if isinstance(response_data, dict):
                result['metadata']['type'] = 'object'
                result['metadata']['keys'] = list(response_data.keys())
                
                # 检查常见的错误字段
                if 'error' in response_data:
                    result['warnings'].append(f"响应包含错误信息: {response_data['error']}")
                
                # 检查空对象
                if not response_data:
                    result['warnings'].append("响应为空对象")
            
            elif isinstance(response_data, list):
                result['metadata']['type'] = 'array'
                result['metadata']['length'] = len(response_data)
                
                if not response_data:
                    result['warnings'].append("响应为空数组")
            
            else:
                result['metadata']['type'] = type(response_data).__name__
            
            # Schema验证（如果提供）
            if endpoint_schema:
                schema_result = DataValidator._validate_against_schema(response_data, endpoint_schema)
                result['errors'].extend(schema_result['errors'])
                result['warnings'].extend(schema_result['warnings'])
            
            # 数据质量检查
            quality_result = DataValidator._check_data_quality(response_data)
            result['warnings'].extend(quality_result['warnings'])
            
            # 最终验证结果
            result['valid'] = len(result['errors']) == 0
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"验证过程异常: {str(e)}")
        
        return result
    
    @staticmethod
    def _validate_against_schema(data: Any, schema: Dict[str, Any]) -> Dict[str, Any]:
        """根据schema验证数据"""
        result = {'errors': [], 'warnings': []}
        
        # 简单的schema验证实现
        # 实际项目中可以使用 jsonschema 库
        
        return result
    
    @staticmethod
    def _check_data_quality(data: Any) -> Dict[str, Any]:
        """检查数据质量"""
        result = {'warnings': []}
        
        if isinstance(data, dict):
            # 检查是否有过多的null值
            null_count = sum(1 for v in data.values() if v is None)
            if null_count > len(data) * 0.5:
                result['warnings'].append(f"数据中null值过多: {null_count}/{len(data)}")
        
        elif isinstance(data, list):
            if len(data) > 1000:
                result['warnings'].append(f"数据量较大: {len(data)} 条记录")
        
        return result

class HashGenerator:
    """哈希生成器"""
    
    @staticmethod
    def generate_data_hash(data: Any) -> str:
        """生成数据哈希值"""
        json_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.md5(json_str.encode('utf-8')).hexdigest()
    
    @staticmethod
    def generate_request_hash(endpoint: str, params: Dict[str, Any]) -> str:
        """生成请求哈希值"""
        combined = f"{endpoint}#{json.dumps(params, sort_keys=True, default=str)}"
        return hashlib.md5(combined.encode('utf-8')).hexdigest()

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int):
        self.total = total
        self.current = 0
        self.start_time = datetime.now()
        self.errors = 0
        self.warnings = 0
    
    def update(self, increment: int = 1, has_error: bool = False, has_warning: bool = False):
        """更新进度"""
        self.current += increment
        if has_error:
            self.errors += 1
        if has_warning:
            self.warnings += 1
    
    def get_progress(self) -> Dict[str, Any]:
        """获取进度信息"""
        elapsed = datetime.now() - self.start_time
        percentage = (self.current / self.total * 100) if self.total > 0 else 0
        
        # 估算剩余时间
        if self.current > 0:
            avg_time_per_item = elapsed.total_seconds() / self.current
            remaining_items = self.total - self.current
            estimated_remaining = remaining_items * avg_time_per_item
        else:
            estimated_remaining = 0
        
        return {
            'current': self.current,
            'total': self.total,
            'percentage': round(percentage, 1),
            'elapsed': str(elapsed).split('.')[0],  # 去掉微秒
            'estimatedRemaining': str(pd.Timedelta(seconds=estimated_remaining)).split('.')[0],
            'errors': self.errors,
            'warnings': self.warnings,
            'completed': self.current >= self.total
        }
    
    def print_progress(self):
        """打印进度"""
        progress = self.get_progress()
        bar_length = 30
        filled_length = int(bar_length * progress['current'] // progress['total'])
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f"\r进度: |{bar}| {progress['percentage']}% "
              f"({progress['current']}/{progress['total']}) "
              f"错误: {progress['errors']} 警告: {progress['warnings']} "
              f"已用时: {progress['elapsed']} 预计剩余: {progress['estimatedRemaining']}", 
              end='', flush=True)
        
        if progress['completed']:
            print()  # 完成后换行

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def get_timestamp() -> str:
    """获取时间戳字符串"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def is_valid_json(text: str) -> bool:
    """检查字符串是否为有效JSON"""
    try:
        json.loads(text)
        return True
    except (json.JSONDecodeError, TypeError):
        return False

# 示例用法
if __name__ == "__main__":
    # 测试数据处理
    test_data = {
        'name': 'Test User',
        'amount': 123.456789,
        'date': datetime.now(),
        'details': {
            'description': 'A' * 2000,  # 长字符串
            'value': None
        }
    }
    
    # 清理数据
    cleaned = DataProcessor.sanitize_data(test_data)
    print("清理后的数据:")
    print(json.dumps(cleaned, indent=2, ensure_ascii=False))
    
    # 提取模式
    schema = DataProcessor.extract_schema(test_data)
    print("\n数据模式:")
    print(json.dumps(schema, indent=2, ensure_ascii=False))
    
    # 测试进度跟踪
    print("\n进度跟踪测试:")
    tracker = ProgressTracker(10)
    for i in range(11):
        tracker.update(has_error=(i % 3 == 0), has_warning=(i % 2 == 0))
        tracker.print_progress()
        import time
        time.sleep(0.1)