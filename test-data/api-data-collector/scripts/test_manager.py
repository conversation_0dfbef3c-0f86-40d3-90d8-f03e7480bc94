#!/usr/bin/env python3
"""
统一测试管理器
提供统一的接口来管理和执行各种测试任务
"""

import json
import os
import sys
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum

# 添加脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from collect_api_data import APIDataCollector
from auth_handler import AuthHandler
from utils import FileManager, ProgressTracker, get_timestamp

class TestType(Enum):
    """测试类型枚举"""
    API_DATA_COLLECTION = "api_data_collection"
    PERFORMANCE_TEST = "performance_test"
    INTEGRATION_TEST = "integration_test"
    CONSISTENCY_TEST = "consistency_test"
    SECURITY_TEST = "security_test"
    ALL_TESTS = "all_tests"

class TestStatus(Enum):
    """测试状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class TestManager:
    """统一测试管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """初始化测试管理器"""
        self.config_dir = config_dir
        self.config = self._load_config()
        self.logger = self._setup_logging()
        
        # 测试结果存储
        self.test_session = {
            'sessionId': get_timestamp(),
            'startTime': datetime.now().isoformat(),
            'status': TestStatus.PENDING.value,
            'tests': {},
            'summary': {
                'total': 0,
                'completed': 0,
                'failed': 0,
                'skipped': 0
            }
        }
        
        # 输出目录
        self.output_dir = "output"
        self.session_dir = os.path.join(self.output_dir, f"session_{self.test_session['sessionId']}")
        FileManager.ensure_directory(self.session_dir)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        config_path = os.path.join(self.config_dir, "test-manager-config.json")
        return FileManager.load_json(config_path) or self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "enabledTests": {
                "api_data_collection": True,
                "performance_test": True,
                "integration_test": True,
                "consistency_test": True,
                "security_test": False
            },
            "testSequence": [
                "api_data_collection",
                "integration_test",
                "consistency_test",
                "performance_test",
                "security_test"
            ],
            "parallel": {
                "enabled": False,
                "maxWorkers": 3
            },
            "reporting": {
                "generateReport": True,
                "includeDetails": True,
                "exportFormats": ["json", "html"]
            },
            "notifications": {
                "enabled": False,
                "onFailure": True,
                "onComplete": True
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        # 确保输出目录存在
        if not hasattr(self, 'output_dir'):
            self.output_dir = "output"
        FileManager.ensure_directory(self.output_dir)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{self.output_dir}/test_manager_{get_timestamp()}.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def run_test(self, test_type: TestType, **kwargs) -> Dict[str, Any]:
        """运行单个测试"""
        test_id = f"{test_type.value}_{get_timestamp()}"
        
        self.logger.info(f"开始执行测试: {test_type.value}")
        
        # 初始化测试记录
        test_record = {
            'testId': test_id,
            'testType': test_type.value,
            'status': TestStatus.RUNNING.value,
            'startTime': datetime.now().isoformat(),
            'parameters': kwargs,
            'result': None,
            'error': None,
            'duration': 0
        }
        
        self.test_session['tests'][test_id] = test_record
        self.test_session['summary']['total'] += 1
        
        try:
            start_time = datetime.now()
            
            # 根据测试类型执行相应的测试
            if test_type == TestType.API_DATA_COLLECTION:
                result = self._run_api_data_collection(**kwargs)
            elif test_type == TestType.PERFORMANCE_TEST:
                result = self._run_performance_test(**kwargs)
            elif test_type == TestType.INTEGRATION_TEST:
                result = self._run_integration_test(**kwargs)
            elif test_type == TestType.CONSISTENCY_TEST:
                result = self._run_consistency_test(**kwargs)
            elif test_type == TestType.SECURITY_TEST:
                result = self._run_security_test(**kwargs)
            else:
                raise ValueError(f"不支持的测试类型: {test_type.value}")
            
            # 更新测试记录
            end_time = datetime.now()
            test_record['status'] = TestStatus.COMPLETED.value
            test_record['endTime'] = end_time.isoformat()
            test_record['duration'] = (end_time - start_time).total_seconds()
            test_record['result'] = result
            
            self.test_session['summary']['completed'] += 1
            self.logger.info(f"测试 {test_type.value} 执行成功")
            
            return result
            
        except Exception as e:
            # 记录错误
            test_record['status'] = TestStatus.FAILED.value
            test_record['endTime'] = datetime.now().isoformat()
            test_record['error'] = str(e)
            
            self.test_session['summary']['failed'] += 1
            self.logger.error(f"测试 {test_type.value} 执行失败: {str(e)}")
            
            raise
    
    def run_test_suite(self, test_types: List[TestType] = None, **kwargs) -> Dict[str, Any]:
        """运行测试套件"""
        if test_types is None:
            # 使用配置中的测试序列
            test_types = [
                TestType(test_name) for test_name in self.config.get('testSequence', [])
                if self.config.get('enabledTests', {}).get(test_name, False)
            ]
        
        self.logger.info(f"开始执行测试套件，包含 {len(test_types)} 个测试")
        self.test_session['status'] = TestStatus.RUNNING.value
        
        # 创建进度跟踪器
        progress = ProgressTracker(len(test_types))
        
        results = {}
        
        try:
            for test_type in test_types:
                try:
                    # 检查测试是否启用
                    if not self.config.get('enabledTests', {}).get(test_type.value, True):
                        self.logger.info(f"跳过测试: {test_type.value} (已禁用)")
                        self.test_session['summary']['skipped'] += 1
                        progress.update(1)
                        continue
                    
                    # 执行测试
                    result = self.run_test(test_type, **kwargs)
                    results[test_type.value] = result
                    
                    progress.update(1, has_error=False)
                    
                except Exception as e:
                    self.logger.error(f"测试 {test_type.value} 失败: {str(e)}")
                    results[test_type.value] = {'error': str(e)}
                    
                    progress.update(1, has_error=True)
                    
                    # 决定是否继续执行其他测试
                    if not self.config.get('continueOnError', True):
                        break
                
                # 显示进度
                progress.print_progress()
            
            # 更新会话状态
            if self.test_session['summary']['failed'] == 0:
                self.test_session['status'] = TestStatus.COMPLETED.value
            else:
                self.test_session['status'] = TestStatus.FAILED.value
            
            self.test_session['endTime'] = datetime.now().isoformat()
            
            # 保存结果
            self._save_session_results()
            
            # 生成报告
            if self.config.get('reporting', {}).get('generateReport', True):
                self._generate_report()
            
            self.logger.info("测试套件执行完成")
            
        except KeyboardInterrupt:
            self.logger.warning("测试被用户中断")
            self.test_session['status'] = TestStatus.FAILED.value
            self.test_session['error'] = "用户中断"
        
        return {
            'session': self.test_session,
            'results': results
        }
    
    def _run_api_data_collection(self, **kwargs) -> Dict[str, Any]:
        """运行API数据采集测试"""
        self.logger.info("执行API数据采集")
        
        collector = APIDataCollector(self.config_dir)
        
        # 根据参数决定采集范围
        if 'module' in kwargs:
            result = collector.collect_module(kwargs['module'])
        elif 'endpoint' in kwargs:
            result = collector.collect_endpoint(kwargs['endpoint'], kwargs.get('params', {}))
        else:
            result = collector.collect_all()
        
        return {
            'type': 'api_data_collection',
            'summary': result.get('metadata', {}),
            'outputDir': 'output'
        }
    
    def _run_performance_test(self, **kwargs) -> Dict[str, Any]:
        """运行性能测试"""
        self.logger.info("执行性能测试")
        
        # 这里应该集成性能测试工具
        # 暂时返回模拟结果
        return {
            'type': 'performance_test',
            'metrics': {
                'averageResponseTime': 150,
                'throughput': 1000,
                'errorRate': 0.01
            },
            'status': 'completed'
        }
    
    def _run_integration_test(self, **kwargs) -> Dict[str, Any]:
        """运行集成测试"""
        self.logger.info("执行集成测试")
        
        # 这里应该集成JUnit集成测试
        # 暂时返回模拟结果
        return {
            'type': 'integration_test',
            'testCases': {
                'total': 25,
                'passed': 23,
                'failed': 2
            },
            'status': 'completed'
        }
    
    def _run_consistency_test(self, **kwargs) -> Dict[str, Any]:
        """运行一致性测试"""
        self.logger.info("执行数据一致性测试")
        
        # 这里应该集成数据一致性检查
        # 暂时返回模拟结果
        return {
            'type': 'consistency_test',
            'checks': {
                'crossTableConsistency': True,
                'dataIntegrity': True,
                'referentialIntegrity': True
            },
            'status': 'completed'
        }
    
    def _run_security_test(self, **kwargs) -> Dict[str, Any]:
        """运行安全测试"""
        self.logger.info("执行安全测试")
        
        # 这里应该集成安全扫描工具
        # 暂时返回模拟结果
        return {
            'type': 'security_test',
            'vulnerabilities': {
                'high': 0,
                'medium': 1,
                'low': 3
            },
            'status': 'completed'
        }
    
    def _save_session_results(self):
        """保存会话结果"""
        session_file = os.path.join(self.session_dir, "test_session.json")
        FileManager.save_json(self.test_session, session_file)
        self.logger.info(f"测试会话结果已保存: {session_file}")
    
    def _generate_report(self):
        """生成测试报告"""
        self.logger.info("生成测试报告")
        
        # 生成HTML报告
        if 'html' in self.config.get('reporting', {}).get('exportFormats', []):
            self._generate_html_report()
        
        # 生成JSON报告
        if 'json' in self.config.get('reporting', {}).get('exportFormats', []):
            self._generate_json_report()
    
    def _generate_html_report(self):
        """生成HTML报告"""
        html_content = self._create_html_report_content()
        report_file = os.path.join(self.session_dir, "test_report.html")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"HTML报告已生成: {report_file}")
    
    def _generate_json_report(self):
        """生成JSON报告"""
        report_data = {
            'session': self.test_session,
            'timestamp': datetime.now().isoformat(),
            'summary': self._create_summary_data()
        }
        
        report_file = os.path.join(self.session_dir, "test_report.json")
        FileManager.save_json(report_data, report_file)
        
        self.logger.info(f"JSON报告已生成: {report_file}")
    
    def _create_html_report_content(self) -> str:
        """创建HTML报告内容"""
        summary = self.test_session['summary']
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告 - {self.test_session['sessionId']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }}
        .test-results {{ margin: 20px 0; }}
        .test-item {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .status-completed {{ border-left: 5px solid #28a745; }}
        .status-failed {{ border-left: 5px solid #dc3545; }}
        .status-skipped {{ border-left: 5px solid #ffc107; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>FinancialSystem 测试报告</h1>
        <p>会话ID: {self.test_session['sessionId']}</p>
        <p>执行时间: {self.test_session['startTime']}</p>
        <p>状态: {self.test_session['status']}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>总测试数</h3>
            <p style="font-size: 2em; margin: 0;">{summary['total']}</p>
        </div>
        <div class="metric">
            <h3>完成</h3>
            <p style="font-size: 2em; margin: 0; color: #28a745;">{summary['completed']}</p>
        </div>
        <div class="metric">
            <h3>失败</h3>
            <p style="font-size: 2em; margin: 0; color: #dc3545;">{summary['failed']}</p>
        </div>
        <div class="metric">
            <h3>跳过</h3>
            <p style="font-size: 2em; margin: 0; color: #ffc107;">{summary['skipped']}</p>
        </div>
    </div>
    
    <div class="test-results">
        <h2>测试详情</h2>
"""
        
        # 添加每个测试的详情
        for test_id, test_data in self.test_session['tests'].items():
            status_class = f"status-{test_data['status']}"
            html += f"""
        <div class="test-item {status_class}">
            <h3>{test_data['testType']}</h3>
            <p><strong>状态:</strong> {test_data['status']}</p>
            <p><strong>开始时间:</strong> {test_data['startTime']}</p>
            <p><strong>持续时间:</strong> {test_data.get('duration', 0):.2f}秒</p>
            {f"<p><strong>错误:</strong> {test_data['error']}</p>" if test_data.get('error') else ""}
        </div>
"""
        
        html += """
    </div>
</body>
</html>
"""
        return html
    
    def _create_summary_data(self) -> Dict[str, Any]:
        """创建摘要数据"""
        return {
            'totalTests': self.test_session['summary']['total'],
            'completedTests': self.test_session['summary']['completed'],
            'failedTests': self.test_session['summary']['failed'],
            'skippedTests': self.test_session['summary']['skipped'],
            'successRate': (self.test_session['summary']['completed'] / 
                          max(self.test_session['summary']['total'], 1) * 100),
            'sessionDuration': self._calculate_session_duration()
        }
    
    def _calculate_session_duration(self) -> float:
        """计算会话持续时间"""
        if 'endTime' in self.test_session:
            start = datetime.fromisoformat(self.test_session['startTime'])
            end = datetime.fromisoformat(self.test_session['endTime'])
            return (end - start).total_seconds()
        return 0
    
    def get_test_status(self) -> Dict[str, Any]:
        """获取测试状态"""
        return {
            'sessionId': self.test_session['sessionId'],
            'status': self.test_session['status'],
            'summary': self.test_session['summary'],
            'currentTests': len(self.test_session['tests'])
        }
    
    def list_available_tests(self) -> List[str]:
        """列出可用的测试类型"""
        return [test_type.value for test_type in TestType if test_type != TestType.ALL_TESTS]

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一测试管理器')
    parser.add_argument('--test-type', type=str, choices=[t.value for t in TestType],
                       help='指定测试类型')
    parser.add_argument('--module', type=str, help='API采集模块名称')
    parser.add_argument('--endpoint', type=str, help='API采集接口路径')
    parser.add_argument('--config-dir', type=str, default='config', help='配置文件目录')
    parser.add_argument('--list-tests', action='store_true', help='列出可用的测试类型')
    
    args = parser.parse_args()
    
    try:
        # 创建测试管理器
        manager = TestManager(args.config_dir)
        
        if args.list_tests:
            # 列出可用测试
            tests = manager.list_available_tests()
            print("可用的测试类型:")
            for test in tests:
                print(f"  - {test}")
            return
        
        if args.test_type:
            # 运行单个测试
            test_type = TestType(args.test_type)
            kwargs = {}
            
            if args.module:
                kwargs['module'] = args.module
            if args.endpoint:
                kwargs['endpoint'] = args.endpoint
            
            if test_type == TestType.ALL_TESTS:
                result = manager.run_test_suite(**kwargs)
            else:
                result = manager.run_test(test_type, **kwargs)
            
            print(f"测试 {args.test_type} 执行完成")
            
        else:
            # 运行完整测试套件
            result = manager.run_test_suite()
            print("测试套件执行完成")
        
        # 显示结果摘要
        status = manager.get_test_status()
        print(f"\\n测试摘要:")
        print(f"  会话ID: {status['sessionId']}")
        print(f"  状态: {status['status']}")
        print(f"  总测试数: {status['summary']['total']}")
        print(f"  完成: {status['summary']['completed']}")
        print(f"  失败: {status['summary']['failed']}")
        print(f"  跳过: {status['summary']['skipped']}")
        
    except KeyboardInterrupt:
        print("\\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()