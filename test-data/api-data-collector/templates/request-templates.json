{"requestTemplates": {"auth": {"login": {"description": "用户登录请求模板", "template": {"username": "${username}", "password": "${password}"}, "variables": {"username": {"type": "string", "required": true, "examples": ["admin", "manager", "user"]}, "password": {"type": "string", "required": true, "examples": ["admin123", "manager123", "user123"]}}}}, "debt": {"statistics": {"description": "债权统计查询模板", "template": {"year": "${year}", "month": "${month}", "company": "${company}"}, "variables": {"year": {"type": "string", "required": false, "examples": ["2024", "2025", null], "default": null}, "month": {"type": "string", "required": false, "examples": ["1", "6", "12", "全部月份", null], "default": null}, "company": {"type": "string", "required": false, "examples": ["中筑天佑", "万润智慧", "万融智达", "所有公司", null], "default": null}}}, "createDebt": {"description": "创建债权记录模板", "template": {"creditor": "${creditor}", "debtor": "${debtor}", "amount": "${amount}", "debtDate": "${debtDate}", "debtNature": "${debtNature}", "isLitigation": "${isLitigation}", "subject": "${subject}", "description": "${description}"}, "variables": {"creditor": {"type": "string", "required": true, "examples": ["中筑天佑", "万润智慧", "万融智达"]}, "debtor": {"type": "string", "required": true, "examples": ["测试债务人A", "测试债务人B", "测试债务人C"]}, "amount": {"type": "number", "required": true, "examples": [10000.0, 50000.0, 100000.0, 1000000.0]}, "debtDate": {"type": "string", "required": true, "format": "date", "examples": ["2024-01-01", "2024-06-01", "2025-01-01"]}, "debtNature": {"type": "string", "required": true, "examples": ["普通债权", "担保债权", "抵押债权"]}, "isLitigation": {"type": "boolean", "required": true, "examples": [true, false]}, "subject": {"type": "string", "required": true, "examples": ["其他应收款", "预付账款", "应收账款"]}, "description": {"type": "string", "required": false, "examples": ["测试债权记录", "自动生成的测试数据", null]}}}}, "export": {"exportParams": {"description": "导出参数模板", "template": {"year": "${year}", "month": "${month}", "company": "${company}", "amount": "${amount}"}, "variables": {"year": {"type": "string", "required": false, "examples": ["2024", "2025"]}, "month": {"type": "string", "required": false, "examples": ["1", "6", "12"]}, "company": {"type": "string", "required": false, "examples": ["中筑天佑", "万润智慧", "所有公司"]}, "amount": {"type": "string", "required": false, "examples": ["10", "50", "100"], "description": "金额限制（万元）"}}}}, "system": {"healthCheck": {"description": "系统健康检查模板", "template": {}, "variables": {}}}}, "testScenarios": {"boundaryTests": {"description": "边界值测试场景", "scenarios": [{"name": "空参数测试", "template": {}, "expectedBehavior": "返回默认结果或全部数据"}, {"name": "最大值测试", "template": {"year": "9999", "amount": "*********"}, "expectedBehavior": "正常处理或返回验证错误"}, {"name": "特殊字符测试", "template": {"company": "测试公司@#$%", "description": "包含特殊字符的描述!@#$%^&*()"}, "expectedBehavior": "正确处理特殊字符或返回验证错误"}]}, "errorTests": {"description": "错误场景测试", "scenarios": [{"name": "无效日期格式", "template": {"debtDate": "invalid-date"}, "expectedBehavior": "返回400错误和明确的错误信息"}, {"name": "负数金额", "template": {"amount": -1000}, "expectedBehavior": "返回400错误，不允许负数金额"}, {"name": "超长字符串", "template": {"description": "${'x'.repeat(10000)}"}, "expectedBehavior": "截断字符串或返回验证错误"}]}, "performanceTests": {"description": "性能测试场景", "scenarios": [{"name": "大数据量查询", "template": {"year": "2024"}, "expectedBehavior": "在合理时间内返回结果", "performanceTarget": {"maxResponseTime": 5000, "maxMemoryUsage": "100MB"}}, {"name": "并发请求", "template": {"year": "2024", "month": "6"}, "expectedBehavior": "支持多并发访问", "performanceTarget": {"maxConcurrency": 50, "maxResponseTime": 2000}}]}}, "dataGenerators": {"randomData": {"company": {"type": "choice", "options": ["中筑天佑", "万润智慧", "万融智达", "天恒永道", "金控投资"]}, "amount": {"type": "range", "min": 1000, "max": 10000000, "decimals": 2}, "date": {"type": "date<PERSON><PERSON><PERSON>", "start": "2020-01-01", "end": "2025-12-31"}, "boolean": {"type": "boolean", "trueRate": 0.3}}, "sequences": {"userIds": {"type": "sequence", "prefix": "USER_", "start": 1, "format": "{prefix}{number:05d}"}, "debtIds": {"type": "sequence", "prefix": "DEBT_", "start": 1, "format": "{prefix}{number:08d}"}}}, "metadata": {"version": "1.0.0", "lastUpdated": "2025-07-24", "description": "API请求模板和测试场景定义", "usage": {"templates": "用于生成标准化的API请求", "scenarios": "定义各种测试场景和预期行为", "generators": "自动生成测试数据的规则"}}}