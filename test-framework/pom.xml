<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.laoshu198838</groupId>
        <artifactId>FinancialSystem</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    
    <artifactId>test-framework</artifactId>
    <packaging>pom</packaging>
    <name>Test Framework</name>
    <description>测试框架和工具模块</description>
    
    <modules>
        <module>test-core</module>
    </modules>
    
    <properties>
        <junit.version>5.9.3</junit.version>
        <mockito.version>5.3.1</mockito.version>
        <assertj.version>3.24.2</assertj.version>
        <testcontainers.version>1.19.3</testcontainers.version>
        <rest-assured.version>5.3.2</rest-assured.version>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <!-- JUnit 5 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.version}</version>
            </dependency>
            
            <!-- Mockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            
            <!-- AssertJ -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
            </dependency>
            
            <!-- Testcontainers -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers</artifactId>
                <version>${testcontainers.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>mysql</artifactId>
                <version>${testcontainers.version}</version>
            </dependency>
            
            <!-- REST Assured -->
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>rest-assured</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>