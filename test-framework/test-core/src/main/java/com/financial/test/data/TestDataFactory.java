package com.financial.test.data;

import com.financial.test.data.builders.*;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 测试数据工厂
 * 提供各种测试数据的创建方法
 */
@Component
public class TestDataFactory {
    
    private static final Random RANDOM = new Random();
    private static final String[] CREDITOR_NAMES = {
        "测试银行A", "测试信托B", "测试保险公司C", "测试担保公司D", "测试小贷公司E"
    };
    private static final String[] DEBTOR_PREFIXES = {
        "测试企业", "测试公司", "测试集团", "测试有限公司"
    };
    private static final String[] INDIVIDUAL_SURNAMES = {
        "张", "王", "李", "赵", "刘", "陈", "杨", "黄"
    };
    
    /**
     * 创建债权记录构建器
     */
    public DebtRecordBuilder debtRecord() {
        return new DebtRecordBuilder();
    }
    
    /**
     * 创建默认债权记录
     */
    public DebtRecord createDefaultDebtRecord() {
        return debtRecord()
            .withCreditor(randomCreditor())
            .withDebtor(randomDebtor())
            .withAmount(randomAmount())
            .withPeriod(currentPeriod())
            .withIsLitigation(RANDOM.nextBoolean())
            .withYear(Year.now().getValue())
            .build();
    }
    
    /**
     * 创建指定场景的债权记录
     */
    public DebtRecord createDebtRecordForScenario(TestScenario scenario) {
        switch (scenario) {
            case SMALL_AMOUNT:
                return debtRecord()
                    .withAmount(new BigDecimal("10000"))
                    .build();
                    
            case LARGE_AMOUNT:
                return debtRecord()
                    .withAmount(new BigDecimal("10000000"))
                    .build();
                    
            case ZERO_AMOUNT:
                return debtRecord()
                    .withAmount(BigDecimal.ZERO)
                    .build();
                    
            case MAX_AMOUNT:
                return debtRecord()
                    .withAmount(new BigDecimal("999999999999.99"))
                    .build();
                    
            case LITIGATION:
                return debtRecord()
                    .withIsLitigation(true)
                    .build();
                    
            case NON_LITIGATION:
                return debtRecord()
                    .withIsLitigation(false)
                    .build();
                    
            default:
                return createDefaultDebtRecord();
        }
    }
    
    /**
     * 批量创建债权记录
     */
    public List<DebtRecord> createDebtRecords(int count) {
        List<DebtRecord> records = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            records.add(createDefaultDebtRecord());
        }
        return records;
    }
    
    /**
     * 创建月度分布数据
     */
    public Map<String, BigDecimal> createMonthlyDistribution(
            BigDecimal totalAmount, 
            DistributionPattern pattern) {
        
        Map<String, BigDecimal> distribution = new LinkedHashMap<>();
        
        switch (pattern) {
            case EVEN:
                // 平均分配到12个月
                BigDecimal monthlyAmount = totalAmount.divide(
                    new BigDecimal("12"), 2, RoundingMode.DOWN
                );
                for (int i = 1; i <= 12; i++) {
                    distribution.put(i + "月", monthlyAmount);
                }
                break;
                
            case QUARTERLY:
                // 按季度分配
                BigDecimal quarterlyAmount = totalAmount.divide(
                    new BigDecimal("4"), 2, RoundingMode.DOWN
                );
                distribution.put("3月", quarterlyAmount);
                distribution.put("6月", quarterlyAmount);
                distribution.put("9月", quarterlyAmount);
                distribution.put("12月", quarterlyAmount);
                break;
                
            case FRONT_LOADED:
                // 前期集中
                distribution.put("1月", totalAmount.multiply(new BigDecimal("0.3")));
                distribution.put("2月", totalAmount.multiply(new BigDecimal("0.3")));
                distribution.put("3月", totalAmount.multiply(new BigDecimal("0.2")));
                distribution.put("4月", totalAmount.multiply(new BigDecimal("0.1")));
                distribution.put("5月", totalAmount.multiply(new BigDecimal("0.1")));
                break;
                
            case BACK_LOADED:
                // 后期集中
                distribution.put("8月", totalAmount.multiply(new BigDecimal("0.1")));
                distribution.put("9月", totalAmount.multiply(new BigDecimal("0.1")));
                distribution.put("10月", totalAmount.multiply(new BigDecimal("0.2")));
                distribution.put("11月", totalAmount.multiply(new BigDecimal("0.3")));
                distribution.put("12月", totalAmount.multiply(new BigDecimal("0.3")));
                break;
                
            case RANDOM:
                // 随机分配
                List<BigDecimal> amounts = distributeRandomly(totalAmount, 12);
                for (int i = 0; i < 12; i++) {
                    if (amounts.get(i).compareTo(BigDecimal.ZERO) > 0) {
                        distribution.put((i + 1) + "月", amounts.get(i));
                    }
                }
                break;
        }
        
        return distribution;
    }
    
    /**
     * 创建处置记录
     */
    public DisposalRecord createDisposalRecord(DebtRecord debtRecord) {
        return DisposalRecord.builder()
            .creditor(debtRecord.getCreditor())
            .debtor(debtRecord.getDebtor())
            .period(debtRecord.getPeriod())
            .isLitigation(debtRecord.getIsLitigation())
            .year(debtRecord.getYear())
            .month(randomMonth())
            .cashDisposal(randomDisposalAmount())
            .installmentRepayment(randomDisposalAmount())
            .assetDebt(randomDisposalAmount())
            .otherDisposal(randomDisposalAmount())
            .build();
    }
    
    /**
     * 创建减值准备记录
     */
    public ImpairmentReserve createImpairmentReserve(DebtRecord debtRecord) {
        BigDecimal debtAmount = debtRecord.getAmount();
        BigDecimal provisionRate = new BigDecimal("0.2"); // 20%计提率
        
        return ImpairmentReserve.builder()
            .creditor(debtRecord.getCreditor())
            .debtor(debtRecord.getDebtor())
            .period(debtRecord.getPeriod())
            .isLitigation(debtRecord.getIsLitigation())
            .year(debtRecord.getYear())
            .month(randomMonth())
            .beginningBalance(BigDecimal.ZERO)
            .currentProvision(debtAmount.multiply(provisionRate))
            .currentReversal(BigDecimal.ZERO)
            .endingBalance(debtAmount.multiply(provisionRate))
            .build();
    }
    
    /**
     * 创建用户账号
     */
    public UserAccount createUserAccount(String role) {
        String username = "test_" + role.toLowerCase() + "_" + System.currentTimeMillis();
        return new UserAccountBuilder()
            .username(username)
            .password("$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG") // password
            .role(role)
            .email(username + "@test.com")
            .status("ACTIVE")
            .build();
    }
    
    // 辅助方法
    
    private String randomCreditor() {
        return CREDITOR_NAMES[RANDOM.nextInt(CREDITOR_NAMES.length)];
    }
    
    private String randomDebtor() {
        if (RANDOM.nextBoolean()) {
            // 企业债务人
            return DEBTOR_PREFIXES[RANDOM.nextInt(DEBTOR_PREFIXES.length)] + 
                   String.format("%03d", RANDOM.nextInt(1000));
        } else {
            // 个人债务人
            return INDIVIDUAL_SURNAMES[RANDOM.nextInt(INDIVIDUAL_SURNAMES.length)] +
                   (RANDOM.nextBoolean() ? "某" : "某某");
        }
    }
    
    private BigDecimal randomAmount() {
        // 生成10万到1000万之间的随机金额
        int amount = 100000 + RANDOM.nextInt(9900000);
        return new BigDecimal(amount).setScale(2, RoundingMode.HALF_UP);
    }
    
    private BigDecimal randomDisposalAmount() {
        // 生成1万到100万之间的随机处置金额
        int amount = 10000 + RANDOM.nextInt(990000);
        return new BigDecimal(amount).setScale(2, RoundingMode.HALF_UP);
    }
    
    private String currentPeriod() {
        return Year.now().getValue() + "年新增";
    }
    
    private int randomMonth() {
        return 1 + RANDOM.nextInt(12);
    }
    
    private List<BigDecimal> distributeRandomly(BigDecimal total, int parts) {
        List<BigDecimal> distribution = new ArrayList<>();
        BigDecimal remaining = total;
        
        for (int i = 0; i < parts - 1; i++) {
            BigDecimal portion = remaining.multiply(
                new BigDecimal(RANDOM.nextDouble() * 0.3)
            ).setScale(2, RoundingMode.DOWN);
            distribution.add(portion);
            remaining = remaining.subtract(portion);
        }
        
        distribution.add(remaining);
        Collections.shuffle(distribution);
        
        return distribution;
    }
    
    // 枚举定义
    
    public enum TestScenario {
        NORMAL,
        SMALL_AMOUNT,
        LARGE_AMOUNT,
        ZERO_AMOUNT,
        MAX_AMOUNT,
        LITIGATION,
        NON_LITIGATION,
        MONTHLY_EVEN,
        QUARTERLY,
        YEAR_END
    }
    
    public enum DistributionPattern {
        EVEN,           // 平均分布
        QUARTERLY,      // 季度分布
        FRONT_LOADED,   // 前期集中
        BACK_LOADED,    // 后期集中
        RANDOM          // 随机分布
    }
    
    // 内部类定义（如果需要的话）
    
    public static class DebtRecord {
        private String creditor;
        private String debtor;
        private BigDecimal amount;
        private String period;
        private Boolean isLitigation;
        private Integer year;
        
        // 构造函数
        public DebtRecord() {}
        
        public DebtRecord(String creditor, String debtor, BigDecimal amount, String period, Boolean isLitigation, Integer year) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.amount = amount;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
        }
        
        // getters and setters
        public String getCreditor() { return creditor; }
        public void setCreditor(String creditor) { this.creditor = creditor; }
        public String getDebtor() { return debtor; }
        public void setDebtor(String debtor) { this.debtor = debtor; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public Boolean getIsLitigation() { return isLitigation; }
        public void setIsLitigation(Boolean isLitigation) { this.isLitigation = isLitigation; }
        public Integer getYear() { return year; }
        public void setYear(Integer year) { this.year = year; }
    }
    
    public static class DisposalRecord {
        private String creditor;
        private String debtor;
        private String period;
        private Boolean isLitigation;
        private Integer year;
        private Integer month;
        private BigDecimal cashDisposal;
        private BigDecimal installmentRepayment;
        private BigDecimal assetDebt;
        private BigDecimal otherDisposal;
        
        // 构造函数
        public DisposalRecord() {}
        
        public DisposalRecord(String creditor, String debtor, String period, Boolean isLitigation, Integer year,
                             Integer month, BigDecimal cashDisposal, BigDecimal installmentRepayment,
                             BigDecimal assetDebt, BigDecimal otherDisposal) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
            this.month = month;
            this.cashDisposal = cashDisposal;
            this.installmentRepayment = installmentRepayment;
            this.assetDebt = assetDebt;
            this.otherDisposal = otherDisposal;
        }
        
        // 静态构建器方法
        public static DisposalRecordBuilder builder() {
            return new DisposalRecordBuilder();
        }
        
        // getters and setters
        public String getCreditor() { return creditor; }
        public void setCreditor(String creditor) { this.creditor = creditor; }
        public String getDebtor() { return debtor; }
        public void setDebtor(String debtor) { this.debtor = debtor; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public Boolean getIsLitigation() { return isLitigation; }
        public void setIsLitigation(Boolean isLitigation) { this.isLitigation = isLitigation; }
        public Integer getYear() { return year; }
        public void setYear(Integer year) { this.year = year; }
        public Integer getMonth() { return month; }
        public void setMonth(Integer month) { this.month = month; }
        public BigDecimal getCashDisposal() { return cashDisposal; }
        public void setCashDisposal(BigDecimal cashDisposal) { this.cashDisposal = cashDisposal; }
        public BigDecimal getInstallmentRepayment() { return installmentRepayment; }
        public void setInstallmentRepayment(BigDecimal installmentRepayment) { this.installmentRepayment = installmentRepayment; }
        public BigDecimal getAssetDebt() { return assetDebt; }
        public void setAssetDebt(BigDecimal assetDebt) { this.assetDebt = assetDebt; }
        public BigDecimal getOtherDisposal() { return otherDisposal; }
        public void setOtherDisposal(BigDecimal otherDisposal) { this.otherDisposal = otherDisposal; }
    }
    
    public static class ImpairmentReserve {
        private String creditor;
        private String debtor;
        private String period;
        private Boolean isLitigation;
        private Integer year;
        private Integer month;
        private BigDecimal beginningBalance;
        private BigDecimal currentProvision;
        private BigDecimal currentReversal;
        private BigDecimal endingBalance;
        
        // 构造函数
        public ImpairmentReserve() {}
        
        public ImpairmentReserve(String creditor, String debtor, String period, Boolean isLitigation, Integer year,
                                Integer month, BigDecimal beginningBalance, BigDecimal currentProvision,
                                BigDecimal currentReversal, BigDecimal endingBalance) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
            this.month = month;
            this.beginningBalance = beginningBalance;
            this.currentProvision = currentProvision;
            this.currentReversal = currentReversal;
            this.endingBalance = endingBalance;
        }
        
        // 静态构建器方法
        public static ImpairmentReserveBuilder builder() {
            return new ImpairmentReserveBuilder();
        }
        
        // getters and setters
        public String getCreditor() { return creditor; }
        public void setCreditor(String creditor) { this.creditor = creditor; }
        public String getDebtor() { return debtor; }
        public void setDebtor(String debtor) { this.debtor = debtor; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public Boolean getIsLitigation() { return isLitigation; }
        public void setIsLitigation(Boolean isLitigation) { this.isLitigation = isLitigation; }
        public Integer getYear() { return year; }
        public void setYear(Integer year) { this.year = year; }
        public Integer getMonth() { return month; }
        public void setMonth(Integer month) { this.month = month; }
        public BigDecimal getBeginningBalance() { return beginningBalance; }
        public void setBeginningBalance(BigDecimal beginningBalance) { this.beginningBalance = beginningBalance; }
        public BigDecimal getCurrentProvision() { return currentProvision; }
        public void setCurrentProvision(BigDecimal currentProvision) { this.currentProvision = currentProvision; }
        public BigDecimal getCurrentReversal() { return currentReversal; }
        public void setCurrentReversal(BigDecimal currentReversal) { this.currentReversal = currentReversal; }
        public BigDecimal getEndingBalance() { return endingBalance; }
        public void setEndingBalance(BigDecimal endingBalance) { this.endingBalance = endingBalance; }
    }
    
    public static class UserAccount {
        private String username;
        private String password;
        private String role;
        private String email;
        private String status;
        
        // 构造函数
        public UserAccount() {}
        
        public UserAccount(String username, String password, String role, String email, String status) {
            this.username = username;
            this.password = password;
            this.role = role;
            this.email = email;
            this.status = status;
        }
        
        // 静态构建器方法
        public static UserAccountBuilder builder() {
            return new UserAccountBuilder();
        }
        
        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}