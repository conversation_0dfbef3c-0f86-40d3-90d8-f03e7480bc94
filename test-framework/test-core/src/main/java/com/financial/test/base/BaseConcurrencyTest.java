package com.financial.test.base;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

/**
 * 并发测试基类
 * 提供并发测试的基础设施和工具方法
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public abstract class BaseConcurrencyTest {
    
    private TestInfo testInfo;
    
    @BeforeEach
    public void initConcurrencyTest(TestInfo testInfo) {
        this.testInfo = testInfo;
        log.info("开始并发测试: {}", testInfo.getDisplayName());
    }
    
    /**
     * 执行并发测试
     * 
     * @param operation 要测试的操作
     * @param threads 线程数
     * @param iterations 每个线程的迭代次数
     * @return 并发测试结果
     */
    protected <T> ConcurrencyTestResult<T> executeConcurrentTest(Supplier<T> operation, 
                                                                 int threads, 
                                                                 int iterations) {
        log.info("执行并发测试 - 线程数: {}, 每线程迭代: {}", threads, iterations);
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threads);
        ExecutorService executor = Executors.newFixedThreadPool(threads);
        
        ConcurrentHashMap<String, Object> results = new ConcurrentHashMap<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());
        AtomicReference<T> lastResult = new AtomicReference<>();
        
        long startTime = System.currentTimeMillis();
        
        // 提交所有任务
        List<Future<?>> futures = new ArrayList<>();
        for (int i = 0; i < threads; i++) {
            final int threadId = i;
            futures.add(executor.submit(() -> {
                try {
                    // 等待所有线程就绪
                    startLatch.await();
                    
                    for (int j = 0; j < iterations; j++) {
                        try {
                            T result = operation.get();
                            lastResult.set(result);
                            successCount.incrementAndGet();
                            
                            // 收集结果用于一致性检查
                            if (result != null) {
                                results.put(String.format("thread_%d_iter_%d", threadId, j), result);
                            }
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                            exceptions.add(e);
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    endLatch.countDown();
                }
            }));
        }
        
        // 同时启动所有线程
        startLatch.countDown();
        
        // 等待所有线程完成
        try {
            boolean completed = endLatch.await(getTimeout(), TimeUnit.SECONDS);
            if (!completed) {
                log.error("并发测试超时");
                futures.forEach(f -> f.cancel(true));
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        return ConcurrencyTestResult.<T>builder()
            .totalOperations(threads * iterations)
            .successCount(successCount.get())
            .failureCount(failureCount.get())
            .duration(endTime - startTime)
            .exceptions(exceptions)
            .results(results)
            .lastResult(lastResult.get())
            .build();
    }
    
    /**
     * 执行竞态条件测试
     * 多个线程同时操作共享资源，检测是否存在竞态条件
     */
    protected <T> RaceConditionTestResult testRaceCondition(Runnable setup,
                                                           Supplier<T> operation,
                                                           RaceConditionChecker<T> checker,
                                                           int threads) {
        log.info("执行竞态条件测试 - 线程数: {}", threads);
        
        // 执行设置
        if (setup != null) {
            setup.run();
        }
        
        // 收集初始状态
        T initialState = operation.get();
        
        // 执行并发操作
        ConcurrencyTestResult<T> result = executeConcurrentTest(operation, threads, 1);
        
        // 检查最终状态
        T finalState = operation.get();
        
        // 验证是否存在竞态条件
        boolean hasRaceCondition = checker.check(initialState, finalState, result);
        
        return RaceConditionTestResult.builder()
            .hasRaceCondition(hasRaceCondition)
            .initialState(initialState)
            .finalState(finalState)
            .concurrencyResult(result)
            .build();
    }
    
    /**
     * 死锁检测测试
     * 检测多线程操作是否可能导致死锁
     */
    protected DeadlockTestResult testDeadlock(Runnable operation, int threads, long timeoutSeconds) {
        log.info("执行死锁检测测试 - 线程数: {}, 超时: {}秒", threads, timeoutSeconds);
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        ExecutorService executor = Executors.newFixedThreadPool(threads);
        CountDownLatch startLatch = new CountDownLatch(1);
        AtomicInteger activeThreads = new AtomicInteger(threads);
        
        // 启动监控线程
        ScheduledExecutorService monitor = Executors.newScheduledThreadPool(1);
        AtomicReference<long[]> deadlockedThreadIds = new AtomicReference<>();
        
        monitor.scheduleAtFixedRate(() -> {
            long[] ids = threadBean.findDeadlockedThreads();
            if (ids != null && ids.length > 0) {
                deadlockedThreadIds.set(ids);
                monitor.shutdown();
            }
        }, 0, 100, TimeUnit.MILLISECONDS);
        
        // 提交测试任务
        List<Future<?>> futures = new ArrayList<>();
        for (int i = 0; i < threads; i++) {
            futures.add(executor.submit(() -> {
                try {
                    startLatch.await();
                    operation.run();
                } catch (Exception e) {
                    log.debug("死锁测试线程异常: {}", e.getMessage());
                } finally {
                    activeThreads.decrementAndGet();
                }
            }));
        }
        
        // 开始测试
        startLatch.countDown();
        
        // 等待完成或超时
        try {
            executor.shutdown();
            boolean completed = executor.awaitTermination(timeoutSeconds, TimeUnit.SECONDS);
            monitor.shutdown();
            
            if (!completed) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        boolean hasDeadlock = deadlockedThreadIds.get() != null;
        
        return DeadlockTestResult.builder()
            .hasDeadlock(hasDeadlock)
            .deadlockedThreadIds(deadlockedThreadIds.get())
            .activeThreadsAtEnd(activeThreads.get())
            .build();
    }
    
    /**
     * 线程安全性测试
     * 验证操作在多线程环境下的安全性
     */
    protected <T> ThreadSafetyTestResult<T> testThreadSafety(Supplier<T> operation,
                                                            ThreadSafetyChecker<T> checker,
                                                            int threads,
                                                            int iterations) {
        log.info("执行线程安全性测试 - 线程数: {}, 迭代: {}", threads, iterations);
        
        // 执行并发测试
        ConcurrencyTestResult<T> result = executeConcurrentTest(operation, threads, iterations);
        
        // 检查线程安全性
        boolean isThreadSafe = checker.isThreadSafe(result);
        List<String> violations = checker.findViolations(result);
        
        return ThreadSafetyTestResult.<T>builder()
            .isThreadSafe(isThreadSafe)
            .violations(violations)
            .concurrencyResult(result)
            .build();
    }
    
    /**
     * 获取测试超时时间（秒）
     */
    protected long getTimeout() {
        return 30; // 默认30秒
    }
    
    /**
     * 并发测试结果
     */
    @lombok.Data
    @lombok.Builder
    public static class ConcurrencyTestResult<T> {
        private int totalOperations;
        private int successCount;
        private int failureCount;
        private long duration;
        private List<Exception> exceptions;
        private Map<String, Object> results;
        private T lastResult;
        
        public double getSuccessRate() {
            return totalOperations == 0 ? 0 : (double) successCount / totalOperations;
        }
        
        public double getOperationsPerSecond() {
            return duration == 0 ? 0 : (double) totalOperations * 1000 / duration;
        }
    }
    
    /**
     * 竞态条件检查器
     */
    @FunctionalInterface
    public interface RaceConditionChecker<T> {
        boolean check(T initialState, T finalState, ConcurrencyTestResult<T> result);
    }
    
    /**
     * 线程安全检查器
     */
    public interface ThreadSafetyChecker<T> {
        boolean isThreadSafe(ConcurrencyTestResult<T> result);
        List<String> findViolations(ConcurrencyTestResult<T> result);
    }
    
    /**
     * 竞态条件测试结果
     */
    @lombok.Data
    @lombok.Builder
    public static class RaceConditionTestResult {
        private boolean hasRaceCondition;
        private Object initialState;
        private Object finalState;
        private ConcurrencyTestResult<?> concurrencyResult;
    }
    
    /**
     * 死锁测试结果
     */
    @lombok.Data
    @lombok.Builder
    public static class DeadlockTestResult {
        private boolean hasDeadlock;
        private long[] deadlockedThreadIds;
        private int activeThreadsAtEnd;
    }
    
    /**
     * 线程安全测试结果
     */
    @lombok.Data
    @lombok.Builder
    public static class ThreadSafetyTestResult<T> {
        private boolean isThreadSafe;
        private List<String> violations;
        private ConcurrencyTestResult<T> concurrencyResult;
    }
}