package com.financial.test.snapshot;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 快照管理器
 * 用于创建、存储和管理测试数据快照
 */
@Slf4j
@Component
public class SnapshotManager {
    
    private static final String SNAPSHOT_BASE_PATH = "test-data/snapshots";
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    private final ObjectMapper objectMapper;
    
    public SnapshotManager() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        this.objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        // 确保快照目录存在
        ensureSnapshotDirectory();
    }
    
    /**
     * 创建快照
     */
    public String createSnapshot(String snapshotId, Object data) {
        try {
            SnapshotData snapshot = new SnapshotData();
            snapshot.setId(snapshotId);
            snapshot.setTimestamp(LocalDateTime.now());
            snapshot.setData(data);
            snapshot.setMetadata(captureMetadata());
            
            String filename = generateSnapshotFilename(snapshotId);
            Path snapshotPath = Paths.get(SNAPSHOT_BASE_PATH, filename);
            
            objectMapper.writeValue(snapshotPath.toFile(), snapshot);
            
            log.info("创建快照成功: {}", snapshotPath);
            return filename;
            
        } catch (IOException e) {
            throw new SnapshotException("创建快照失败: " + snapshotId, e);
        }
    }
    
    /**
     * 加载快照
     */
    public <T> T loadSnapshot(String snapshotId, Class<T> dataType) {
        try {
            Path snapshotPath = findLatestSnapshot(snapshotId);
            if (snapshotPath == null) {
                return null;
            }
            
            SnapshotData snapshot = objectMapper.readValue(
                snapshotPath.toFile(), 
                SnapshotData.class
            );
            
            return objectMapper.convertValue(snapshot.getData(), dataType);
            
        } catch (IOException e) {
            throw new SnapshotException("加载快照失败: " + snapshotId, e);
        }
    }
    
    /**
     * 检查快照是否存在
     */
    public boolean exists(String snapshotId) {
        return findLatestSnapshot(snapshotId) != null;
    }
    
    /**
     * 更新快照
     */
    public void updateSnapshot(String snapshotId, Object newData) {
        // 创建新版本的快照，保留历史
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        String versionedId = snapshotId + "_" + timestamp;
        createSnapshot(versionedId, newData);
        
        // 更新最新版本链接
        updateLatestLink(snapshotId, versionedId);
    }
    
    /**
     * 删除快照
     */
    public void deleteSnapshot(String snapshotId) {
        try {
            Path snapshotPath = findLatestSnapshot(snapshotId);
            if (snapshotPath != null) {
                Files.delete(snapshotPath);
                log.info("删除快照: {}", snapshotPath);
            }
        } catch (IOException e) {
            throw new SnapshotException("删除快照失败: " + snapshotId, e);
        }
    }
    
    /**
     * 列出所有快照
     */
    public Map<String, SnapshotInfo> listSnapshots() {
        Map<String, SnapshotInfo> snapshots = new HashMap<>();
        
        try {
            Files.walk(Paths.get(SNAPSHOT_BASE_PATH))
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .forEach(path -> {
                    try {
                        SnapshotData snapshot = objectMapper.readValue(
                            path.toFile(), 
                            SnapshotData.class
                        );
                        
                        SnapshotInfo info = new SnapshotInfo();
                        info.setId(snapshot.getId());
                        info.setTimestamp(snapshot.getTimestamp());
                        info.setSize(Files.size(path));
                        info.setPath(path.toString());
                        
                        snapshots.put(snapshot.getId(), info);
                        
                    } catch (IOException e) {
                        log.warn("读取快照信息失败: {}", path, e);
                    }
                });
                
        } catch (IOException e) {
            log.error("列出快照失败", e);
        }
        
        return snapshots;
    }
    
    /**
     * 清理过期快照
     */
    public void cleanupOldSnapshots(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        
        try {
            Files.walk(Paths.get(SNAPSHOT_BASE_PATH))
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .forEach(path -> {
                    try {
                        SnapshotData snapshot = objectMapper.readValue(
                            path.toFile(), 
                            SnapshotData.class
                        );
                        
                        if (snapshot.getTimestamp().isBefore(cutoffTime)) {
                            Files.delete(path);
                            log.info("清理过期快照: {}", path);
                        }
                        
                    } catch (IOException e) {
                        log.warn("处理快照失败: {}", path, e);
                    }
                });
                
        } catch (IOException e) {
            log.error("清理快照失败", e);
        }
    }
    
    /**
     * 比较两个快照
     */
    public SnapshotComparator.SnapshotDiff compareSnapshots(String snapshotId1, String snapshotId2) {
        Object data1 = loadSnapshot(snapshotId1, Object.class);
        Object data2 = loadSnapshot(snapshotId2, Object.class);
        
        return SnapshotComparator.compare(data1, data2);
    }
    
    // 私有辅助方法
    
    private void ensureSnapshotDirectory() {
        try {
            Files.createDirectories(Paths.get(SNAPSHOT_BASE_PATH));
        } catch (IOException e) {
            throw new SnapshotException("创建快照目录失败", e);
        }
    }
    
    private String generateSnapshotFilename(String snapshotId) {
        String sanitizedId = snapshotId.replaceAll("[^a-zA-Z0-9_-]", "_");
        return sanitizedId + ".json";
    }
    
    private Path findLatestSnapshot(String snapshotId) {
        String filename = generateSnapshotFilename(snapshotId);
        Path snapshotPath = Paths.get(SNAPSHOT_BASE_PATH, filename);
        
        if (Files.exists(snapshotPath)) {
            return snapshotPath;
        }
        
        // 查找带版本的快照
        try {
            return Files.walk(Paths.get(SNAPSHOT_BASE_PATH))
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().startsWith(snapshotId + "_"))
                .max((p1, p2) -> {
                    try {
                        return Files.getLastModifiedTime(p1)
                            .compareTo(Files.getLastModifiedTime(p2));
                    } catch (IOException e) {
                        return 0;
                    }
                })
                .orElse(null);
                
        } catch (IOException e) {
            log.error("查找快照失败: {}", snapshotId, e);
            return null;
        }
    }
    
    private void updateLatestLink(String snapshotId, String versionedId) {
        // 在实际实现中，可以创建符号链接或更新索引文件
        // 这里简化为日志记录
        log.info("更新快照链接: {} -> {}", snapshotId, versionedId);
    }
    
    private Map<String, Object> captureMetadata() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("javaVersion", System.getProperty("java.version"));
        metadata.put("osName", System.getProperty("os.name"));
        metadata.put("userName", System.getProperty("user.name"));
        metadata.put("timestamp", LocalDateTime.now().toString());
        return metadata;
    }
    
    /**
     * 快照数据结构
     */
    @lombok.Data
    public static class SnapshotData {
        private String id;
        private LocalDateTime timestamp;
        private Object data;
        private Map<String, Object> metadata;
    }
    
    /**
     * 快照信息
     */
    @lombok.Data
    public static class SnapshotInfo {
        private String id;
        private LocalDateTime timestamp;
        private long size;
        private String path;
    }
    
    /**
     * 快照异常
     */
    public static class SnapshotException extends RuntimeException {
        public SnapshotException(String message) {
            super(message);
        }
        
        public SnapshotException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}