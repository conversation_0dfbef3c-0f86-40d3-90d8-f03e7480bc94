package com.financial.test.snapshot;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.Map;

/**
 * 快照差异对象
 * 用于记录和比较数据快照之间的差异
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SnapshotDiff {
    
    /**
     * 差异类型
     */
    public enum DiffType {
        ADDED,    // 新增
        REMOVED,  // 删除
        MODIFIED, // 修改
        UNCHANGED // 未变化
    }
    
    /**
     * 差异的表名
     */
    private String tableName;
    
    /**
     * 差异类型
     */
    private DiffType diffType;
    
    /**
     * 旧值
     */
    private Map<String, Object> oldValue;
    
    /**
     * 新值
     */
    private Map<String, Object> newValue;
    
    /**
     * 主键值
     */
    private Map<String, Object> primaryKey;
    
    /**
     * 差异的字段列表
     */
    private List<String> changedFields;
    
    /**
     * 差异描述
     */
    private String description;
    
    /**
     * 检查是否有实际差异
     */
    public boolean hasDifference() {
        return diffType != DiffType.UNCHANGED;
    }
    
    /**
     * 获取格式化的差异描述
     */
    public String getFormattedDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("Table: ").append(tableName);
        sb.append(", Type: ").append(diffType);
        
        if (primaryKey != null && !primaryKey.isEmpty()) {
            sb.append(", Key: ").append(primaryKey);
        }
        
        if (changedFields != null && !changedFields.isEmpty()) {
            sb.append(", Changed Fields: ").append(changedFields);
        }
        
        if (description != null) {
            sb.append(", Description: ").append(description);
        }
        
        return sb.toString();
    }
    
    /**
     * 构造器 - 新增记录
     */
    public static SnapshotDiff added(String tableName, Map<String, Object> newValue, Map<String, Object> primaryKey) {
        return new SnapshotDiff(tableName, DiffType.ADDED, null, newValue, primaryKey, null, "Record added");
    }
    
    /**
     * 构造器 - 删除记录
     */
    public static SnapshotDiff removed(String tableName, Map<String, Object> oldValue, Map<String, Object> primaryKey) {
        return new SnapshotDiff(tableName, DiffType.REMOVED, oldValue, null, primaryKey, null, "Record removed");
    }
    
    /**
     * 构造器 - 修改记录
     */
    public static SnapshotDiff modified(String tableName, Map<String, Object> oldValue, 
                                      Map<String, Object> newValue, Map<String, Object> primaryKey, 
                                      List<String> changedFields) {
        return new SnapshotDiff(tableName, DiffType.MODIFIED, oldValue, newValue, primaryKey, 
                               changedFields, "Record modified");
    }
    
    /**
     * 构造器 - 无变化记录
     */
    public static SnapshotDiff unchanged(String tableName, Map<String, Object> value, Map<String, Object> primaryKey) {
        return new SnapshotDiff(tableName, DiffType.UNCHANGED, value, value, primaryKey, null, "No changes");
    }
}