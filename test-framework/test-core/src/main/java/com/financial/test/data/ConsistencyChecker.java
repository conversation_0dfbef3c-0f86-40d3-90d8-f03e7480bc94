package com.financial.test.data;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 数据一致性检查器
 * 用于验证跨表数据的一致性
 */
@Slf4j
@Component
public class ConsistencyChecker {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final BigDecimal TOLERANCE = new BigDecimal("0.01"); // 允许的误差范围
    
    /**
     * 检查债权操作后的数据一致性
     */
    public CheckResult checkDebtConsistency(String creditor, String debtor, Integer year) {
        CheckResult result = new CheckResult();
        
        try {
            // 获取各表的数据
            Map<String, BigDecimal> amounts = getAmountsFromAllTables(creditor, debtor, year);
            
            // 检查金额一致性
            checkAmountConsistency(amounts, result);
            
            // 检查记录完整性
            checkRecordCompleteness(creditor, debtor, year, result);
            
            // 检查逻辑一致性
            checkLogicalConsistency(creditor, debtor, year, result);
            
        } catch (Exception e) {
            result.addError("检查过程中发生异常: " + e.getMessage());
            log.error("一致性检查失败", e);
        }
        
        return result;
    }
    
    /**
     * 检查跨表事务一致性
     */
    public CheckResult checkTransactionConsistency(TransactionContext context) {
        CheckResult result = new CheckResult();
        
        // 开始事务前的快照
        Map<String, Object> beforeSnapshot = context.getBeforeSnapshot();
        
        // 事务后的状态
        Map<String, Object> afterSnapshot = captureCurrentSnapshot(context);
        
        // 验证预期的变化
        validateExpectedChanges(beforeSnapshot, afterSnapshot, context, result);
        
        return result;
    }
    
    /**
     * 获取各表的金额数据
     */
    private Map<String, BigDecimal> getAmountsFromAllTables(
            String creditor, String debtor, Integer year) {
        
        Map<String, BigDecimal> amounts = new HashMap<>();
        
        // 查询新增表
        BigDecimal addAmount = jdbcTemplate.queryForObject(
            "SELECT COALESCE(amount, 0) FROM overdue_debt_add " +
            "WHERE creditor = ? AND debtor = ? AND year = ?",
            BigDecimal.class, creditor, debtor, year
        );
        amounts.put("add_table", addAmount);
        
        // 查询减值准备表
        BigDecimal impairmentAmount = jdbcTemplate.queryForObject(
            "SELECT COALESCE(SUM(ending_balance), 0) FROM impairment_reserve " +
            "WHERE creditor = ? AND debtor = ? AND year = ?",
            BigDecimal.class, creditor, debtor, year
        );
        amounts.put("impairment_table", impairmentAmount);
        
        // 查询处置表
        BigDecimal disposalAmount = jdbcTemplate.queryForObject(
            "SELECT COALESCE(SUM(total_disposal), 0) FROM overdue_debt_decrease " +
            "WHERE creditor = ? AND debtor = ? AND year = ?",
            BigDecimal.class, creditor, debtor, year
        );
        amounts.put("disposal_table", disposalAmount);
        
        // 计算余额
        BigDecimal balance = addAmount.subtract(disposalAmount);
        amounts.put("calculated_balance", balance);
        
        return amounts;
    }
    
    /**
     * 检查金额一致性
     */
    private void checkAmountConsistency(Map<String, BigDecimal> amounts, CheckResult result) {
        BigDecimal addAmount = amounts.get("add_table");
        BigDecimal impairmentAmount = amounts.get("impairment_table");
        BigDecimal disposalAmount = amounts.get("disposal_table");
        BigDecimal calculatedBalance = amounts.get("calculated_balance");
        
        // 检查减值准备是否合理（通常不应超过债权金额）
        if (impairmentAmount.compareTo(addAmount) > 0) {
            result.addError(String.format(
                "减值准备金额(%s)超过债权金额(%s)",
                impairmentAmount, addAmount
            ));
        }
        
        // 检查处置金额是否合理（不应超过债权金额）
        if (disposalAmount.compareTo(addAmount) > 0) {
            result.addWarning(String.format(
                "处置金额(%s)超过原始债权金额(%s)",
                disposalAmount, addAmount
            ));
        }
        
        // 检查余额计算
        if (calculatedBalance.compareTo(BigDecimal.ZERO) < 0) {
            result.addWarning("计算余额为负数: " + calculatedBalance);
        }
    }
    
    /**
     * 检查记录完整性
     */
    private void checkRecordCompleteness(
            String creditor, String debtor, Integer year, CheckResult result) {
        
        // 检查是否所有相关表都有记录
        Integer addCount = jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM overdue_debt_add " +
            "WHERE creditor = ? AND debtor = ? AND year = ?",
            Integer.class, creditor, debtor, year
        );
        
        Integer impairmentCount = jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM impairment_reserve " +
            "WHERE creditor = ? AND debtor = ? AND year = ?",
            Integer.class, creditor, debtor, year
        );
        
        if (addCount > 0 && impairmentCount == 0) {
            result.addWarning("债权记录存在但没有对应的减值准备记录");
        }
        
        // 检查诉讼/非诉讼表的一致性
        checkLitigationConsistency(creditor, debtor, year, result);
    }
    
    /**
     * 检查诉讼/非诉讼表的一致性
     */
    private void checkLitigationConsistency(
            String creditor, String debtor, Integer year, CheckResult result) {
        
        // 获取债权的诉讼标志
        Boolean isLitigation = jdbcTemplate.queryForObject(
            "SELECT is_litigation FROM overdue_debt_add " +
            "WHERE creditor = ? AND debtor = ? AND year = ? LIMIT 1",
            Boolean.class, creditor, debtor, year
        );
        
        if (isLitigation != null) {
            if (isLitigation) {
                // 检查诉讼表
                Integer litigationCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM litigation_claim " +
                    "WHERE creditor = ? AND debtor = ? AND year = ?",
                    Integer.class, creditor, debtor, year
                );
                
                if (litigationCount == 0) {
                    result.addError("标记为诉讼但诉讼表中没有记录");
                }
            } else {
                // 检查非诉讼表
                Integer nonLitigationCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM non_litigation_claim " +
                    "WHERE creditor = ? AND debtor = ? AND year = ?",
                    Integer.class, creditor, debtor, year
                );
                
                if (nonLitigationCount == 0) {
                    result.addError("标记为非诉讼但非诉讼表中没有记录");
                }
            }
        }
    }
    
    /**
     * 检查逻辑一致性
     */
    private void checkLogicalConsistency(
            String creditor, String debtor, Integer year, CheckResult result) {
        
        // 检查月度数据累加是否等于总额
        checkMonthlyDataConsistency(creditor, debtor, year, result);
        
        // 检查处置明细是否等于总处置额
        checkDisposalDetailsConsistency(creditor, debtor, year, result);
    }
    
    /**
     * 检查月度数据一致性
     */
    private void checkMonthlyDataConsistency(
            String creditor, String debtor, Integer year, CheckResult result) {
        
        String sql = """
            SELECT amount,
                   COALESCE(`1月`, 0) + COALESCE(`2月`, 0) + COALESCE(`3月`, 0) +
                   COALESCE(`4月`, 0) + COALESCE(`5月`, 0) + COALESCE(`6月`, 0) +
                   COALESCE(`7月`, 0) + COALESCE(`8月`, 0) + COALESCE(`9月`, 0) +
                   COALESCE(`10月`, 0) + COALESCE(`11月`, 0) + COALESCE(`12月`, 0) as monthly_sum
            FROM overdue_debt_add
            WHERE creditor = ? AND debtor = ? AND year = ?
        """;
        
        Map<String, Object> data = jdbcTemplate.queryForMap(sql, creditor, debtor, year);
        BigDecimal totalAmount = (BigDecimal) data.get("amount");
        BigDecimal monthlySum = (BigDecimal) data.get("monthly_sum");
        
        if (totalAmount.subtract(monthlySum).abs().compareTo(TOLERANCE) > 0) {
            result.addError(String.format(
                "月度数据累加(%s)与总额(%s)不一致",
                monthlySum, totalAmount
            ));
        }
    }
    
    /**
     * 检查处置明细一致性
     */
    private void checkDisposalDetailsConsistency(
            String creditor, String debtor, Integer year, CheckResult result) {
        
        List<Map<String, Object>> disposals = jdbcTemplate.queryForList(
            """
            SELECT month,
                   cash_disposal + installment_repayment + asset_debt + other_disposal as calculated_total,
                   total_disposal
            FROM overdue_debt_decrease
            WHERE creditor = ? AND debtor = ? AND year = ?
            """,
            creditor, debtor, year
        );
        
        for (Map<String, Object> disposal : disposals) {
            BigDecimal calculated = (BigDecimal) disposal.get("calculated_total");
            BigDecimal total = (BigDecimal) disposal.get("total_disposal");
            Integer month = (Integer) disposal.get("month");
            
            if (calculated.subtract(total).abs().compareTo(TOLERANCE) > 0) {
                result.addError(String.format(
                    "%d月处置明细累加(%s)与总处置额(%s)不一致",
                    month, calculated, total
                ));
            }
        }
    }
    
    /**
     * 捕获当前快照
     */
    private Map<String, Object> captureCurrentSnapshot(TransactionContext context) {
        Map<String, Object> snapshot = new HashMap<>();
        
        // 捕获各表的当前状态
        snapshot.put("debt_count", getTableCount("overdue_debt_add", context));
        snapshot.put("disposal_count", getTableCount("overdue_debt_decrease", context));
        snapshot.put("impairment_count", getTableCount("impairment_reserve", context));
        
        // 捕获金额汇总
        snapshot.put("total_debt_amount", getTotalAmount("overdue_debt_add", context));
        snapshot.put("total_disposal_amount", getTotalAmount("overdue_debt_decrease", context));
        
        return snapshot;
    }
    
    /**
     * 获取表记录数
     */
    private Long getTableCount(String tableName, TransactionContext context) {
        String sql = String.format(
            "SELECT COUNT(*) FROM %s WHERE creditor LIKE ?",
            tableName
        );
        return jdbcTemplate.queryForObject(sql, Long.class, context.getNamespace() + "%");
    }
    
    /**
     * 获取总金额
     */
    private BigDecimal getTotalAmount(String tableName, TransactionContext context) {
        String amountColumn = tableName.equals("overdue_debt_add") ? "amount" : "total_disposal";
        String sql = String.format(
            "SELECT COALESCE(SUM(%s), 0) FROM %s WHERE creditor LIKE ?",
            amountColumn, tableName
        );
        return jdbcTemplate.queryForObject(sql, BigDecimal.class, context.getNamespace() + "%");
    }
    
    /**
     * 验证预期的变化
     */
    private void validateExpectedChanges(
            Map<String, Object> before, 
            Map<String, Object> after,
            TransactionContext context,
            CheckResult result) {
        
        // 验证记录数变化
        Long debtCountBefore = (Long) before.get("debt_count");
        Long debtCountAfter = (Long) after.get("debt_count");
        Long expectedChange = context.getExpectedDebtCountChange();
        
        if (!debtCountAfter.equals(debtCountBefore + expectedChange)) {
            result.addError(String.format(
                "债权记录数变化不符合预期。预期: %d, 实际: %d",
                expectedChange, debtCountAfter - debtCountBefore
            ));
        }
        
        // 验证金额变化
        BigDecimal amountBefore = (BigDecimal) before.get("total_debt_amount");
        BigDecimal amountAfter = (BigDecimal) after.get("total_debt_amount");
        BigDecimal expectedAmountChange = context.getExpectedAmountChange();
        
        BigDecimal actualChange = amountAfter.subtract(amountBefore);
        if (actualChange.subtract(expectedAmountChange).abs().compareTo(TOLERANCE) > 0) {
            result.addError(String.format(
                "金额变化不符合预期。预期: %s, 实际: %s",
                expectedAmountChange, actualChange
            ));
        }
    }
    
    /**
     * 检查结果
     */
    @Data
    public static class CheckResult {
        private boolean consistent = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        private Map<String, Object> details = new HashMap<>();
        
        public void addError(String error) {
            this.errors.add(error);
            this.consistent = false;
        }
        
        public void addWarning(String warning) {
            this.warnings.add(warning);
        }
        
        public void addDetail(String key, Object value) {
            this.details.put(key, value);
        }
        
        public boolean isConsistent() {
            return consistent && errors.isEmpty();
        }
        
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append("一致性检查结果: ").append(consistent ? "通过" : "失败").append("\n");
            
            if (!errors.isEmpty()) {
                sb.append("错误:\n");
                errors.forEach(e -> sb.append("  - ").append(e).append("\n"));
            }
            
            if (!warnings.isEmpty()) {
                sb.append("警告:\n");
                warnings.forEach(w -> sb.append("  - ").append(w).append("\n"));
            }
            
            return sb.toString();
        }
    }
    
    /**
     * 事务上下文
     */
    @Data
    public static class TransactionContext {
        private String namespace;
        private Map<String, Object> beforeSnapshot;
        private Long expectedDebtCountChange;
        private BigDecimal expectedAmountChange;
        private String operation;
        private Map<String, Object> operationParams;
    }
}