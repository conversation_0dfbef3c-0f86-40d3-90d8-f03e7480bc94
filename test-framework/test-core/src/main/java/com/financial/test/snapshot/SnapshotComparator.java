package com.financial.test.snapshot;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Data;

import java.util.*;

/**
 * 快照比较器
 * 用于比较两个对象并生成差异报告
 */
public class SnapshotComparator {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 比较两个对象并返回差异
     */
    public static SnapshotDiff compare(Object expected, Object actual) {
        SnapshotDiff diff = new SnapshotDiff();
        
        if (expected == null && actual == null) {
            return diff;
        }
        
        if (expected == null || actual == null) {
            diff.addDifference(DiffType.MISSING, "", expected, actual);
            return diff;
        }
        
        // 转换为JsonNode进行深度比较
        JsonNode expectedNode = OBJECT_MAPPER.valueToTree(expected);
        JsonNode actualNode = OBJECT_MAPPER.valueToTree(actual);
        
        compareNodes("", expectedNode, actualNode, diff);
        
        return diff;
    }
    
    /**
     * 递归比较JsonNode
     */
    private static void compareNodes(String path, JsonNode expected, JsonNode actual, SnapshotDiff diff) {
        if (expected.equals(actual)) {
            return;
        }
        
        if (expected.getNodeType() != actual.getNodeType()) {
            diff.addDifference(DiffType.TYPE_MISMATCH, path, expected, actual);
            return;
        }
        
        if (expected.isObject()) {
            compareObjects(path, (ObjectNode) expected, (ObjectNode) actual, diff);
        } else if (expected.isArray()) {
            compareArrays(path, expected, actual, diff);
        } else {
            diff.addDifference(DiffType.VALUE_MISMATCH, path, expected, actual);
        }
    }
    
    /**
     * 比较对象节点
     */
    private static void compareObjects(String path, ObjectNode expected, ObjectNode actual, SnapshotDiff diff) {
        Set<String> allFields = new HashSet<>();
        expected.fieldNames().forEachRemaining(allFields::add);
        actual.fieldNames().forEachRemaining(allFields::add);
        
        for (String field : allFields) {
            String fieldPath = path.isEmpty() ? field : path + "." + field;
            JsonNode expectedField = expected.get(field);
            JsonNode actualField = actual.get(field);
            
            if (expectedField == null) {
                diff.addDifference(DiffType.ADDED, fieldPath, null, actualField);
            } else if (actualField == null) {
                diff.addDifference(DiffType.REMOVED, fieldPath, expectedField, null);
            } else {
                compareNodes(fieldPath, expectedField, actualField, diff);
            }
        }
    }
    
    /**
     * 比较数组节点
     */
    private static void compareArrays(String path, JsonNode expected, JsonNode actual, SnapshotDiff diff) {
        int expectedSize = expected.size();
        int actualSize = actual.size();
        
        if (expectedSize != actualSize) {
            diff.addDifference(DiffType.SIZE_MISMATCH, path, 
                "size=" + expectedSize, "size=" + actualSize);
        }
        
        int minSize = Math.min(expectedSize, actualSize);
        for (int i = 0; i < minSize; i++) {
            String elementPath = path + "[" + i + "]";
            compareNodes(elementPath, expected.get(i), actual.get(i), diff);
        }
        
        // 记录额外的元素
        for (int i = minSize; i < expectedSize; i++) {
            diff.addDifference(DiffType.REMOVED, path + "[" + i + "]", expected.get(i), null);
        }
        
        for (int i = minSize; i < actualSize; i++) {
            diff.addDifference(DiffType.ADDED, path + "[" + i + "]", null, actual.get(i));
        }
    }
    
    /**
     * 快照差异
     */
    @Data
    public static class SnapshotDiff {
        private final List<Difference> differences = new ArrayList<>();
        
        public void addDifference(DiffType type, String path, Object expected, Object actual) {
            differences.add(new Difference(type, path, expected, actual));
        }
        
        public boolean hasDifferences() {
            return !differences.isEmpty();
        }
        
        public int getDifferenceCount() {
            return differences.size();
        }
        
        @Override
        public String toString() {
            if (differences.isEmpty()) {
                return "No differences found";
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append("Found ").append(differences.size()).append(" differences:\n");
            
            for (Difference diff : differences) {
                sb.append("  ").append(diff.toString()).append("\n");
            }
            
            return sb.toString();
        }
        
        /**
         * 生成HTML格式的差异报告
         */
        public String toHtmlReport() {
            StringBuilder html = new StringBuilder();
            html.append("<html><body>");
            html.append("<h2>Snapshot Comparison Report</h2>");
            
            if (differences.isEmpty()) {
                html.append("<p style='color:green'>No differences found</p>");
            } else {
                html.append("<table border='1'>");
                html.append("<tr><th>Type</th><th>Path</th><th>Expected</th><th>Actual</th></tr>");
                
                for (Difference diff : differences) {
                    html.append("<tr>");
                    html.append("<td>").append(diff.getType()).append("</td>");
                    html.append("<td>").append(diff.getPath()).append("</td>");
                    html.append("<td>").append(formatValue(diff.getExpected())).append("</td>");
                    html.append("<td>").append(formatValue(diff.getActual())).append("</td>");
                    html.append("</tr>");
                }
                
                html.append("</table>");
            }
            
            html.append("</body></html>");
            return html.toString();
        }
        
        private String formatValue(Object value) {
            if (value == null) {
                return "<i>null</i>";
            }
            if (value instanceof JsonNode) {
                return "<pre>" + value.toString() + "</pre>";
            }
            return value.toString();
        }
    }
    
    /**
     * 单个差异
     */
    @Data
    public static class Difference {
        private final DiffType type;
        private final String path;
        private final Object expected;
        private final Object actual;
        
        @Override
        public String toString() {
            switch (type) {
                case ADDED:
                    return String.format("[ADDED] %s: %s", path, actual);
                case REMOVED:
                    return String.format("[REMOVED] %s: %s", path, expected);
                case VALUE_MISMATCH:
                    return String.format("[CHANGED] %s: %s -> %s", path, expected, actual);
                case TYPE_MISMATCH:
                    return String.format("[TYPE_MISMATCH] %s: %s -> %s", 
                        path, getType(expected), getType(actual));
                case SIZE_MISMATCH:
                    return String.format("[SIZE_MISMATCH] %s: %s -> %s", path, expected, actual);
                default:
                    return String.format("[%s] %s: %s -> %s", type, path, expected, actual);
            }
        }
        
        private String getType(Object obj) {
            if (obj == null) return "null";
            if (obj instanceof JsonNode) {
                return ((JsonNode) obj).getNodeType().toString();
            }
            return obj.getClass().getSimpleName();
        }
    }
    
    /**
     * 差异类型
     */
    public enum DiffType {
        ADDED,          // 新增字段
        REMOVED,        // 删除字段
        VALUE_MISMATCH, // 值不匹配
        TYPE_MISMATCH,  // 类型不匹配
        SIZE_MISMATCH,  // 大小不匹配
        MISSING         // 缺失
    }
}