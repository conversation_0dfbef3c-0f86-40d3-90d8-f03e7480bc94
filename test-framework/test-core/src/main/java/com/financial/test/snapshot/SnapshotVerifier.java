package com.financial.test.snapshot;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.BiPredicate;

/**
 * 快照验证器
 * 用于验证当前数据与快照的一致性
 */
@Slf4j
@Component
public class SnapshotVerifier {
    
    @Autowired
    private SnapshotManager snapshotManager;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 创建验证器实例（静态工厂方法）
     */
    public static <T> VerificationBuilder<T> verify(String snapshotId, T actual) {
        return new VerificationBuilder<>(snapshotId, actual);
    }
    
    /**
     * 执行快照验证
     */
    public <T> VerificationResult verify(
            String snapshotId, 
            T actual, 
            Class<T> type,
            VerificationOptions options) {
        
        // 加载快照
        T expected = snapshotManager.loadSnapshot(snapshotId, type);
        
        if (expected == null) {
            // 快照不存在，创建新快照
            if (options.isCreateIfMissing()) {
                snapshotManager.createSnapshot(snapshotId, actual);
                return VerificationResult.created(snapshotId);
            } else {
                return VerificationResult.notFound(snapshotId);
            }
        }
        
        // 比较数据
        boolean matches = options.getComparator().test(expected, actual);
        
        if (!matches) {
            // 生成差异报告
            SnapshotComparator.SnapshotDiff diff = SnapshotComparator.compare(expected, actual);
            
            if (options.isUpdateIfDifferent()) {
                // 更新快照
                snapshotManager.updateSnapshot(snapshotId, actual);
                return VerificationResult.updated(snapshotId, diff);
            } else {
                return VerificationResult.mismatch(snapshotId, diff);
            }
        }
        
        return VerificationResult.success(snapshotId);
    }
    
    /**
     * 验证结果
     */
    @lombok.Data
    public static class VerificationResult {
        private final Status status;
        private final String snapshotId;
        private final SnapshotComparator.SnapshotDiff diff;
        private final String message;
        
        public enum Status {
            SUCCESS,      // 验证通过
            CREATED,      // 创建了新快照
            UPDATED,      // 更新了快照
            MISMATCH,     // 不匹配
            NOT_FOUND     // 快照不存在
        }
        
        public boolean isSuccess() {
            return status == Status.SUCCESS;
        }
        
        public static VerificationResult success(String snapshotId) {
            return new VerificationResult(Status.SUCCESS, snapshotId, null, "Snapshot matches");
        }
        
        public static VerificationResult created(String snapshotId) {
            return new VerificationResult(Status.CREATED, snapshotId, null, "Snapshot created");
        }
        
        public static VerificationResult updated(String snapshotId, SnapshotComparator.SnapshotDiff diff) {
            return new VerificationResult(Status.UPDATED, snapshotId, diff, "Snapshot updated");
        }
        
        public static VerificationResult mismatch(String snapshotId, SnapshotComparator.SnapshotDiff diff) {
            return new VerificationResult(Status.MISMATCH, snapshotId, diff, "Snapshot mismatch");
        }
        
        public static VerificationResult notFound(String snapshotId) {
            return new VerificationResult(Status.NOT_FOUND, snapshotId, null, "Snapshot not found");
        }
    }
    
    /**
     * 验证选项
     */
    @lombok.Data
    public static class VerificationOptions {
        private boolean createIfMissing = true;
        private boolean updateIfDifferent = false;
        private BiPredicate<Object, Object> comparator = Object::equals;
        private boolean ignoreOrder = false;
        private boolean ignoreNulls = false;
    }
    
    /**
     * 流式构建器
     */
    public static class VerificationBuilder<T> {
        private final String snapshotId;
        private final T actual;
        private final VerificationOptions options = new VerificationOptions();
        
        @Autowired
        private SnapshotVerifier verifier;
        
        public VerificationBuilder(String snapshotId, T actual) {
            this.snapshotId = snapshotId;
            this.actual = actual;
        }
        
        public VerificationBuilder<T> withComparator(BiPredicate<Object, Object> comparator) {
            this.options.setComparator(comparator);
            return this;
        }
        
        public VerificationBuilder<T> withCreateIfMissing(boolean create) {
            this.options.setCreateIfMissing(create);
            return this;
        }
        
        public VerificationBuilder<T> withUpdateIfDifferent(boolean update) {
            this.options.setUpdateIfDifferent(update);
            return this;
        }
        
        public VerificationBuilder<T> ignoreOrder() {
            this.options.setIgnoreOrder(true);
            return this;
        }
        
        public VerificationBuilder<T> ignoreNulls() {
            this.options.setIgnoreNulls(true);
            return this;
        }
        
        public void assertMatches() {
            if (verifier == null) {
                // 如果不是通过Spring注入，手动创建
                verifier = new SnapshotVerifier();
            }
            
            @SuppressWarnings("unchecked")
            Class<T> type = (Class<T>) actual.getClass();
            
            VerificationResult result = verifier.verify(snapshotId, actual, type, options);
            
            if (!result.isSuccess() && result.getStatus() != VerificationResult.Status.CREATED) {
                throw new AssertionError(formatErrorMessage(result));
            }
        }
        
        private String formatErrorMessage(VerificationResult result) {
            StringBuilder message = new StringBuilder();
            message.append("Snapshot verification failed: ").append(result.getMessage()).append("\n");
            
            if (result.getDiff() != null) {
                message.append("Differences found:\n");
                message.append(result.getDiff().toString());
            }
            
            return message.toString();
        }
    }
}