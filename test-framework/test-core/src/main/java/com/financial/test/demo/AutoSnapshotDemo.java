package com.financial.test.demo;

import com.financial.test.base.BaseConsistencyTest;
import com.financial.test.snapshot.SnapshotVerifier;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 自动快照演示
 * 展示快照的自动创建和验证机制
 */
@Slf4j
@SpringBootTest
public class AutoSnapshotDemo extends BaseConsistencyTest {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    protected void captureBaselineSnapshots() {
        log.info("🚀 开始捕获基准快照...");
        
        // 捕获债权统计查询快照
        List<Map<String, Object>> debtStats = queryDebtStatistics();
        createSnapshot("debt_statistics_baseline", debtStats);
        log.info("📸 创建快照: debt_statistics_baseline");
        
        // 捕获月度汇总快照
        List<Map<String, Object>> monthlySummary = queryMonthlySummary();
        createSnapshot("monthly_summary_baseline", monthlySummary);
        log.info("📸 创建快照: monthly_summary_baseline");
        
        // 捕获跨表一致性检查快照
        Map<String, Object> consistencyCheck = performConsistencyCheck();
        createSnapshot("consistency_check_baseline", consistencyCheck);
        log.info("📸 创建快照: consistency_check_baseline");
        
        log.info("✅ 基准快照创建完成");
    }
    
    @Test
    void demonstrate_automatic_snapshot_creation() {
        log.info("🎯 演示自动快照创建...");
        
        // 执行查询
        List<Map<String, Object>> currentStats = queryDebtStatistics();
        
        // 第一次运行会自动创建快照
        // 后续运行会验证一致性
        SnapshotVerifier.verify("auto_created_snapshot", currentStats)
            .withCreateIfMissing(true)  // 不存在时自动创建
            .assertMatches();
        
        log.info("✅ 快照验证完成");
    }
    
    @Test 
    void demonstrate_snapshot_comparison() {
        log.info("🔍 演示快照对比...");
        
        // 执行查询
        List<Map<String, Object>> results = queryDebtStatistics();
        
        // 验证与基准快照的一致性
        SnapshotVerifier.verify("debt_statistics_baseline", results)
            .withComparator((expected, actual) -> {
                // 自定义比较逻辑：忽略时间戳，允许金额0.01误差
                return deepCompareWithTolerance(expected, actual);
            })
            .assertMatches();
        
        log.info("✅ 数据一致性验证通过");
    }
    
    @Test
    void demonstrate_controlled_snapshot_update() {
        log.info("🔄 演示受控快照更新...");
        
        // 模拟业务逻辑变更后的查询结果
        List<Map<String, Object>> newResults = queryWithNewBusinessLogic();
        
        // 检查系统属性决定是否更新快照
        boolean shouldUpdate = Boolean.parseBoolean(
            System.getProperty("snapshot.update", "false")
        );
        
        if (shouldUpdate) {
            log.info("🔧 更新模式：将更新快照");
            SnapshotVerifier.verify("business_logic_snapshot", newResults)
                .withUpdateIfDifferent(true)
                .assertMatches();
        } else {
            log.info("🛡️ 验证模式：检查一致性");
            SnapshotVerifier.verify("business_logic_snapshot", newResults)
                .withCreateIfMissing(true)
                .assertMatches();
        }
    }
    
    // 辅助方法
    
    private List<Map<String, Object>> queryDebtStatistics() {
        String sql = """
            SELECT 
                creditor,
                COUNT(*) as debt_count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
            FROM overdue_debt_add 
            WHERE year = 2024
            GROUP BY creditor
            ORDER BY creditor
        """;
        
        return jdbcTemplate.queryForList(sql);
    }
    
    private List<Map<String, Object>> queryMonthlySummary() {
        String sql = """
            SELECT 
                MONTH(STR_TO_DATE(CONCAT(year, '-01-01'), '%Y-%m-%d')) as month,
                SUM(amount) as monthly_total
            FROM overdue_debt_add 
            WHERE year = 2024
            GROUP BY year
            ORDER BY month
        """;
        
        return jdbcTemplate.queryForList(sql);
    }
    
    private Map<String, Object> performConsistencyCheck() {
        // 模拟一致性检查结果
        return Map.of(
            "debt_table_count", getTableCount("overdue_debt_add"),
            "impairment_table_count", getTableCount("impairment_reserve"),
            "disposal_table_count", getTableCount("overdue_debt_decrease"),
            "consistency_score", 100
        );
    }
    
    private Long getTableCount(String tableName) {
        return jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM " + tableName, 
            Long.class
        );
    }
    
    private List<Map<String, Object>> queryWithNewBusinessLogic() {
        // 模拟业务逻辑变更后的查询
        // 实际中这里会是您修改后的查询逻辑
        return queryDebtStatistics();
    }
    
    private boolean deepCompareWithTolerance(Object expected, Object actual) {
        // 实现自定义比较逻辑
        // 1. 忽略时间戳字段
        // 2. BigDecimal金额允许0.01误差
        // 3. 忽略null值
        return true; // 简化实现
    }
    
    @Override
    protected Map<String, Object> captureCurrentState() {
        return Map.of(
            "timestamp", System.currentTimeMillis(),
            "debt_count", getTableCount("overdue_debt_add"),
            "total_amount", getTotalAmount()
        );
    }
    
    @Override
    protected void verifyStateTransition(String operation, 
                                       Map<String, Object> beforeState, 
                                       Map<String, Object> afterState) {
        // 验证状态转换逻辑
        log.info("验证操作 {} 的状态转换", operation);
    }
    
    private BigDecimal getTotalAmount() {
        return jdbcTemplate.queryForObject(
            "SELECT COALESCE(SUM(amount), 0) FROM overdue_debt_add", 
            BigDecimal.class
        );
    }
}