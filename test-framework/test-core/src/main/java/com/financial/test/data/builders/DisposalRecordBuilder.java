package com.financial.test.data.builders;

import java.math.BigDecimal;

/**
 * 处置记录构建器
 */
public class DisposalRecordBuilder {
    private String creditor;
    private String debtor;
    private String period;
    private Boolean isLitigation;
    private Integer year;
    private Integer month;
    private BigDecimal cashDisposal = BigDecimal.ZERO;
    private BigDecimal installmentRepayment = BigDecimal.ZERO;
    private BigDecimal assetDebt = BigDecimal.ZERO;
    private BigDecimal otherDisposal = BigDecimal.ZERO;
    
    public static DisposalRecordBuilder builder() {
        return new DisposalRecordBuilder();
    }
    
    public DisposalRecordBuilder creditor(String creditor) {
        this.creditor = creditor;
        return this;
    }
    
    public DisposalRecordBuilder debtor(String debtor) {
        this.debtor = debtor;
        return this;
    }
    
    public DisposalRecordBuilder period(String period) {
        this.period = period;
        return this;
    }
    
    public DisposalRecordBuilder isLitigation(Boolean isLitigation) {
        this.isLitigation = isLitigation;
        return this;
    }
    
    public DisposalRecordBuilder year(Integer year) {
        this.year = year;
        return this;
    }
    
    public DisposalRecordBuilder month(Integer month) {
        this.month = month;
        return this;
    }
    
    public DisposalRecordBuilder cashDisposal(BigDecimal cashDisposal) {
        this.cashDisposal = cashDisposal;
        return this;
    }
    
    public DisposalRecordBuilder installmentRepayment(BigDecimal installmentRepayment) {
        this.installmentRepayment = installmentRepayment;
        return this;
    }
    
    public DisposalRecordBuilder assetDebt(BigDecimal assetDebt) {
        this.assetDebt = assetDebt;
        return this;
    }
    
    public DisposalRecordBuilder otherDisposal(BigDecimal otherDisposal) {
        this.otherDisposal = otherDisposal;
        return this;
    }
    
    public com.financial.test.data.TestDataFactory.DisposalRecord build() {
        com.financial.test.data.TestDataFactory.DisposalRecord record = new com.financial.test.data.TestDataFactory.DisposalRecord();
        record.setCreditor(this.creditor);
        record.setDebtor(this.debtor);
        record.setPeriod(this.period);
        record.setIsLitigation(this.isLitigation);
        record.setYear(this.year);
        record.setMonth(this.month);
        record.setCashDisposal(this.cashDisposal);
        record.setInstallmentRepayment(this.installmentRepayment);
        record.setAssetDebt(this.assetDebt);
        record.setOtherDisposal(this.otherDisposal);
        return record;
    }
}