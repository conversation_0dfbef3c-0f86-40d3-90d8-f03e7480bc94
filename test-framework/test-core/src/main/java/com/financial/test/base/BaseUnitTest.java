package com.financial.test.base;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

/**
 * 单元测试基类
 * 提供Mockito支持和基础测试功能
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.STRICT_STUBS)
public abstract class BaseUnitTest {
    
    @BeforeEach
    public void baseSetUp() {
        // 基础设置
        setUp();
    }
    
    /**
     * 子类可以覆盖此方法进行特定的初始化
     */
    protected void setUp() {
        // 默认空实现
    }
    
    /**
     * 创建一个带有默认值的Builder模式辅助方法
     */
    protected <T> T withDefaults(T builder) {
        // 子类可以实现具体的默认值设置逻辑
        return builder;
    }
    
    /**
     * 断言抛出预期异常并验证消息
     */
    protected void assertThrowsWithMessage(
            Class<? extends Exception> expectedType,
            String expectedMessage,
            Runnable runnable) {
        
        try {
            runnable.run();
            throw new AssertionError("Expected exception " + expectedType.getName() + " was not thrown");
        } catch (Exception e) {
            if (!expectedType.isInstance(e)) {
                throw new AssertionError("Expected " + expectedType.getName() + 
                    " but got " + e.getClass().getName(), e);
            }
            if (expectedMessage != null && !e.getMessage().contains(expectedMessage)) {
                throw new AssertionError("Expected message containing '" + expectedMessage + 
                    "' but got '" + e.getMessage() + "'");
            }
        }
    }
}