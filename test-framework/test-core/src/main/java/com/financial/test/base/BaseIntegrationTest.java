package com.financial.test.base;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import com.financial.test.utils.TestDataCleaner;
import com.financial.test.utils.TestTransactionManager;
import com.financial.test.utils.TestTransactionManager.TransactionalOperation;

/**
 * 集成测试基类
 * 提供Spring Boot完整上下文环境
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public abstract class BaseIntegrationTest {
    
    @Autowired
    protected MockMvc mockMvc;
    
    @Autowired
    protected TestDataCleaner testDataCleaner;
    
    @Autowired
    protected TestTransactionManager testTransactionManager;
    
    @BeforeEach
    public void baseSetUp() {
        // 确保每个测试开始时的清洁状态
        testDataCleaner.cleanTestData();
        
        // 子类可以覆盖此方法进行额外的初始化
        setUp();
    }
    
    /**
     * 子类可以覆盖此方法进行特定的初始化
     */
    protected void setUp() {
        // 默认空实现
    }
    
    /**
     * 获取测试命名空间，用于数据隔离
     */
    protected String getTestNamespace() {
        return this.getClass().getSimpleName() + "_" + System.currentTimeMillis();
    }
    
    /**
     * 执行在新事务中的操作
     */
    protected <T> T executeInNewTransaction(TransactionalOperation<T> operation) {
        return testTransactionManager.executeInNewTransaction(operation);
    }
}