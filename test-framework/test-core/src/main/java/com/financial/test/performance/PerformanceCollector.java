package com.financial.test.performance;

import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.TestWatcher;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 性能数据收集器
 * 负责收集和计算性能测试指标
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
public class PerformanceCollector implements TestWatcher {
    
    private final Map<String, List<Long>> responseTimesMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> successCountMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> failureCountMap = new ConcurrentHashMap<>();
    private final OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
    
    private String currentSession;
    
    /**
     * 开始新的测试会话
     */
    public void startNewSession(String sessionName) {
        this.currentSession = sessionName;
        responseTimesMap.clear();
        successCountMap.clear();
        failureCountMap.clear();
        log.info("开始性能测试会话: {}", sessionName);
    }
    
    /**
     * 收集单次执行的性能数据
     */
    public <T> PerformanceMetrics collect(String testName, Runnable operation) {
        Instant startTime = Instant.now();
        List<Long> responseTimes = Collections.synchronizedList(new ArrayList<>());
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong failureCount = new AtomicLong(0);
        
        double startCpuUsage = getCpuUsage();
        long startMemory = getMemoryUsage();
        
        try {
            long operationStart = System.nanoTime();
            operation.run();
            long operationEnd = System.nanoTime();
            
            long responseTime = TimeUnit.NANOSECONDS.toMillis(operationEnd - operationStart);
            responseTimes.add(responseTime);
            successCount.incrementAndGet();
        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("性能测试执行失败: {}", e.getMessage());
        }
        
        Instant endTime = Instant.now();
        double endCpuUsage = getCpuUsage();
        long endMemory = getMemoryUsage();
        
        return buildMetrics(
            testName,
            responseTimes,
            successCount.get(),
            failureCount.get(),
            startTime,
            endTime,
            1,
            (startCpuUsage + endCpuUsage) / 2,
            (startMemory + endMemory) / 2 / (1024 * 1024)
        );
    }
    
    /**
     * 收集并发执行的性能数据
     */
    public <T> PerformanceMetrics collectConcurrent(String testName,
                                                   Supplier<T> operation,
                                                   int threads,
                                                   long duration,
                                                   TimeUnit unit) {
        ExecutorService executor = Executors.newFixedThreadPool(threads);
        List<Long> responseTimes = Collections.synchronizedList(new ArrayList<>());
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong failureCount = new AtomicLong(0);
        
        Instant startTime = Instant.now();
        long endTimeMillis = System.currentTimeMillis() + unit.toMillis(duration);
        
        double startCpuUsage = getCpuUsage();
        long startMemory = getMemoryUsage();
        
        CountDownLatch startLatch = new CountDownLatch(1);
        List<Future<?>> futures = new ArrayList<>();
        
        // 启动所有线程
        for (int i = 0; i < threads; i++) {
            futures.add(executor.submit(() -> {
                try {
                    startLatch.await();
                    while (System.currentTimeMillis() < endTimeMillis) {
                        long operationStart = System.nanoTime();
                        try {
                            operation.get();
                            long operationEnd = System.nanoTime();
                            long responseTime = TimeUnit.NANOSECONDS.toMillis(operationEnd - operationStart);
                            responseTimes.add(responseTime);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                            log.debug("并发测试操作失败: {}", e.getMessage());
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }));
        }
        
        // 同时启动所有线程
        startLatch.countDown();
        
        // 等待所有任务完成
        futures.forEach(future -> {
            try {
                future.get();
            } catch (Exception e) {
                log.error("等待任务完成时出错: {}", e.getMessage());
            }
        });
        
        executor.shutdown();
        try {
            executor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Instant endTime = Instant.now();
        double endCpuUsage = getCpuUsage();
        long endMemory = getMemoryUsage();
        
        return buildMetrics(
            testName,
            responseTimes,
            successCount.get(),
            failureCount.get(),
            startTime,
            endTime,
            threads,
            (startCpuUsage + endCpuUsage) / 2,
            (startMemory + endMemory) / 2 / (1024 * 1024)
        );
    }
    
    /**
     * 构建性能指标对象
     */
    private PerformanceMetrics buildMetrics(String testName,
                                           List<Long> responseTimes,
                                           long successCount,
                                           long failureCount,
                                           Instant startTime,
                                           Instant endTime,
                                           int concurrency,
                                           double cpuUsage,
                                           double memoryUsage) {
        if (responseTimes.isEmpty()) {
            return PerformanceMetrics.builder()
                .testName(testName)
                .totalRequests(failureCount)
                .successfulRequests(0)
                .failedRequests(failureCount)
                .startTime(startTime)
                .endTime(endTime)
                .concurrency(concurrency)
                .cpuUsage(cpuUsage)
                .memoryUsage(memoryUsage)
                .build();
        }
        
        Collections.sort(responseTimes);
        
        double sum = responseTimes.stream().mapToLong(Long::longValue).sum();
        double average = sum / responseTimes.size();
        double duration = (endTime.toEpochMilli() - startTime.toEpochMilli()) / 1000.0;
        double throughput = (successCount + failureCount) / duration;
        
        return PerformanceMetrics.builder()
            .testName(testName)
            .totalRequests(successCount + failureCount)
            .successfulRequests(successCount)
            .failedRequests(failureCount)
            .startTime(startTime)
            .endTime(endTime)
            .minResponseTime(responseTimes.get(0))
            .maxResponseTime(responseTimes.get(responseTimes.size() - 1))
            .averageResponseTime(average)
            .medianResponseTime(getPercentile(responseTimes, 50))
            .p90ResponseTime(getPercentile(responseTimes, 90))
            .p95ResponseTime(getPercentile(responseTimes, 95))
            .p99ResponseTime(getPercentile(responseTimes, 99))
            .throughput(throughput)
            .concurrency(concurrency)
            .cpuUsage(cpuUsage)
            .memoryUsage(memoryUsage)
            .build();
    }
    
    /**
     * 计算百分位数
     */
    private double getPercentile(List<Long> sortedValues, int percentile) {
        if (sortedValues.isEmpty()) {
            return 0;
        }
        int index = (int) Math.ceil(percentile / 100.0 * sortedValues.size()) - 1;
        return sortedValues.get(Math.max(0, index));
    }
    
    /**
     * 获取CPU使用率
     */
    private double getCpuUsage() {
        if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
            return ((com.sun.management.OperatingSystemMXBean) osBean).getProcessCpuLoad() * 100;
        }
        return -1;
    }
    
    /**
     * 获取内存使用量（字节）
     */
    private long getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    @Override
    public void testSuccessful(ExtensionContext context) {
        log.info("性能测试完成: {}", context.getDisplayName());
    }
    
    @Override
    public void testFailed(ExtensionContext context, Throwable cause) {
        log.error("性能测试失败: {}, 原因: {}", context.getDisplayName(), cause.getMessage());
    }
}