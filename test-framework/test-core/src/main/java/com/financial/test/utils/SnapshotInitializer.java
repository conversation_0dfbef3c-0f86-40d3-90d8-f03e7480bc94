package com.financial.test.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import com.financial.test.snapshot.SnapshotManager;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 快照初始化器
 * 在应用启动时自动检查并创建必要的快照
 */
@Slf4j
@Component
@Profile("test")
public class SnapshotInitializer implements CommandLineRunner {
    
    @Autowired
    private SnapshotManager snapshotManager;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final String[] REQUIRED_SNAPSHOTS = {
        "debt_statistics_baseline",
        "monthly_summary_baseline", 
        "consistency_check_baseline",
        "cross_table_validation_baseline"
    };
    
    @Override
    public void run(String... args) throws Exception {
        log.info("🔍 检查快照完整性...");
        
        boolean shouldInitialize = shouldInitializeSnapshots();
        
        if (shouldInitialize) {
            log.info("🚀 开始初始化快照...");
            initializeAllSnapshots();
            log.info("✅ 快照初始化完成");
        } else {
            log.info("📸 快照已存在，跳过初始化");
        }
    }
    
    private boolean shouldInitializeSnapshots() {
        // 检查是否有手动初始化标志
        String forceInit = System.getProperty("snapshot.init", "auto");
        if ("force".equals(forceInit)) {
            log.info("🔧 检测到强制初始化标志");
            return true;
        }
        
        // 检查关键快照是否存在
        for (String snapshotId : REQUIRED_SNAPSHOTS) {
            if (!snapshotManager.exists(snapshotId)) {
                log.info("📋 缺失快照: {}", snapshotId);
                return true;
            }
        }
        
        // 检查快照目录是否为空
        File snapshotDir = new File("test-data/snapshots");
        if (!snapshotDir.exists() || snapshotDir.listFiles().length == 0) {
            log.info("📁 快照目录为空");
            return true;
        }
        
        return false;
    }
    
    private void initializeAllSnapshots() {
        try {
            // 1. 债权统计基准快照
            createDebtStatisticsSnapshot();
            
            // 2. 月度汇总基准快照
            createMonthlySummarySnapshot();
            
            // 3. 一致性检查基准快照
            createConsistencyCheckSnapshot();
            
            // 4. 跨表验证基准快照
            createCrossTableValidationSnapshot();
            
        } catch (Exception e) {
            log.error("❌ 快照初始化失败", e);
            throw new RuntimeException("快照初始化失败", e);
        }
    }
    
    private void createDebtStatisticsSnapshot() {
        log.info("📸 创建债权统计基准快照...");
        
        String sql = """
            SELECT 
                creditor,
                COUNT(*) as debt_count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                MAX(amount) as max_amount,
                MIN(amount) as min_amount
            FROM overdue_debt_add 
            WHERE year >= 2024
            GROUP BY creditor
            ORDER BY creditor
        """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        snapshotManager.createSnapshot("debt_statistics_baseline", results);
        
        log.info("✅ 债权统计快照已创建，包含 {} 条记录", results.size());
    }
    
    private void createMonthlySummarySnapshot() {
        log.info("📸 创建月度汇总基准快照...");
        
        String sql = """
            SELECT 
                year,
                SUM(COALESCE(`1月`, 0)) as jan_amount,
                SUM(COALESCE(`2月`, 0)) as feb_amount,
                SUM(COALESCE(`3月`, 0)) as mar_amount,
                SUM(COALESCE(`4月`, 0)) as apr_amount,
                SUM(COALESCE(`5月`, 0)) as may_amount,
                SUM(COALESCE(`6月`, 0)) as jun_amount,
                SUM(COALESCE(`7月`, 0)) as jul_amount,
                SUM(COALESCE(`8月`, 0)) as aug_amount,
                SUM(COALESCE(`9月`, 0)) as sep_amount,
                SUM(COALESCE(`10月`, 0)) as oct_amount,
                SUM(COALESCE(`11月`, 0)) as nov_amount,
                SUM(COALESCE(`12月`, 0)) as dec_amount
            FROM overdue_debt_add 
            WHERE year >= 2024
            GROUP BY year
            ORDER BY year
        """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        snapshotManager.createSnapshot("monthly_summary_baseline", results);
        
        log.info("✅ 月度汇总快照已创建");
    }
    
    private void createConsistencyCheckSnapshot() {
        log.info("📸 创建一致性检查基准快照...");
        
        Map<String, Object> consistencyData = Map.of(
            "debt_table_count", getTableCount("overdue_debt_add"),
            "disposal_table_count", getTableCount("overdue_debt_decrease"), 
            "impairment_table_count", getTableCount("impairment_reserve"),
            "litigation_table_count", getTableCount("litigation_claim"),
            "total_debt_amount", getTotalAmount("overdue_debt_add", "amount"),
            "total_disposal_amount", getTotalAmount("overdue_debt_decrease", "total_disposal"),
            "check_timestamp", System.currentTimeMillis()
        );
        
        snapshotManager.createSnapshot("consistency_check_baseline", consistencyData);
        
        log.info("✅ 一致性检查快照已创建");
    }
    
    private void createCrossTableValidationSnapshot() {
        log.info("📸 创建跨表验证基准快照...");
        
        String sql = """
            SELECT 
                a.creditor,
                a.debtor,
                a.year,
                a.amount as debt_amount,
                COALESCE(d.total_disposal, 0) as disposal_amount,
                COALESCE(i.ending_balance, 0) as impairment_amount,
                (a.amount - COALESCE(d.total_disposal, 0)) as remaining_balance
            FROM overdue_debt_add a
            LEFT JOIN (
                SELECT creditor, debtor, year, SUM(total_disposal) as total_disposal
                FROM overdue_debt_decrease 
                GROUP BY creditor, debtor, year
            ) d ON a.creditor = d.creditor AND a.debtor = d.debtor AND a.year = d.year
            LEFT JOIN (
                SELECT creditor, debtor, year, SUM(ending_balance) as ending_balance
                FROM impairment_reserve 
                GROUP BY creditor, debtor, year
            ) i ON a.creditor = i.creditor AND a.debtor = i.debtor AND a.year = i.year
            WHERE a.year >= 2024
            ORDER BY a.creditor, a.debtor, a.year
        """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        snapshotManager.createSnapshot("cross_table_validation_baseline", results);
        
        log.info("✅ 跨表验证快照已创建，包含 {} 条记录", results.size());
    }
    
    private Long getTableCount(String tableName) {
        try {
            return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM " + tableName, 
                Long.class
            );
        } catch (Exception e) {
            log.warn("⚠️ 无法获取表 {} 的记录数: {}", tableName, e.getMessage());
            return 0L;
        }
    }
    
    private Object getTotalAmount(String tableName, String columnName) {
        try {
            return jdbcTemplate.queryForObject(
                String.format("SELECT COALESCE(SUM(%s), 0) FROM %s", columnName, tableName),
                Object.class
            );
        } catch (Exception e) {
            log.warn("⚠️ 无法获取表 {} 的总金额: {}", tableName, e.getMessage());
            return 0;
        }
    }
}