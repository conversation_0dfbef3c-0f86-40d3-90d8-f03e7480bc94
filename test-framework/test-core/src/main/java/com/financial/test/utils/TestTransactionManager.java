package com.financial.test.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 测试事务管理器
 * 提供细粒度的事务控制
 */
@Slf4j
@Component
public class TestTransactionManager {
    
    @Autowired
    private PlatformTransactionManager transactionManager;
    
    @Autowired
    private TransactionTemplate transactionTemplate;
    
    /**
     * 在新事务中执行操作
     */
    public <T> T executeInNewTransaction(TransactionalOperation<T> operation) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        
        TransactionStatus status = transactionManager.getTransaction(def);
        
        try {
            T result = operation.execute();
            transactionManager.commit(status);
            return result;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("Transaction failed", e);
        }
    }
    
    /**
     * 在当前事务中执行操作
     */
    public <T> T executeInCurrentTransaction(TransactionalOperation<T> operation) {
        return transactionTemplate.execute(status -> {
            try {
                return operation.execute();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("Operation failed", e);
            }
        });
    }
    
    /**
     * 执行并回滚（用于测试异常情况）
     */
    public <T> T executeAndRollback(TransactionalOperation<T> operation) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        
        TransactionStatus status = transactionManager.getTransaction(def);
        
        try {
            T result = operation.execute();
            return result;
        } catch (Exception e) {
            throw new RuntimeException("Operation failed", e);
        } finally {
            // 总是回滚
            transactionManager.rollback(status);
        }
    }
    
    /**
     * 检查是否在事务中
     */
    public boolean isInTransaction() {
        return transactionTemplate.execute(TransactionStatus::isNewTransaction) != null;
    }
    
    /**
     * 创建保存点
     */
    public Object createSavepoint() {
        return transactionTemplate.execute(status -> {
            Object savepoint = status.createSavepoint();
            log.debug("Created savepoint: {}", savepoint);
            return savepoint;
        });
    }
    
    /**
     * 回滚到保存点
     */
    public void rollbackToSavepoint(Object savepoint) {
        transactionTemplate.execute(status -> {
            status.rollbackToSavepoint(savepoint);
            log.debug("Rolled back to savepoint: {}", savepoint);
            return null;
        });
    }
    
    /**
     * 释放保存点
     */
    public void releaseSavepoint(Object savepoint) {
        transactionTemplate.execute(status -> {
            status.releaseSavepoint(savepoint);
            log.debug("Released savepoint: {}", savepoint);
            return null;
        });
    }
    
    @FunctionalInterface
    public interface TransactionalOperation<T> {
        T execute() throws Exception;
    }
}