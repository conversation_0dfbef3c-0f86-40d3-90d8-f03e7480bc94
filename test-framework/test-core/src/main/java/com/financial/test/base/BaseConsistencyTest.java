package com.financial.test.base;

import com.financial.test.snapshot.SnapshotManager;
import com.financial.test.snapshot.SnapshotVerifier;
import com.financial.test.data.ConsistencyChecker;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据一致性测试基类
 * 提供快照测试和跨表一致性验证功能
 */
@SpringBootTest
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Transactional
public abstract class BaseConsistencyTest {
    
    @Autowired
    protected SnapshotManager snapshotManager;
    
    @Autowired
    protected ConsistencyChecker consistencyChecker;
    
    private final Map<String, Object> baselineSnapshots = new ConcurrentHashMap<>();
    
    @BeforeAll
    public void captureBaseline() {
        // 捕获基准快照
        captureBaselineSnapshots();
    }
    
    /**
     * 子类实现此方法来定义需要捕获的基准快照
     */
    protected abstract void captureBaselineSnapshots();
    
    /**
     * 创建查询快照
     */
    protected void createSnapshot(String snapshotId, Object data) {
        snapshotManager.createSnapshot(snapshotId, data);
        baselineSnapshots.put(snapshotId, data);
    }
    
    /**
     * 验证快照一致性
     */
    protected void verifySnapshot(String snapshotId, Object currentData) {
        SnapshotVerifier.verify(snapshotId, currentData)
            .withComparator((a, b) -> getComparator().compare(a, b))
            .assertMatches();
    }
    
    /**
     * 验证跨表一致性
     */
    protected void verifyCrossTableConsistency(String creditor, String debtor, Integer year) {
        ConsistencyChecker.CheckResult result = consistencyChecker.checkDebtConsistency(creditor, debtor, year);
        
        if (!result.isConsistent()) {
            throw new AssertionError("Cross-table consistency check failed:\n" + 
                result.getErrors().toString());
        }
    }
    
    /**
     * 获取比较器，子类可以覆盖提供自定义比较逻辑
     */
    protected SnapshotComparator getComparator() {
        return new DefaultSnapshotComparator();
    }
    
    /**
     * 执行带有一致性检查的操作
     */
    protected <T> T executeWithConsistencyCheck(
            String operationName, 
            ConsistentOperation<T> operation) {
        
        // 捕获操作前状态
        Map<String, Object> beforeState = captureCurrentState();
        
        // 执行操作
        T result = operation.execute();
        
        // 捕获操作后状态
        Map<String, Object> afterState = captureCurrentState();
        
        // 验证一致性
        verifyStateTransition(operationName, beforeState, afterState);
        
        return result;
    }
    
    /**
     * 捕获当前系统状态
     */
    protected abstract Map<String, Object> captureCurrentState();
    
    /**
     * 验证状态转换的一致性
     */
    protected abstract void verifyStateTransition(
            String operation, 
            Map<String, Object> beforeState, 
            Map<String, Object> afterState);
    
    @FunctionalInterface
    public interface ConsistentOperation<T> {
        T execute();
    }
    
    /**
     * 默认快照比较器
     */
    public static class DefaultSnapshotComparator implements SnapshotComparator {
        @Override
        public boolean compare(Object expected, Object actual) {
            // 默认使用equals比较
            return expected.equals(actual);
        }
    }
    
    public interface SnapshotComparator {
        boolean compare(Object expected, Object actual);
    }
}