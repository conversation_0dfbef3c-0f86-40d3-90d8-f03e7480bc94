package com.financial.test.data.builders;

import java.math.BigDecimal;

/**
 * 减值准备记录构建器
 */
public class ImpairmentReserveBuilder {
    private String creditor;
    private String debtor;
    private String period;
    private Boolean isLitigation;
    private Integer year;
    private Integer month;
    private BigDecimal beginningBalance = BigDecimal.ZERO;
    private BigDecimal currentProvision = BigDecimal.ZERO;
    private BigDecimal currentReversal = BigDecimal.ZERO;
    private BigDecimal endingBalance = BigDecimal.ZERO;
    
    public static ImpairmentReserveBuilder builder() {
        return new ImpairmentReserveBuilder();
    }
    
    public ImpairmentReserveBuilder creditor(String creditor) {
        this.creditor = creditor;
        return this;
    }
    
    public ImpairmentReserveBuilder debtor(String debtor) {
        this.debtor = debtor;
        return this;
    }
    
    public ImpairmentReserveBuilder period(String period) {
        this.period = period;
        return this;
    }
    
    public ImpairmentReserveBuilder isLitigation(Boolean isLitigation) {
        this.isLitigation = isLitigation;
        return this;
    }
    
    public ImpairmentReserveBuilder year(Integer year) {
        this.year = year;
        return this;
    }
    
    public ImpairmentReserveBuilder month(Integer month) {
        this.month = month;
        return this;
    }
    
    public ImpairmentReserveBuilder beginningBalance(BigDecimal beginningBalance) {
        this.beginningBalance = beginningBalance;
        return this;
    }
    
    public ImpairmentReserveBuilder currentProvision(BigDecimal currentProvision) {
        this.currentProvision = currentProvision;
        return this;
    }
    
    public ImpairmentReserveBuilder currentReversal(BigDecimal currentReversal) {
        this.currentReversal = currentReversal;
        return this;
    }
    
    public ImpairmentReserveBuilder endingBalance(BigDecimal endingBalance) {
        this.endingBalance = endingBalance;
        return this;
    }
    
    public com.financial.test.data.TestDataFactory.ImpairmentReserve build() {
        com.financial.test.data.TestDataFactory.ImpairmentReserve record = new com.financial.test.data.TestDataFactory.ImpairmentReserve();
        record.setCreditor(this.creditor);
        record.setDebtor(this.debtor);
        record.setPeriod(this.period);
        record.setIsLitigation(this.isLitigation);
        record.setYear(this.year);
        record.setMonth(this.month);
        record.setBeginningBalance(this.beginningBalance);
        record.setCurrentProvision(this.currentProvision);
        record.setCurrentReversal(this.currentReversal);
        record.setEndingBalance(this.endingBalance);
        return record;
    }
}