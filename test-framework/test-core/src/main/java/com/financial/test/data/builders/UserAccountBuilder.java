package com.financial.test.data.builders;

/**
 * 用户账号构建器
 */
public class UserAccountBuilder {
    private String username;
    private String password;
    private String role;
    private String email;
    private String status = "ACTIVE";
    
    public static UserAccountBuilder builder() {
        return new UserAccountBuilder();
    }
    
    public UserAccountBuilder username(String username) {
        this.username = username;
        return this;
    }
    
    public UserAccountBuilder password(String password) {
        this.password = password;
        return this;
    }
    
    public UserAccountBuilder role(String role) {
        this.role = role;
        return this;
    }
    
    public UserAccountBuilder email(String email) {
        this.email = email;
        return this;
    }
    
    public UserAccountBuilder status(String status) {
        this.status = status;
        return this;
    }
    
    public com.financial.test.data.TestDataFactory.UserAccount build() {
        com.financial.test.data.TestDataFactory.UserAccount account = new com.financial.test.data.TestDataFactory.UserAccount();
        account.setUsername(this.username);
        account.setPassword(this.password);
        account.setRole(this.role);
        account.setEmail(this.email);
        account.setStatus(this.status);
        return account;
    }
}