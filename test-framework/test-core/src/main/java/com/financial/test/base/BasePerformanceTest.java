package com.financial.test.base;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import com.financial.test.performance.PerformanceCollector;
import com.financial.test.performance.PerformanceMetrics;
import com.financial.test.performance.PerformanceReport;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 性能测试基类
 * 提供性能测试的基础设施和通用方法
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("performance-test")
@ExtendWith(PerformanceCollector.class)
public abstract class BasePerformanceTest {
    
    protected PerformanceCollector performanceCollector;
    
    @BeforeEach
    public void initPerformanceTest() {
        this.performanceCollector = new PerformanceCollector();
        performanceCollector.startNewSession(getTestName());
    }
    
    /**
     * 执行性能测试
     * 
     * @param testName 测试名称
     * @param operation 测试操作
     * @param iterations 迭代次数
     * @return 性能测试结果
     */
    protected <T> PerformanceMetrics executePerformanceTest(String testName, 
                                                            Supplier<T> operation, 
                                                            int iterations) {
        log.info("开始性能测试: {}, 迭代次数: {}", testName, iterations);
        
        // 预热
        warmup(operation, Math.min(iterations / 10, 100));
        
        // 执行测试
        PerformanceMetrics metrics = performanceCollector.collect(testName, () -> {
            for (int i = 0; i < iterations; i++) {
                operation.get();
            }
        });
        
        // 验证性能指标
        validatePerformanceMetrics(metrics);
        
        return metrics;
    }
    
    /**
     * 并发性能测试
     * 
     * @param testName 测试名称
     * @param operation 测试操作
     * @param threads 线程数
     * @param duration 持续时间
     * @param unit 时间单位
     * @return 性能测试结果
     */
    protected <T> PerformanceMetrics executeConcurrentPerformanceTest(String testName,
                                                                     Supplier<T> operation,
                                                                     int threads,
                                                                     long duration,
                                                                     TimeUnit unit) {
        log.info("开始并发性能测试: {}, 线程数: {}, 持续时间: {} {}", 
                testName, threads, duration, unit);
        
        return performanceCollector.collectConcurrent(testName, operation, threads, duration, unit);
    }
    
    /**
     * 负载测试
     * 
     * @param testName 测试名称
     * @param operation 测试操作
     * @param startThreads 起始线程数
     * @param maxThreads 最大线程数
     * @param stepSize 线程递增步长
     * @param stepDuration 每步持续时间
     * @param unit 时间单位
     * @return 性能报告
     */
    protected <T> PerformanceReport executeLoadTest(String testName,
                                                   Supplier<T> operation,
                                                   int startThreads,
                                                   int maxThreads,
                                                   int stepSize,
                                                   long stepDuration,
                                                   TimeUnit unit) {
        log.info("开始负载测试: {}, 线程范围: {}-{}, 步长: {}", 
                testName, startThreads, maxThreads, stepSize);
        
        PerformanceReport report = new PerformanceReport(testName);
        
        for (int threads = startThreads; threads <= maxThreads; threads += stepSize) {
            PerformanceMetrics metrics = executeConcurrentPerformanceTest(
                testName + "_" + threads + "threads",
                operation,
                threads,
                stepDuration,
                unit
            );
            report.addMetrics(threads, metrics);
            
            // 检查是否需要提前终止（如响应时间过长或错误率过高）
            if (shouldStopLoadTest(metrics)) {
                log.warn("负载测试提前终止，线程数: {}, 原因: 性能指标超出阈值", threads);
                break;
            }
        }
        
        return report;
    }
    
    /**
     * 预热操作
     */
    private <T> void warmup(Supplier<T> operation, int warmupIterations) {
        log.debug("预热中，迭代次数: {}", warmupIterations);
        for (int i = 0; i < warmupIterations; i++) {
            operation.get();
        }
    }
    
    /**
     * 验证性能指标是否满足要求
     * 子类可重写此方法以自定义验证逻辑
     */
    protected void validatePerformanceMetrics(PerformanceMetrics metrics) {
        // 默认验证逻辑
        if (metrics.getAverageResponseTime() > getMaxAcceptableResponseTime()) {
            throw new AssertionError(String.format(
                "平均响应时间 %.2fms 超过最大可接受时间 %dms",
                metrics.getAverageResponseTime(),
                getMaxAcceptableResponseTime()
            ));
        }
        
        if (metrics.getErrorRate() > getMaxAcceptableErrorRate()) {
            throw new AssertionError(String.format(
                "错误率 %.2f%% 超过最大可接受错误率 %.2f%%",
                metrics.getErrorRate() * 100,
                getMaxAcceptableErrorRate() * 100
            ));
        }
    }
    
    /**
     * 判断是否应该停止负载测试
     */
    protected boolean shouldStopLoadTest(PerformanceMetrics metrics) {
        return metrics.getAverageResponseTime() > getMaxAcceptableResponseTime() * 2
            || metrics.getErrorRate() > 0.1; // 错误率超过10%
    }
    
    /**
     * 获取测试名称
     */
    protected abstract String getTestName();
    
    /**
     * 获取最大可接受响应时间（毫秒）
     * 子类可重写以自定义阈值
     */
    protected long getMaxAcceptableResponseTime() {
        return 1000; // 默认1秒
    }
    
    /**
     * 获取最大可接受错误率
     * 子类可重写以自定义阈值
     */
    protected double getMaxAcceptableErrorRate() {
        return 0.01; // 默认1%
    }
}