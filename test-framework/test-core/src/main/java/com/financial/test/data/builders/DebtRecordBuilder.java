package com.financial.test.data.builders;

import com.financial.test.data.TestDataFactory;
import java.math.BigDecimal;
import java.time.Year;

/**
 * 债权记录构建器
 */
public class DebtRecordBuilder {
    private String creditor = "默认债权人";
    private String debtor = "默认债务人";
    private BigDecimal amount = new BigDecimal("100000");
    private String period = Year.now().getValue() + "年新增";
    private Boolean isLitigation = false;
    private Integer year = Year.now().getValue();
    
    public DebtRecordBuilder withCreditor(String creditor) {
        this.creditor = creditor;
        return this;
    }
    
    public DebtRecordBuilder withDebtor(String debtor) {
        this.debtor = debtor;
        return this;
    }
    
    public DebtRecordBuilder withAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }
    
    public DebtRecordBuilder withPeriod(String period) {
        this.period = period;
        return this;
    }
    
    public DebtRecordBuilder withIsLitigation(Boolean isLitigation) {
        this.isLitigation = isLitigation;
        return this;
    }
    
    public DebtRecordBuilder withYear(Integer year) {
        this.year = year;
        return this;
    }
    
    public TestDataFactory.DebtRecord build() {
        TestDataFactory.DebtRecord record = new TestDataFactory.DebtRecord();
        record.setCreditor(this.creditor);
        record.setDebtor(this.debtor);
        record.setAmount(this.amount);
        record.setPeriod(this.period);
        record.setIsLitigation(this.isLitigation);
        record.setYear(this.year);
        return record;
    }
}