package com.financial.test.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试数据加载器
 * 用于从JSON文件加载测试数据和场景配置
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
public class TestDataLoader {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String TEST_DATA_BASE_PATH = "test-data";
    private static final String SCENARIOS_PATH = TEST_DATA_BASE_PATH + "/scenarios";
    private static final String SNAPSHOTS_PATH = TEST_DATA_BASE_PATH + "/snapshots";
    
    /**
     * 加载场景测试数据
     * 
     * @param scenarioPath 场景文件相对路径（如 "boundary-cases/extreme-values.json"）
     * @return 场景数据对象
     */
    public ScenarioData loadScenario(String scenarioPath) throws IOException {
        String fullPath = SCENARIOS_PATH + "/" + scenarioPath;
        log.info("加载场景数据: {}", fullPath);
        
        JsonNode rootNode = loadJsonFile(fullPath);
        
        ScenarioData scenarioData = new ScenarioData();
        scenarioData.setScenarioInfo(parseScenarioInfo(rootNode.get("scenario")));
        scenarioData.setTestData(rootNode.get("data"));
        scenarioData.setAssertions(parseAssertions(rootNode.get("assertions")));
        
        return scenarioData;
    }
    
    /**
     * 加载快照数据
     * 
     * @param snapshotName 快照文件名（如 "新增表_baseline.json"）
     * @return 快照数据
     */
    public <T> T loadSnapshot(String snapshotName, Class<T> targetClass) throws IOException {
        String fullPath = SNAPSHOTS_PATH + "/" + snapshotName;
        log.info("加载快照数据: {}", fullPath);
        
        return loadJsonFile(fullPath, targetClass);
    }
    
    /**
     * 批量加载指定目录下的所有测试数据
     * 
     * @param directory 目录路径（相对于test-data）
     * @return 文件名到数据的映射
     */
    public Map<String, JsonNode> loadAllFromDirectory(String directory) throws IOException {
        String fullPath = TEST_DATA_BASE_PATH + "/" + directory;
        Map<String, JsonNode> dataMap = new HashMap<>();
        
        try {
            Path dirPath = Paths.get(getClass().getClassLoader().getResource(fullPath).toURI());
            
            Files.walk(dirPath)
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .forEach(path -> {
                    try {
                        String fileName = path.getFileName().toString();
                        JsonNode data = loadJsonFile(fullPath + "/" + fileName);
                        dataMap.put(fileName, data);
                    } catch (IOException e) {
                        log.error("加载文件失败: {}", path, e);
                    }
                });
        } catch (Exception e) {
            log.error("遍历目录失败: {}", fullPath, e);
        }
        
        log.info("从目录 {} 加载了 {} 个文件", directory, dataMap.size());
        return dataMap;
    }
    
    /**
     * 生成测试数据
     * 根据数据生成规则动态创建测试数据
     * 
     * @param generatorConfig 数据生成配置
     * @return 生成的测试数据
     */
    public TestDataSet generateTestData(DataGeneratorConfig generatorConfig) {
        log.info("生成测试数据: {} 条记录", generatorConfig.getRecordCount());
        
        TestDataSet dataSet = new TestDataSet();
        Random random = new Random();
        
        // 生成债权记录
        List<Map<String, Object>> debtRecords = new ArrayList<>();
        for (int i = 0; i < generatorConfig.getRecordCount(); i++) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", String.format(generatorConfig.getIdPattern(), i));
            record.put("creditor", selectRandom(generatorConfig.getCreditors(), random));
            record.put("debtor", "债务人_" + i);
            record.put("amount", generateRandomAmount(generatorConfig.getAmountRange(), random));
            record.put("debtDate", generateRandomDate(generatorConfig.getYears(), random));
            record.put("debtNature", selectRandom(Arrays.asList("普通债权", "担保债权", "抵押债权"), random));
            record.put("isLitigation", random.nextDouble() < generatorConfig.getLitigationRatio());
            record.put("subject", "其他应收款");
            
            debtRecords.add(record);
        }
        dataSet.setDebtRecords(debtRecords);
        
        // 生成处置记录
        int disposalCount = (int) (generatorConfig.getRecordCount() * generatorConfig.getDisposalRatio());
        List<Map<String, Object>> disposalRecords = new ArrayList<>();
        for (int i = 0; i < disposalCount; i++) {
            Map<String, Object> record = new HashMap<>();
            Map<String, Object> debtRecord = debtRecords.get(random.nextInt(debtRecords.size()));
            
            record.put("id", "DISPOSAL_" + i);
            record.put("debtRecordId", debtRecord.get("id"));
            record.put("disposalDate", addDays((String) debtRecord.get("debtDate"), random.nextInt(365) + 30));
            record.put("disposalMethod", selectRandom(Arrays.asList("现金清收", "资产抵债", "债务重组", "分期清收"), random));
            record.put("disposalAmount", (Double) debtRecord.get("amount") * (0.1 + random.nextDouble() * 0.8));
            
            disposalRecords.add(record);
        }
        dataSet.setDisposalRecords(disposalRecords);
        
        // 生成减值记录
        int impairmentCount = (int) (generatorConfig.getRecordCount() * generatorConfig.getImpairmentRatio());
        List<Map<String, Object>> impairmentRecords = new ArrayList<>();
        for (int i = 0; i < impairmentCount; i++) {
            Map<String, Object> record = new HashMap<>();
            Map<String, Object> debtRecord = debtRecords.get(random.nextInt(debtRecords.size()));
            
            record.put("id", "IMPAIRMENT_" + i);
            record.put("debtRecordId", debtRecord.get("id"));
            record.put("impairmentRate", 5 + random.nextDouble() * 45);
            record.put("recordDate", getLastDayOfMonth((String) debtRecord.get("debtDate")));
            
            impairmentRecords.add(record);
        }
        dataSet.setImpairmentRecords(impairmentRecords);
        
        return dataSet;
    }
    
    /**
     * 验证测试数据完整性
     */
    public ValidationResult validateTestData(TestDataSet dataSet) {
        ValidationResult result = new ValidationResult();
        
        // 检查ID唯一性
        Set<String> debtIds = dataSet.getDebtRecords().stream()
            .map(r -> (String) r.get("id"))
            .collect(Collectors.toSet());
        
        if (debtIds.size() != dataSet.getDebtRecords().size()) {
            result.addError("债权记录存在重复ID");
        }
        
        // 检查引用完整性
        Set<String> referencedDebtIds = new HashSet<>();
        dataSet.getDisposalRecords().forEach(r -> referencedDebtIds.add((String) r.get("debtRecordId")));
        dataSet.getImpairmentRecords().forEach(r -> referencedDebtIds.add((String) r.get("debtRecordId")));
        
        referencedDebtIds.forEach(id -> {
            if (!debtIds.contains(id)) {
                result.addError("引用了不存在的债权ID: " + id);
            }
        });
        
        // 检查数据一致性
        dataSet.getDisposalRecords().forEach(disposal -> {
            String debtId = (String) disposal.get("debtRecordId");
            Optional<Map<String, Object>> debtOpt = dataSet.getDebtRecords().stream()
                .filter(d -> debtId.equals(d.get("id")))
                .findFirst();
            
            if (debtOpt.isPresent()) {
                Map<String, Object> debt = debtOpt.get();
                if ((Double) disposal.get("disposalAmount") > (Double) debt.get("amount")) {
                    result.addWarning("处置金额超过债权金额: " + debtId);
                }
            }
        });
        
        result.setValid(result.getErrors().isEmpty());
        return result;
    }
    
    // 辅助方法
    
    private JsonNode loadJsonFile(String resourcePath) throws IOException {
        Resource resource = new ClassPathResource(resourcePath);
        try (InputStream is = resource.getInputStream()) {
            return objectMapper.readTree(is);
        }
    }
    
    private <T> T loadJsonFile(String resourcePath, Class<T> targetClass) throws IOException {
        Resource resource = new ClassPathResource(resourcePath);
        try (InputStream is = resource.getInputStream()) {
            return objectMapper.readValue(is, targetClass);
        }
    }
    
    private ScenarioInfo parseScenarioInfo(JsonNode node) {
        ScenarioInfo info = new ScenarioInfo();
        info.setName(node.get("name").asText());
        info.setDescription(node.get("description").asText());
        info.setCategory(node.get("category").asText());
        info.setExpectedBehavior(node.get("expectedBehavior").asText());
        return info;
    }
    
    private List<Assertion> parseAssertions(JsonNode node) {
        List<Assertion> assertions = new ArrayList<>();
        if (node != null && node.isArray()) {
            node.forEach(assertionNode -> {
                Assertion assertion = new Assertion();
                assertion.setType(assertionNode.get("type").asText());
                assertion.setDescription(assertionNode.get("description").asText());
                assertions.add(assertion);
            });
        }
        return assertions;
    }
    
    private <T> T selectRandom(List<T> items, Random random) {
        return items.get(random.nextInt(items.size()));
    }
    
    private double generateRandomAmount(AmountRange range, Random random) {
        double amount = range.getMin() + (range.getMax() - range.getMin()) * random.nextDouble();
        return Math.round(amount * 100.0) / 100.0; // 保留2位小数
    }
    
    private String generateRandomDate(List<Integer> years, Random random) {
        int year = selectRandom(years, random);
        int month = random.nextInt(12) + 1;
        int day = random.nextInt(28) + 1; // 简化处理，避免月末问题
        return String.format("%d-%02d-%02d", year, month, day);
    }
    
    private String addDays(String date, int days) {
        // 简化实现，实际应使用日期库
        return date; // TODO: 实现日期加法
    }
    
    private String getLastDayOfMonth(String date) {
        // 简化实现，返回月末日期
        String[] parts = date.split("-");
        return String.format("%s-%s-31", parts[0], parts[1]);
    }
    
    // 内部数据类
    
    @lombok.Data
    public static class ScenarioData {
        private ScenarioInfo scenarioInfo;
        private JsonNode testData;
        private List<Assertion> assertions;
    }
    
    @lombok.Data
    public static class ScenarioInfo {
        private String name;
        private String description;
        private String category;
        private String expectedBehavior;
    }
    
    @lombok.Data
    public static class Assertion {
        private String type;
        private String description;
        private Map<String, Object> parameters = new HashMap<>();
    }
    
    @lombok.Data
    public static class TestDataSet {
        private List<Map<String, Object>> debtRecords = new ArrayList<>();
        private List<Map<String, Object>> disposalRecords = new ArrayList<>();
        private List<Map<String, Object>> impairmentRecords = new ArrayList<>();
    }
    
    @lombok.Data
    public static class DataGeneratorConfig {
        private int recordCount;
        private String idPattern = "GEN_%d";
        private List<String> creditors;
        private AmountRange amountRange;
        private List<Integer> years;
        private double litigationRatio;
        private double disposalRatio;
        private double impairmentRatio;
    }
    
    @lombok.Data
    public static class AmountRange {
        private double min;
        private double max;
    }
    
    @lombok.Data
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
    }
}