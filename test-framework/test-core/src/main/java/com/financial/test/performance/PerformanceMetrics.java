package com.financial.test.performance;

import lombok.Data;
import lombok.Builder;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能测试指标
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@Builder
public class PerformanceMetrics {
    
    /**
     * 测试名称
     */
    private String testName;
    
    /**
     * 总请求数
     */
    private long totalRequests;
    
    /**
     * 成功请求数
     */
    private long successfulRequests;
    
    /**
     * 失败请求数
     */
    private long failedRequests;
    
    /**
     * 测试开始时间
     */
    private Instant startTime;
    
    /**
     * 测试结束时间
     */
    private Instant endTime;
    
    /**
     * 最小响应时间（毫秒）
     */
    private double minResponseTime;
    
    /**
     * 最大响应时间（毫秒）
     */
    private double maxResponseTime;
    
    /**
     * 平均响应时间（毫秒）
     */
    private double averageResponseTime;
    
    /**
     * 中位数响应时间（毫秒）
     */
    private double medianResponseTime;
    
    /**
     * 90分位响应时间（毫秒）
     */
    private double p90ResponseTime;
    
    /**
     * 95分位响应时间（毫秒）
     */
    private double p95ResponseTime;
    
    /**
     * 99分位响应时间（毫秒）
     */
    private double p99ResponseTime;
    
    /**
     * 吞吐量（请求/秒）
     */
    private double throughput;
    
    /**
     * 并发数
     */
    private int concurrency;
    
    /**
     * CPU使用率
     */
    private double cpuUsage;
    
    /**
     * 内存使用量（MB）
     */
    private double memoryUsage;
    
    /**
     * 错误率
     */
    public double getErrorRate() {
        if (totalRequests == 0) {
            return 0;
        }
        return (double) failedRequests / totalRequests;
    }
    
    /**
     * 测试持续时间（秒）
     */
    public double getDuration() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return Duration.between(startTime, endTime).toMillis() / 1000.0;
    }
    
    /**
     * 获取性能测试摘要
     */
    public String getSummary() {
        return String.format(
            "测试: %s\n" +
            "持续时间: %.2f秒\n" +
            "总请求数: %d, 成功: %d, 失败: %d\n" +
            "错误率: %.2f%%\n" +
            "吞吐量: %.2f req/s\n" +
            "响应时间 - 平均: %.2fms, 中位数: %.2fms, P90: %.2fms, P95: %.2fms, P99: %.2fms\n" +
            "响应时间范围: %.2fms - %.2fms\n" +
            "并发数: %d\n" +
            "资源使用 - CPU: %.2f%%, 内存: %.2fMB",
            testName,
            getDuration(),
            totalRequests, successfulRequests, failedRequests,
            getErrorRate() * 100,
            throughput,
            averageResponseTime, medianResponseTime, p90ResponseTime, p95ResponseTime, p99ResponseTime,
            minResponseTime, maxResponseTime,
            concurrency,
            cpuUsage, memoryUsage
        );
    }
    
    /**
     * 判断性能是否达标
     * 
     * @param maxResponseTime 最大可接受响应时间
     * @param maxErrorRate 最大可接受错误率
     * @return 是否达标
     */
    public boolean meetsPerformanceCriteria(double maxResponseTime, double maxErrorRate) {
        return averageResponseTime <= maxResponseTime && getErrorRate() <= maxErrorRate;
    }
}