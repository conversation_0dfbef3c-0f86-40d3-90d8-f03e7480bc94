package com.financial.test.performance;

import lombok.Data;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 性能测试报告
 * 用于生成和导出性能测试结果
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
public class PerformanceReport {
    
    private String reportName;
    private LocalDateTime reportTime;
    private Map<Integer, PerformanceMetrics> loadTestResults;
    private List<PerformanceMetrics> allMetrics;
    
    public PerformanceReport(String reportName) {
        this.reportName = reportName;
        this.reportTime = LocalDateTime.now();
        this.loadTestResults = new TreeMap<>();
        this.allMetrics = new ArrayList<>();
    }
    
    /**
     * 添加负载测试结果
     */
    public void addMetrics(int threads, PerformanceMetrics metrics) {
        loadTestResults.put(threads, metrics);
        allMetrics.add(metrics);
    }
    
    /**
     * 添加单个测试结果
     */
    public void addMetrics(PerformanceMetrics metrics) {
        allMetrics.add(metrics);
    }
    
    /**
     * 生成文本格式报告
     */
    public String generateTextReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=".repeat(80)).append("\n");
        report.append(String.format("性能测试报告: %s\n", reportName));
        report.append(String.format("生成时间: %s\n", 
            reportTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)));
        report.append("=".repeat(80)).append("\n\n");
        
        if (!loadTestResults.isEmpty()) {
            report.append("负载测试结果:\n");
            report.append("-".repeat(80)).append("\n");
            report.append(String.format("%-10s %-10s %-10s %-10s %-10s %-10s %-10s\n",
                "线程数", "吞吐量", "平均响应", "P90", "P95", "P99", "错误率"));
            report.append("-".repeat(80)).append("\n");
            
            for (Map.Entry<Integer, PerformanceMetrics> entry : loadTestResults.entrySet()) {
                PerformanceMetrics metrics = entry.getValue();
                report.append(String.format("%-10d %-10.2f %-10.2f %-10.2f %-10.2f %-10.2f %-10.2f%%\n",
                    entry.getKey(),
                    metrics.getThroughput(),
                    metrics.getAverageResponseTime(),
                    metrics.getP90ResponseTime(),
                    metrics.getP95ResponseTime(),
                    metrics.getP99ResponseTime(),
                    metrics.getErrorRate() * 100));
            }
            report.append("\n");
            
            // 找出最佳性能点
            Map.Entry<Integer, PerformanceMetrics> bestThroughput = loadTestResults.entrySet().stream()
                .max(Map.Entry.comparingByValue((a, b) -> Double.compare(a.getThroughput(), b.getThroughput())))
                .orElse(null);
            
            if (bestThroughput != null) {
                report.append(String.format("最佳吞吐量: %.2f req/s @ %d 线程\n",
                    bestThroughput.getValue().getThroughput(),
                    bestThroughput.getKey()));
            }
        }
        
        // 所有测试的汇总统计
        if (!allMetrics.isEmpty()) {
            report.append("\n详细测试结果:\n");
            report.append("-".repeat(80)).append("\n");
            
            for (PerformanceMetrics metrics : allMetrics) {
                report.append(metrics.getSummary()).append("\n");
                report.append("-".repeat(80)).append("\n");
            }
        }
        
        // 生成建议
        report.append("\n性能分析建议:\n");
        report.append(generateRecommendations());
        
        return report.toString();
    }
    
    /**
     * 生成CSV格式报告
     */
    public String generateCsvReport() {
        StringBuilder csv = new StringBuilder();
        
        // CSV头
        csv.append("测试名称,线程数,总请求数,成功请求数,失败请求数,错误率,")
            .append("吞吐量,平均响应时间,中位数响应时间,P90,P95,P99,")
            .append("最小响应时间,最大响应时间,CPU使用率,内存使用量\n");
        
        // 数据行
        for (PerformanceMetrics metrics : allMetrics) {
            csv.append(String.format("%s,%d,%d,%d,%d,%.4f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\n",
                metrics.getTestName(),
                metrics.getConcurrency(),
                metrics.getTotalRequests(),
                metrics.getSuccessfulRequests(),
                metrics.getFailedRequests(),
                metrics.getErrorRate(),
                metrics.getThroughput(),
                metrics.getAverageResponseTime(),
                metrics.getMedianResponseTime(),
                metrics.getP90ResponseTime(),
                metrics.getP95ResponseTime(),
                metrics.getP99ResponseTime(),
                metrics.getMinResponseTime(),
                metrics.getMaxResponseTime(),
                metrics.getCpuUsage(),
                metrics.getMemoryUsage()));
        }
        
        return csv.toString();
    }
    
    /**
     * 导出报告到文件
     */
    public void exportToFile(String directory) throws IOException {
        Path dir = Paths.get(directory);
        if (!Files.exists(dir)) {
            Files.createDirectories(dir);
        }
        
        String timestamp = reportTime.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String baseFileName = String.format("%s_%s", reportName.replaceAll("[^a-zA-Z0-9]", "_"), timestamp);
        
        // 导出文本报告
        Path textFile = dir.resolve(baseFileName + ".txt");
        Files.write(textFile, generateTextReport().getBytes());
        
        // 导出CSV报告
        Path csvFile = dir.resolve(baseFileName + ".csv");
        Files.write(csvFile, generateCsvReport().getBytes());
    }
    
    /**
     * 生成性能优化建议
     */
    private String generateRecommendations() {
        List<String> recommendations = new ArrayList<>();
        
        if (!loadTestResults.isEmpty()) {
            // 分析吞吐量趋势
            List<Map.Entry<Integer, PerformanceMetrics>> entries = new ArrayList<>(loadTestResults.entrySet());
            
            // 检查是否存在吞吐量下降
            for (int i = 1; i < entries.size(); i++) {
                PerformanceMetrics prev = entries.get(i - 1).getValue();
                PerformanceMetrics curr = entries.get(i).getValue();
                
                if (curr.getThroughput() < prev.getThroughput() * 0.9) {
                    recommendations.add(String.format(
                        "• 在 %d 线程时吞吐量开始下降，建议优化并发处理能力",
                        entries.get(i).getKey()));
                    break;
                }
            }
            
            // 检查响应时间
            OptionalDouble avgP95 = loadTestResults.values().stream()
                .mapToDouble(PerformanceMetrics::getP95ResponseTime)
                .average();
            
            if (avgP95.isPresent() && avgP95.getAsDouble() > 1000) {
                recommendations.add("• P95响应时间超过1秒，建议优化慢查询和算法效率");
            }
            
            // 检查错误率
            OptionalDouble avgErrorRate = loadTestResults.values().stream()
                .mapToDouble(PerformanceMetrics::getErrorRate)
                .average();
            
            if (avgErrorRate.isPresent() && avgErrorRate.getAsDouble() > 0.01) {
                recommendations.add("• 错误率超过1%，需要检查系统稳定性和错误处理");
            }
        }
        
        // 资源使用建议
        OptionalDouble avgCpu = allMetrics.stream()
            .mapToDouble(PerformanceMetrics::getCpuUsage)
            .filter(cpu -> cpu >= 0)
            .average();
        
        if (avgCpu.isPresent() && avgCpu.getAsDouble() > 80) {
            recommendations.add("• CPU使用率较高，考虑优化计算密集型操作或增加服务器资源");
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("• 性能表现良好，继续保持当前优化水平");
        }
        
        return String.join("\n", recommendations);
    }
}