package com.financial.test.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 测试数据清理工具
 */
@Slf4j
@Component
public class TestDataCleaner {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final List<String> TABLES_TO_CLEAN = Arrays.asList(
        "overdue_debt_add",
        "overdue_debt_decrease",
        "impairment_reserve",
        "litigation_claim",
        "non_litigation_claim",
        "audit_log"
    );
    
    /**
     * 清理所有测试数据
     */
    @Transactional
    public void cleanAllTestData() {
        log.info("开始清理测试数据...");
        
        // 禁用外键检查
        jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 0");
        
        try {
            for (String table : TABLES_TO_CLEAN) {
                cleanTable(table);
            }
        } finally {
            // 重新启用外键检查
            jdbcTemplate.execute("SET FOREIGN_KEY_CHECKS = 1");
        }
        
        log.info("测试数据清理完成");
    }
    
    /**
     * 清理特定命名空间的测试数据
     */
    @Transactional
    public void cleanTestData(String namespace) {
        log.info("清理命名空间 {} 的测试数据", namespace);
        
        String pattern = namespace + "%";
        
        // 清理债权相关表
        jdbcTemplate.update(
            "DELETE FROM overdue_debt_add WHERE creditor LIKE ? OR debtor LIKE ?",
            pattern, pattern
        );
        
        jdbcTemplate.update(
            "DELETE FROM overdue_debt_decrease WHERE creditor LIKE ? OR debtor LIKE ?",
            pattern, pattern
        );
        
        jdbcTemplate.update(
            "DELETE FROM impairment_reserve WHERE creditor LIKE ? OR debtor LIKE ?",
            pattern, pattern
        );
        
        // 清理诉讼相关表
        jdbcTemplate.update(
            "DELETE FROM litigation_claim WHERE creditor LIKE ? OR debtor LIKE ?",
            pattern, pattern
        );
        
        jdbcTemplate.update(
            "DELETE FROM non_litigation_claim WHERE creditor LIKE ? OR debtor LIKE ?",
            pattern, pattern
        );
        
        log.info("命名空间 {} 的数据清理完成", namespace);
    }
    
    /**
     * 清理测试生成的数据（保留基准数据）
     */
    @Transactional
    public void cleanTestData() {
        log.info("清理测试生成的数据（保留基准数据）");
        
        // 只清理以TEST_开头的数据
        String[] testPrefixes = {"TEST_", "TEMP_", "MOCK_"};
        
        for (String prefix : testPrefixes) {
            String pattern = prefix + "%";
            
            jdbcTemplate.update(
                "DELETE FROM overdue_debt_add WHERE creditor LIKE ? OR debtor LIKE ?",
                pattern, pattern
            );
            
            jdbcTemplate.update(
                "DELETE FROM overdue_debt_decrease WHERE creditor LIKE ? OR debtor LIKE ?",
                pattern, pattern
            );
        }
    }
    
    /**
     * 清理单个表
     */
    private void cleanTable(String tableName) {
        try {
            int rowsDeleted = jdbcTemplate.update("TRUNCATE TABLE " + tableName);
            log.debug("清理表 {}", tableName);
        } catch (Exception e) {
            log.warn("清理表 {} 失败: {}", tableName, e.getMessage());
            // 如果TRUNCATE失败，尝试DELETE
            try {
                jdbcTemplate.update("DELETE FROM " + tableName);
            } catch (Exception ex) {
                log.error("删除表 {} 数据失败", tableName, ex);
            }
        }
    }
    
    /**
     * 获取表的记录数
     */
    public long getTableCount(String tableName) {
        return jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM " + tableName, 
            Long.class
        );
    }
    
    /**
     * 验证数据是否已清理
     */
    public boolean isDataClean() {
        for (String table : TABLES_TO_CLEAN) {
            if (getTableCount(table) > 0) {
                return false;
            }
        }
        return true;
    }
}