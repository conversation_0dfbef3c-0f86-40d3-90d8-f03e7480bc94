# 测试环境配置
spring.profiles.active=test

# 数据库配置 - 使用内存数据库或测试数据库
spring.datasource.primary.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.primary.username=sa
spring.datasource.primary.password=
spring.datasource.primary.driver-class-name=org.h2.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# 日志配置
logging.level.com.financial=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.test=DEBUG

# 测试特定配置
spring.test.database.replace=none
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# 禁用不必要的自动配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration