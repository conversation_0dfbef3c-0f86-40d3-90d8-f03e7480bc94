# FinancialSystem 测试框架

## 概述

这是FinancialSystem项目的专用测试框架，提供了一套完整的测试工具和基础设施，用于确保代码质量和防止功能回归。

## 🚀 新增功能 (2025-07-24)

### 1. 性能测试支持
- **BasePerformanceTest**: 性能测试基类，提供性能指标收集和验证
- **PerformanceCollector**: 自动收集响应时间、吞吐量、CPU/内存使用等指标
- **PerformanceReport**: 生成详细的性能测试报告（支持文本和CSV格式）

### 2. 并发测试支持
- **BaseConcurrencyTest**: 并发测试基类，支持多线程场景测试
- 竞态条件检测
- 死锁检测
- 线程安全性验证

### 3. 测试工具增强
- **TestDataLoader**: 灵活的测试数据加载器，支持JSON格式的场景数据
- **ScenarioTestExecutor**: 场景化测试执行器，支持复杂业务场景的自动化测试

### 4. 场景测试数据扩展
- 边界条件测试数据（极限值、零值等）
- 异常场景测试数据（无效数据、约束违反等）
- 复杂业务场景数据（混合处置、跨年处理等）
- 性能测试数据集（小、中、大规模）

## 模块结构

```
test-framework/
├── test-core/          # 核心测试工具和基类
│   ├── base/          # 测试基类（单元、集成、一致性、性能、并发）
│   ├── utils/         # 测试工具类
│   ├── data/          # 测试数据管理
│   ├── snapshot/      # 快照测试支持
│   └── performance/   # 性能测试组件
└── test-data/         # 测试数据集
    ├── baseline/      # 基准数据
    ├── scenarios/     # 场景数据
    │   ├── boundary-cases/    # 边界条件
    │   ├── exception-cases/   # 异常场景
    │   ├── business-scenarios/# 业务场景
    │   └── performance/       # 性能测试
    └── snapshots/     # 查询结果快照
```

## 快速开始

### 1. 添加依赖

在您的模块的`pom.xml`中添加测试框架依赖：

```xml
<dependency>
    <groupId>com.financial</groupId>
    <artifactId>test-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <scope>test</scope>
</dependency>
```

### 2. 编写单元测试

```java
import com.financial.test.base.BaseUnitTest;

class DebtServiceTest extends BaseUnitTest {
    
    @Mock
    private DebtRepository debtRepository;
    
    @InjectMocks
    private DebtService debtService;
    
    @Test
    void should_calculateInterest_when_validDebtProvided() {
        // Given
        DebtRecord debt = TestDataFactory.createDefaultDebtRecord();
        when(debtRepository.findById(1L)).thenReturn(Optional.of(debt));
        
        // When
        BigDecimal interest = debtService.calculateInterest(1L);
        
        // Then
        assertThat(interest).isNotNull().isPositive();
    }
}
```

### 3. 编写集成测试

```java
import com.financial.test.base.BaseIntegrationTest;

class DebtControllerIntegrationTest extends BaseIntegrationTest {
    
    @Test
    void should_createDebt_when_validRequestProvided() throws Exception {
        // Given
        String namespace = getTestNamespace();
        DebtRequest request = TestDataFactory.debtRecord()
            .withCreditor(namespace + "_CREDITOR")
            .withAmount(new BigDecimal("100000"))
            .build();
        
        // When & Then
        mockMvc.perform(post("/api/debts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.creditor").value(request.getCreditor()));
    }
}
```

### 4. 编写数据一致性测试

```java
import com.financial.test.base.BaseConsistencyTest;

class DebtConsistencyTest extends BaseConsistencyTest {
    
    @Override
    protected void captureBaselineSnapshots() {
        // 捕获基准查询快照
        createSnapshot("all_debts_2024", debtService.findAllDebts(2024));
        createSnapshot("monthly_summary", debtService.getMonthlySummary(2024, 6));
    }
    
    @Test
    void should_maintainQueryConsistency_after_codeChanges() {
        // 执行相同的查询
        List<DebtRecord> currentDebts = debtService.findAllDebts(2024);
        
        // 验证结果与快照一致
        verifySnapshot("all_debts_2024", currentDebts);
    }
    
    @Test
    void should_maintainCrossTableConsistency_when_debtAdded() {
        executeWithConsistencyCheck("add_debt", () -> {
            DebtRequest request = TestDataFactory.createDebtRequest();
            return debtService.addDebt(request);
        });
    }
}
```

## 核心功能

### 1. 测试基类

- **BaseUnitTest**: 提供Mockito支持的单元测试基类
- **BaseIntegrationTest**: 提供Spring Boot完整上下文的集成测试基类
- **BaseConsistencyTest**: 提供数据一致性验证的测试基类
- **BasePerformanceTest**: 提供性能测试的基类（新增）
- **BaseConcurrencyTest**: 提供并发测试的基类（新增）

### 2. 测试数据管理

#### TestDataFactory
```java
// 创建默认测试数据
DebtRecord debt = TestDataFactory.createDefaultDebtRecord();

// 创建特定场景数据
DebtRecord largeDebt = TestDataFactory.createDebtRecordForScenario(TestScenario.LARGE_AMOUNT);

// 批量创建数据
List<DebtRecord> debts = TestDataFactory.createDebtRecords(100);

// 创建月度分布数据
Map<String, BigDecimal> monthlyData = TestDataFactory.createMonthlyDistribution(
    new BigDecimal("1200000"), 
    DistributionPattern.QUARTERLY
);
```

#### TestDataCleaner
```java
// 清理所有测试数据
testDataCleaner.cleanAllTestData();

// 清理特定命名空间的数据
testDataCleaner.cleanTestData("TEST_NAMESPACE_123");

// 验证数据是否已清理
assertTrue(testDataCleaner.isDataClean());
```

### 3. 快照测试

```java
// 创建快照
snapshotManager.createSnapshot("query_result_001", queryResult);

// 验证快照
SnapshotVerifier.verify("query_result_001", currentResult)
    .withComparator(new CustomComparator())
    .ignoreOrder()
    .assertMatches();

// 更新快照
snapshotManager.updateSnapshot("query_result_001", newResult);
```

### 4. 数据一致性检查

```java
// 检查债权操作后的一致性
ConsistencyChecker.CheckResult result = consistencyChecker.checkDebtConsistency(
    creditor, debtor, year
);

if (!result.isConsistent()) {
    fail("数据一致性检查失败: " + result.getSummary());
}
```

### 5. 事务管理

```java
// 在新事务中执行
DebtRecord result = testTransactionManager.executeInNewTransaction(() -> {
    return debtService.addDebt(request);
});

// 执行并回滚（用于测试）
testTransactionManager.executeAndRollback(() -> {
    debtService.addDebt(request);
    // 验证数据
    return null;
});
```

### 6. 性能测试（新增）

```java
import com.financial.test.base.BasePerformanceTest;

class DebtServicePerformanceTest extends BasePerformanceTest {
    
    @Override
    protected String getTestName() {
        return "债权服务性能测试";
    }
    
    @Test
    void test_queryPerformance() {
        // 执行性能测试
        PerformanceMetrics metrics = executePerformanceTest(
            "债权查询性能",
            () -> debtService.findByCreditor("测试债权人"),
            1000  // 执行1000次
        );
        
        // 验证性能指标
        assertThat(metrics.getAverageResponseTime()).isLessThan(100);
        assertThat(metrics.getErrorRate()).isLessThan(0.01);
    }
    
    @Test
    void test_concurrentPerformance() {
        // 并发性能测试
        PerformanceMetrics metrics = executeConcurrentPerformanceTest(
            "并发查询测试",
            () -> debtService.findAll(),
            50,    // 50个线程
            10,    // 持续10秒
            TimeUnit.SECONDS
        );
        
        assertThat(metrics.getThroughput()).isGreaterThan(100);
    }
    
    @Test
    void test_loadTest() {
        // 负载测试
        PerformanceReport report = executeLoadTest(
            "债权查询负载测试",
            () -> debtService.findByYear(2024),
            10,    // 起始10线程
            100,   // 最大100线程
            10,    // 每次增加10线程
            5,     // 每步持续5秒
            TimeUnit.SECONDS
        );
        
        // 导出性能报告
        report.exportToFile("target/performance-reports");
    }
}
```

### 7. 并发测试（新增）

```java
import com.financial.test.base.BaseConcurrencyTest;

class DebtServiceConcurrencyTest extends BaseConcurrencyTest {
    
    @Test
    void test_concurrentDebtCreation() {
        // 测试并发创建债权
        ConcurrencyTestResult<Long> result = executeConcurrentTest(
            () -> debtService.createDebt(TestDataFactory.createDebtRequest()),
            10,    // 10个线程
            100    // 每个线程创建100条
        );
        
        assertThat(result.getSuccessRate()).isEqualTo(1.0);
        assertThat(result.getExceptions()).isEmpty();
    }
    
    @Test
    void test_raceCondition() {
        // 竞态条件测试
        RaceConditionTestResult result = testRaceCondition(
            () -> debtService.initializeCounter(),  // 设置
            () -> debtService.incrementCounter(),    // 操作
            (initial, finalVal, concResult) -> {    // 检查器
                int expected = initial + concResult.getSuccessCount();
                return finalVal.equals(expected);
            },
            100  // 100个线程
        );
        
        assertThat(result.hasRaceCondition()).isFalse();
    }
    
    @Test
    void test_deadlockDetection() {
        // 死锁检测测试
        DeadlockTestResult result = testDeadlock(
            () -> debtService.performComplexTransaction(),
            10,   // 10个线程
            30    // 30秒超时
        );
        
        assertThat(result.hasDeadlock()).isFalse();
    }
}
```

### 8. 场景测试（新增）

```java
// 加载场景测试数据
TestDataLoader loader = new TestDataLoader();
ScenarioData extremeValues = loader.loadScenario("boundary-cases/extreme-values.json");

// 执行场景测试
ScenarioTestExecutor executor = new ScenarioTestExecutor(jdbcTemplate, transactionTemplate);
ScenarioExecutionResult result = executor.execute(extremeValues);

// 验证结果
assertThat(result.isSuccess()).isTrue();
assertThat(result.getAssertionResults())
    .allMatch(AssertionResult::isPassed);

// 批量执行多个场景
List<ScenarioData> scenarios = Arrays.asList(
    loader.loadScenario("boundary-cases/extreme-values.json"),
    loader.loadScenario("exception-cases/invalid-data.json"),
    loader.loadScenario("business-scenarios/complex-disposal.json")
);

BatchExecutionResult batchResult = executor.executeBatch(scenarios);
log.info(batchResult.getSummary());
```

## 最佳实践

### 1. 测试命名规范
```java
// 单元测试
void should_expectedBehavior_when_condition()

// 集成测试  
void test_scenario_expectedOutcome()

// 一致性测试
void verify_aspect_condition()
```

### 2. 数据隔离
```java
// 使用命名空间隔离测试数据
String namespace = getTestNamespace(); // 返回 "TestClassName_timestamp"
DebtRecord debt = TestDataFactory.debtRecord()
    .withCreditor(namespace + "_CREDITOR")
    .build();
```

### 3. 清理策略
```java
@AfterEach
void cleanup() {
    // 自动清理以TEST_开头的数据
    testDataCleaner.cleanTestData();
}
```

### 4. 断言使用
```java
// 使用AssertJ进行流式断言
assertThat(result)
    .isNotNull()
    .hasFieldOrPropertyWithValue("creditor", "测试债权人")
    .matches(debt -> debt.getAmount().compareTo(BigDecimal.ZERO) > 0);
```

## 故障排除

### 问题1：快照不匹配
```bash
# 更新快照
mvn test -Dsnapshot.update=true

# 查看差异报告
cat target/snapshot-diffs/diff-report.html
```

### 问题2：测试数据污染
```java
// 确保使用@Transactional和@Rollback
@Test
@Transactional
@Rollback
void test_should_not_pollute_database() {
    // 测试代码
}
```

### 问题3：并发测试失败
```java
// 使用独立的命名空间
String namespace = "CONCURRENT_" + UUID.randomUUID();
```

## 扩展框架

### 添加自定义比较器
```java
public class BigDecimalComparator implements SnapshotComparator {
    @Override
    public boolean compare(Object expected, Object actual) {
        if (expected instanceof BigDecimal && actual instanceof BigDecimal) {
            BigDecimal e = (BigDecimal) expected;
            BigDecimal a = (BigDecimal) actual;
            return e.compareTo(a) == 0;
        }
        return Objects.equals(expected, actual);
    }
}
```

### 添加自定义断言
```java
public class DebtAssertions {
    public static void assertDebtValid(DebtRecord debt) {
        assertThat(debt).isNotNull();
        assertThat(debt.getAmount()).isPositive();
        assertThat(debt.getCreditor()).isNotBlank();
        assertThat(debt.getDebtor()).isNotBlank();
    }
}
```

## 贡献指南

1. 遵循现有的代码风格
2. 为新功能添加测试
3. 更新相关文档
4. 提交PR前运行所有测试

## 相关文档

- [测试策略](../docs/testing/test-strategy.md)
- [数据一致性测试指南](../docs/testing/data-consistency-testing.md)
- [测试实施指南](../docs/testing/test-implementation-guide.md)
- [测试数据准备指南](../docs/testing/test-data-setup.md)