/**
 * 资产管理页面 - 集成新通知系统示例
 * 展示如何在实际业务场景中使用统一的通知系统
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
} from '@mui/icons-material';

// 导入新的通知系统
import { useNotifications } from 'components/feedback/NotificationManager';

const AssetManagementWithNotifications = () => {
  const notifications = useNotifications();
  const [assets, setAssets] = useState([
    { id: 1, name: '办公楼A', location: '北京市朝阳区', area: 1000, status: '正常' },
    { id: 2, name: '仓库B', location: '上海市浦东区', area: 2000, status: '正常' },
  ]);
  const [editingAsset, setEditingAsset] = useState(null);
  const [formData, setFormData] = useState({ name: '', location: '', area: '' });

  // 添加资产
  const handleAddAsset = () => {
    if (!formData.name || !formData.location || !formData.area) {
      notifications.warning('请填写所有必填字段：资产名称、位置、面积', {
        title: '表单验证',
        duration: 7000, // 表单验证错误使用7秒
      });
      return;
    }

    // 模拟API调用
    setTimeout(() => {
      const newAsset = {
        id: Date.now(),
        name: formData.name,
        location: formData.location,
        area: parseInt(formData.area),
        status: '正常',
      };

      setAssets(prev => [...prev, newAsset]);
      setFormData({ name: '', location: '', area: '' });

      notifications.success('资产添加成功！', {
        title: '操作成功',
      });
    }, 1000);

    // 显示加载状态
    notifications.info('正在添加资产...', {
      title: '处理中',
      duration: 1000,
    });
  };

  // 编辑资产
  const handleEditAsset = asset => {
    setEditingAsset(asset.id);
    setFormData({
      name: asset.name,
      location: asset.location,
      area: asset.area.toString(),
    });
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (!formData.name || !formData.location || !formData.area) {
      notifications.warning('请填写所有必填字段', {
        title: '表单验证',
      });
      return;
    }

    notifications.confirm('确定要保存这些更改吗？', {
      title: '确认保存',
      confirmText: '保存',
      cancelText: '取消',
      onConfirm: () => {
        // 模拟保存
        setTimeout(() => {
          setAssets(prev =>
            prev.map(asset =>
              asset.id === editingAsset
                ? {
                  ...asset,
                  name: formData.name,
                  location: formData.location,
                  area: parseInt(formData.area),
                }
                : asset,
            ),
          );
          setEditingAsset(null);
          setFormData({ name: '', location: '', area: '' });

          notifications.success('资产信息已更新！');
        }, 500);
      },
      onCancel: () => {
        notifications.info('取消保存');
      },
    });
  };

  // 删除资产
  const handleDeleteAsset = asset => {
    notifications.confirm(`确定要删除资产"${asset.name}"吗？此操作不可撤销。`, {
      title: '确认删除',
      confirmText: '删除',
      cancelText: '取消',
      type: 'delete',
      onConfirm: () => {
        // 模拟删除API调用
        setTimeout(() => {
          setAssets(prev => prev.filter(a => a.id !== asset.id));
          notifications.success('资产已删除');
        }, 500);
      },
    });
  };

  // 批量操作示例
  const handleBatchOperation = () => {
    notifications.info('正在执行批量操作...', {
      title: '批量处理',
      duration: 0, // 不自动消失
    });

    // 模拟批量操作
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% 成功率

      if (success) {
        notifications.success('批量操作完成！共处理 2 条记录', {
          title: '批量操作成功',
        });
      } else {
        notifications.error('批量操作失败：网络连接超时', {
          title: '批量操作失败',
          duration: 8000, // 网络错误使用8秒
        });
      }
    }, 3000);
  };

  // 数据导出示例
  const handleExportData = () => {
    notifications.toast('开始导出数据...', 'info');

    setTimeout(() => {
      notifications.toast('导出完成', 'success');
    }, 2000);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        资产管理 - 通知系统集成示例
      </Typography>

      {/* 添加资产表单 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {editingAsset ? '编辑资产' : '添加资产'}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="资产名称"
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="位置"
                value={formData.location}
                onChange={e => setFormData(prev => ({ ...prev, location: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="面积(㎡)"
                type="number"
                value={formData.area}
                onChange={e => setFormData(prev => ({ ...prev, area: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={editingAsset ? <SaveIcon /> : <AddIcon />}
                onClick={editingAsset ? handleSaveEdit : handleAddAsset}
                sx={{ height: '56px' }}
              >
                {editingAsset ? '保存' : '添加'}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button variant="outlined" onClick={handleBatchOperation}>
          批量操作示例
        </Button>
        <Button variant="outlined" onClick={handleExportData}>
          导出数据
        </Button>
      </Box>

      {/* 资产列表 */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>资产名称</TableCell>
              <TableCell>位置</TableCell>
              <TableCell>面积(㎡)</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {assets.map(asset => (
              <TableRow key={asset.id}>
                <TableCell>{asset.name}</TableCell>
                <TableCell>{asset.location}</TableCell>
                <TableCell>{asset.area}</TableCell>
                <TableCell>{asset.status}</TableCell>
                <TableCell>
                  <IconButton size="small" onClick={() => handleEditAsset(asset)} color="primary">
                    <EditIcon />
                  </IconButton>
                  <IconButton size="small" onClick={() => handleDeleteAsset(asset)} color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 使用说明 */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            通知系统特性演示
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • 所有弹窗都在页面正中央显示
            <br />
            • 根据消息类型自动设置显示时长
            <br />
            • 表单验证错误显示7秒
            <br />
            • 网络错误显示8秒
            <br />
            • 成功消息显示3秒
            <br />
            • Toast提示显示2秒
            <br />• 确认对话框需要用户操作才会关闭
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AssetManagementWithNotifications;
