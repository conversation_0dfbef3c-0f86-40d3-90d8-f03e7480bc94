/**
 * App.js 集成示例
 * 展示如何在应用中集成新的通知系统
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// 导入新的通知系统
import { NotificationProvider } from 'components/feedback/NotificationManager';

// 导入主题
import theme from 'assets/theme';

// 导入页面组件
import Dashboard from 'layouts/dashboard';
import AssetManagement from 'layouts/assetmanagement';
import DebtManagement from 'layouts/debtmanagement';

// 导入示例页面
import NotificationExamples from 'components/feedback/NotificationExamples';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {/* 在最外层包装NotificationProvider */}
      <NotificationProvider>
        <Router>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/asset-management" element={<AssetManagement />} />
            <Route path="/debt-management" element={<DebtManagement />} />
            <Route path="/notification-examples" element={<NotificationExamples />} />
          </Routes>
        </Router>
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;
