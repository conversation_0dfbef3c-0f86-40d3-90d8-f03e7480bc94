import React from 'react';
import { render, screen } from '@testing-library/react';
import CompanyProgressChart from '../CompanyProgressChart';

describe('CompanyProgressChart', () => {
  const mockData = [
    {
      companyName: '中筑天佑',
      yearEndAmount: 11224,
      cumulativeRecovery: 10324,
      periodEndAmount: 900,
      completionRate: 103,
      targetAmount: 10000,
    },
    {
      companyName: '贵州中恒',
      yearEndAmount: 8500,
      cumulativeRecovery: 7200,
      periodEndAmount: 1300,
      completionRate: 85,
      targetAmount: 8500,
    },
    {
      companyName: '中建西南',
      yearEndAmount: 6800,
      cumulativeRecovery: 6100,
      periodEndAmount: 700,
      completionRate: 90,
      targetAmount: 6800,
    },
  ];

  test('renders without crashing', () => {
    render(<CompanyProgressChart data={mockData} />);
    expect(screen.getByText('各子公司存量债权回收完成情况')).toBeInTheDocument();
  });

  test('displays custom title', () => {
    const customTitle = '2024年度公司进度报告';
    render(<CompanyProgressChart data={mockData} title={customTitle} />);
    expect(screen.getByText(customTitle)).toBeInTheDocument();
  });

  test('shows no data message when data is empty', () => {
    render(<CompanyProgressChart data={[]} />);
    expect(screen.getByText('暂无数据')).toBeInTheDocument();
  });

  test('shows no data message when data is null', () => {
    render(<CompanyProgressChart data={null} />);
    expect(screen.getByText('暂无数据')).toBeInTheDocument();
  });

  test('displays statistics correctly', () => {
    render(<CompanyProgressChart data={mockData} />);

    // Check if company count is displayed
    expect(screen.getByText('3 家')).toBeInTheDocument();

    // Check if average completion rate is displayed (should be around 92.7%)
    expect(screen.getByText(/92\.\d%/)).toBeInTheDocument();

    // Check if highest completion company is displayed
    expect(screen.getByText('中筑天佑')).toBeInTheDocument();
    expect(screen.getByText('103.0%')).toBeInTheDocument();

    // Check if lowest completion company is displayed
    expect(screen.getByText('贵州中恒')).toBeInTheDocument();
    expect(screen.getByText('85.0%')).toBeInTheDocument();
  });

  test('renders more info button', () => {
    render(<CompanyProgressChart data={mockData} />);
    expect(screen.getByText('更多信息')).toBeInTheDocument();
  });

  test('handles invalid data gracefully', () => {
    const invalidData = [
      { companyName: null },
      { companyName: '测试公司' },
      {
        companyName: '有效公司',
        yearEndAmount: 1000,
        cumulativeRecovery: 800,
        periodEndAmount: 200,
        completionRate: 80,
      },
    ];

    render(<CompanyProgressChart data={invalidData} />);
    // Should only render the valid company
    expect(screen.getByText('1 家')).toBeInTheDocument();
  });
});
