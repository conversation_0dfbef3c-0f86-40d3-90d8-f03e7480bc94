import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Chart } from 'react-chartjs-2';
import {
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  Typography,
  IconButton,
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import CloseIcon from '@mui/icons-material/Close';
import DownloadIcon from '@mui/icons-material/Download';
import GenericDataTable from 'components/tables/GenericDataTable';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 各子公司存量债权回收完成情况图表组件
 * @param {Object} props
 * @param {Array} props.data - 公司进度数据数组
 * @param {String} props.title - 图表标题
 * @returns {JSX.Element}
 */
const CompanyProgressChart = ({ data, title = '各子公司存量债权回收完成情况' }) => {
  const [displayMode, setDisplayMode] = useState('target'); // 'target' 或 'initial'
  const [showDetailModal, setShowDetailModal] = useState(false);
  // 过滤掉无效数据、清收目标为0或没有数据的公司，以及母公司万润科技
  const validData = (data || []).filter(item => {
    if (!item || !item.companyName) {
      return false;
    }

    // 过滤掉母公司万润科技（因为只显示子公司数据）
    if (item.companyName.includes('万润科技')) {
      return false;
    }

    // 检查清收目标：必须存在且大于0
    const hasValidTarget = item.collectionTargetAmount && item.collectionTargetAmount > 0;

    return hasValidTarget;
  });

  // 如果没有数据，显示暂无数据提示
  if (!validData || validData.length === 0) {
    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          minWidth: '650px',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '450px',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
          <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
          <Box sx={{ fontSize: '14px' }}>暂无数据</Box>
        </Box>
      </Box>
    );
  }

  // 提取数据并计算堆叠柱状图数据
  const labels = validData.map(item => item.companyName);
  const cumulativeRecoveries = validData.map(item => item.cumulativeRecovery || 0);
  const completionRates = validData.map(item => item.completionRate || 0);

  // 根据显示模式计算目标金额和未完成金额
  const targetAmounts = validData.map(item => {
    if (displayMode === 'target') {
      return item.collectionTargetAmount || 0;
    } else {
      return item.yearEndAmount || 0;
    }
  });

  // 未完成金额 = 目标金额 - 累计清收金额
  const remainingAmounts = validData.map((item, index) => {
    const target = targetAmounts[index];
    const cumulative = cumulativeRecoveries[index];
    return Math.max(0, target - cumulative); // 确保不为负数
  });

  // 基于清收目标的完成比例（固定用清收目标计算）
  const targetBasedCompletionRates = validData.map(item => {
    const target = item.collectionTargetAmount || 0;
    const cumulative = item.cumulativeRecovery || 0;
    return target > 0 ? (cumulative / target) * 100 : 0;
  });

  // 计算统计信息（统一使用基于清收目标的完成率）
  const totalCompanies = validData.length;
  const avgCompletionRate =
    targetBasedCompletionRates.reduce((sum, rate) => sum + rate, 0) / totalCompanies;
  const maxCompletionRate = Math.max(...targetBasedCompletionRates);
  const minCompletionRate = Math.min(...targetBasedCompletionRates);
  const maxCompletionIndex = targetBasedCompletionRates.indexOf(maxCompletionRate);
  const minCompletionIndex = targetBasedCompletionRates.indexOf(minCompletionRate);
  const maxCompletionItem = {
    ...validData[maxCompletionIndex],
    completionRate: maxCompletionRate,
  };
  const minCompletionItem = {
    ...validData[minCompletionIndex],
    completionRate: minCompletionRate,
  };

  // 图表数据配置 - 堆叠柱状图 + 折线图
  const chartData = {
    labels,
    datasets: [
      {
        type: 'bar',
        label: '累计清收金额',
        data: cumulativeRecoveries,
        backgroundColor: 'rgba(76, 175, 80, 0.8)',
        borderColor: 'rgba(76, 175, 80, 1)',
        borderWidth: 1,
        stack: 'stack1', // 设置堆叠组
        yAxisID: 'y',
        order: 2,
        barPercentage: 0.6, // 减少柱子宽度约三分之一（从默认0.9减到0.6）
        categoryPercentage: 0.8,
      },
      {
        type: 'bar',
        label: '未完成金额',
        data: remainingAmounts,
        backgroundColor: 'rgba(244, 67, 54, 0.8)',
        borderColor: 'rgba(244, 67, 54, 1)',
        borderWidth: 1,
        stack: 'stack1', // 同一堆叠组
        yAxisID: 'y',
        order: 2,
        barPercentage: 0.6, // 减少柱子宽度约三分之一（从默认0.9减到0.6）
        categoryPercentage: 0.8,
      },
      {
        type: 'line',
        label: '完成进度（基于清收目标）',
        data: targetBasedCompletionRates,
        borderColor: 'rgba(76, 175, 80, 1)',
        backgroundColor: 'transparent',
        borderWidth: 3,
        tension: 0.4,
        fill: false,
        yAxisID: 'y1',
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: 'rgba(76, 175, 80, 1)',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointHoverBackgroundColor: 'rgba(76, 175, 80, 1)',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3,
        order: 1,
      },
    ],
  };

  // 图表选项配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    scales: {
      x: {
        display: true,
        stacked: true, // 启用x轴堆叠
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 11,
          },
          maxRotation: labels.length > 8 ? 45 : 0,
          minRotation: labels.length > 8 ? 45 : 0,
          autoSkip: false,
          callback: function (value, index) {
            const label = labels[index];
            // 如果公司名称过长，截断显示
            return label.length > 8 ? label.substring(0, 8) + '...' : label;
          },
        },
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        stacked: true, // 启用y轴堆叠
        title: {
          display: true,
          text: '金额 (万元)',
          font: {
            size: 12,
          },
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return value.toFixed(0);
          },
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: '完成进度 (%)',
          font: {
            size: 12,
            weight: 'bold',
          },
        },
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          callback: function (value) {
            return value.toFixed(0) + '%';
          },
          font: {
            weight: 'bold',
            size: 12,
          },
        },
      },
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: true,
        text: `${title} (${displayMode === 'target' ? '基于清收目标' : '基于期初金额'})`,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14,
          weight: 'medium',
        },
        bodyFont: {
          size: 12,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || '';
            if (context.datasetIndex === 2) {
              // 折线图 - 显示百分比
              return `${label}: ${context.parsed.y.toFixed(1)}%`;
            } else {
              // 柱状图 - 显示金额
              return `${label}: ${context.parsed.y.toFixed(2)} 万元`;
            }
          },
          afterBody: function (context) {
            if (context.length > 0) {
              const dataIndex = context[0].dataIndex;
              const targetAmount = targetAmounts[dataIndex];
              const cumulativeAmount = cumulativeRecoveries[dataIndex];
              const item = validData[dataIndex];
              const targetBasedRate = targetBasedCompletionRates[dataIndex];
              return [
                `期初金额: ${(item.yearEndAmount || 0).toFixed(2)} 万元`,
                `清收目标: ${(item.collectionTargetAmount || 0).toFixed(2)} 万元`,
                `累计清收: ${cumulativeAmount.toFixed(2)} 万元`,
                `期末余额: ${((item.yearEndAmount || 0) - cumulativeAmount).toFixed(2)} 万元`,
                `完成进度: ${targetBasedRate.toFixed(1)}%`,
              ];
            }
            return [];
          },
        },
      },
      datalabels: {
        display: function (context) {
          // 只在柱状图上显示数值，折线图不显示
          return context.dataset.type === 'bar';
        },
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          size: 9,
        },
        formatter(value) {
          return Math.round(value).toLocaleString();
        },
      },
    },
    layout: {
      padding: {
        left: 20,
        right: 40,
        top: 20,
        bottom: 20,
      },
    },
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    setShowDetailModal(true);
  };

  // 处理显示模式变更
  const handleDisplayModeChange = event => {
    setDisplayMode(event.target.value);
  };

  // 准备表格数据
  const tableData = validData.map(item => {
    const cumulativeAmount = item.cumulativeRecovery || 0;
    const targetAmount = item.collectionTargetAmount || 0;
    const initialAmount = item.yearEndAmount || 0;
    const periodEndAmount = initialAmount - cumulativeAmount;
    const targetBasedRate = targetAmount > 0 ? (cumulativeAmount / targetAmount) * 100 : 0;

    return {
      companyName: item.companyName,
      initialAmount: initialAmount,
      collectionTargetAmount: targetAmount,
      cumulativeRecovery: cumulativeAmount,
      periodEndAmount: periodEndAmount,
      completionRate: targetBasedRate,
    };
  });

  return (
    <>
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          marginBottom: '20px',
        }}
      >
        {/* 显示模式选择器 */}
        <Box sx={{ position: 'absolute', top: 8, left: 8, zIndex: 1 }}>
          <FormControl size="small">
            <RadioGroup row value={displayMode} onChange={handleDisplayModeChange} sx={{ gap: 1 }}>
              <FormControlLabel
                value="target"
                control={<Radio size="small" />}
                label="清收目标"
                sx={{ '& .MuiFormControlLabel-label': { fontSize: '12px' } }}
              />
              <FormControlLabel
                value="initial"
                control={<Radio size="small" />}
                label="期初金额"
                sx={{ '& .MuiFormControlLabel-label': { fontSize: '12px' } }}
              />
            </RadioGroup>
          </FormControl>
        </Box>

        {/* 更多信息按钮 */}
        <Button
          variant="text"
          size="small"
          startIcon={<InfoOutlinedIcon />}
          onClick={handleMoreInfo}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 1,
            color: 'primary.main',
            '&:hover': {
              backgroundColor: 'rgba(76, 175, 80, 0.08)',
            },
          }}
        >
          更多信息
        </Button>

        {/* 图表容器 - 支持横向滚动 */}
        <Box
          sx={{
            overflowX: labels.length > 8 ? 'auto' : 'hidden',
            overflowY: 'hidden',
            width: '100%',
            pt: 6, // 为显示模式选择器和按钮留出更多空间
          }}
        >
          <Box
            sx={{
              minWidth: labels.length > 8 ? `${labels.length * 100}px` : '100%',
              width: '100%',
              height: '400px',
            }}
          >
            <Chart type="bar" data={chartData} options={options} />
          </Box>
        </Box>

        {/* 数据汇总信息 */}
        <Box
          sx={{
            mt: 3,
            pt: 2,
            borderTop: '1px solid #e0e0e0',
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          {/* 第一行：总体统计 */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>公司总数：</Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold' }}>{totalCompanies} 家</Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>平均完成进度：</Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(255, 159, 64, 1)' }}>
                {avgCompletionRate.toFixed(1)}%
              </Box>
            </Box>
          </Box>

          {/* 第二行：总金额统计 */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-around',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>总清收目标：</Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'text.primary' }}>
                {targetAmounts.reduce((sum, amount) => sum + amount, 0).toFixed(2)} 万元
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>总累计清收：</Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(76, 175, 80, 1)' }}>
                {cumulativeRecoveries.reduce((sum, amount) => sum + amount, 0).toFixed(2)} 万元
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>总未完成金额：</Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(244, 67, 54, 1)' }}>
                {remainingAmounts.reduce((sum, amount) => sum + amount, 0).toFixed(2)} 万元
              </Box>
            </Box>
          </Box>

          {/* 第三行：最高最低完成进度 */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-around',
              flexWrap: 'wrap',
              gap: 2,
              mt: 1,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>完成进度最高：</Box>
              <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
                <Box sx={{ fontSize: '14px', fontWeight: 'medium' }}>
                  {maxCompletionItem.companyName}
                </Box>
                <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(76, 175, 80, 1)' }}>
                  {maxCompletionItem.completionRate.toFixed(1)}%
                </Box>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>完成进度最低：</Box>
              <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
                <Box sx={{ fontSize: '14px', fontWeight: 'medium' }}>
                  {minCompletionItem.companyName}
                </Box>
                <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(244, 67, 54, 1)' }}>
                  {minCompletionItem.completionRate.toFixed(1)}%
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* 详细信息模态框 */}
      <Dialog
        open={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { minHeight: '400px' }, // 减少高度三分之一：从600px到400px
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            pb: 1,
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            各子公司存量债权回收完成情况 - 详细数据
          </Typography>
          <IconButton
            onClick={() => setShowDetailModal(false)}
            sx={{
              color: 'grey.500',
              '&:hover': {
                backgroundColor: 'grey.100',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <GenericDataTable
              columns={[
                {
                  field: 'companyName',
                  headerName: '公司名称',
                  width: '16%', // 缩短列宽，5个汉字长度约16%
                },
                {
                  field: 'initialAmount',
                  headerName: '期初金额',
                  width: '14%', // 缩短列宽
                  type: 'number',
                  renderCell: value => value?.toFixed(2) || '0.00',
                },
                {
                  field: 'collectionTargetAmount',
                  headerName: '清收目标',
                  width: '14%', // 缩短列宽
                  type: 'number',
                  renderCell: value => value?.toFixed(2) || '0.00',
                },
                {
                  field: 'cumulativeRecovery',
                  headerName: '累计清收金额',
                  width: '16%', // 稍微调整
                  type: 'number',
                  renderCell: value => (
                    <Box sx={{ color: 'rgba(76, 175, 80, 1)', fontWeight: 'medium' }}>
                      {value?.toFixed(2) || '0.00'}
                    </Box>
                  ),
                },
                {
                  field: 'periodEndAmount',
                  headerName: '期末余额',
                  width: '14%', // 缩短列宽
                  type: 'number',
                  renderCell: value => (
                    <Box sx={{ color: value > 0 ? 'rgba(244, 67, 54, 1)' : 'text.primary' }}>
                      {value?.toFixed(2) || '0.00'}
                    </Box>
                  ),
                },
                {
                  field: 'completionRate',
                  headerName: '完成比例',
                  width: '12%', // 缩短列宽
                  type: 'number',
                  renderCell: value => {
                    const rate = value || 0;
                    let color;
                    if (rate >= 100) {
                      color = 'rgba(76, 175, 80, 1)';
                    } else if (rate >= 50) {
                      color = 'rgba(255, 152, 0, 1)';
                    } else {
                      color = 'rgba(244, 67, 54, 1)';
                    }
                    return <Box sx={{ color, fontWeight: 'bold' }}>{rate.toFixed(1)}%</Box>;
                  },
                },
              ]}
              data={tableData}
              pageSize={12}
              fontSize={13} // 稍微减小字体
              rowHeight={35} // 减少行高以适应更紧凑的布局
              showPagination={tableData.length > 12}
              compact={true} // 启用紧凑模式
            />

            {/* 统计汇总 */}
            <Box
              sx={{
                mt: 3,
                p: 2,
                bgcolor: 'background.paper',
                borderRadius: 1,
                border: '1px solid #e0e0e0',
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                统计汇总
              </Typography>
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: 2,
                }}
              >
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    总期初金额
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'text.primary' }}>
                    {tableData.reduce((sum, item) => sum + item.initialAmount, 0).toFixed(2)} 万元
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    总清收目标
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'text.primary' }}>
                    {tableData
                      .reduce((sum, item) => sum + item.collectionTargetAmount, 0)
                      .toFixed(2)}{' '}
                    万元
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    总累计清收
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'rgba(76, 175, 80, 1)' }}>
                    {tableData.reduce((sum, item) => sum + item.cumulativeRecovery, 0).toFixed(2)}{' '}
                    万元
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    总期末余额
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'rgba(244, 67, 54, 1)' }}>
                    {tableData.reduce((sum, item) => sum + item.periodEndAmount, 0).toFixed(2)} 万元
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    平均完成比例
                  </Typography>
                  <Typography variant="h6" sx={{ color: 'rgba(255, 152, 0, 1)' }}>
                    {(
                      tableData.reduce((sum, item) => sum + item.completionRate, 0) /
                      tableData.length
                    ).toFixed(1)}
                    %
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, gap: 1 }}>
          <Button
            onClick={() => setShowDetailModal(false)}
            variant="outlined"
            sx={{
              color: '#666666',
              borderColor: '#cccccc',
              '&:hover': {
                borderColor: '#999999',
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
            }}
          >
            关闭
          </Button>
          <Button
            onClick={() => {
              // TODO: 实现下载逻辑
              console.log('下载功能待实现');
            }}
            variant="contained"
            startIcon={<DownloadIcon />}
            sx={{
              backgroundColor: '#1976d2',
              color: '#ffffff',
              '&:hover': {
                backgroundColor: '#1565c0',
              },
            }}
          >
            下载
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

CompanyProgressChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      companyName: PropTypes.string.isRequired,
      yearEndAmount: PropTypes.number, // 期初金额
      cumulativeRecovery: PropTypes.number, // 累计清收金额
      completionRate: PropTypes.number, // 完成率
      collectionTargetAmount: PropTypes.number, // 清收目标金额
      hasTarget: PropTypes.bool, // 是否有清收目标
    }),
  ),
  title: PropTypes.string,
};

CompanyProgressChart.defaultProps = {
  data: [],
  title: '各子公司存量债权回收完成情况',
};

export default CompanyProgressChart;
