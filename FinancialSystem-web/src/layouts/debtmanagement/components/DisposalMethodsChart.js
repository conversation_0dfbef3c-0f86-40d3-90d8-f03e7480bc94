import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Chart } from 'react-chartjs-2';
import { Button, Box } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import DisposalMethodsDetailModal from './DisposalMethodsDetailModal';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 存量债权清收处置方式统计图表组件
 * @param {Object} props
 * @param {Array} props.data - 处置方式数据数组，每项包含 method, amount, percentage
 * @param {String} props.title - 图表标题
 * @param {Function} props.onDetailClick - 详细信息按钮点击回调
 * @returns {JSX.Element}
 */
const DisposalMethodsChart = ({ data, title = '存量债权清收方式', onDetailClick }) => {
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // 过滤掉金额为0的数据项
  const filteredData = (data || []).filter(item => item.amount > 0);

  // 如果没有数据，显示暂无数据提示
  if (!filteredData || filteredData.length === 0) {
    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
          <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
          <Box sx={{ fontSize: '14px' }}>暂无数据</Box>
        </Box>
      </Box>
    );
  }

  // 提取标签和数据（数据已经是万元单位，直接使用）
  const labels = filteredData.map(item => item.method);
  const amounts = filteredData.map(item => item.amount);

  // 绿色系配色（4种不同深浅）
  const greenColors = [
    'rgba(46, 125, 50, 0.8)', // 深绿色
    'rgba(76, 175, 80, 0.8)', // 标准绿色
    'rgba(129, 199, 132, 0.8)', // 浅绿色
    'rgba(165, 214, 167, 0.8)', // 更浅绿色
  ];

  const greenBorderColors = [
    'rgba(46, 125, 50, 1)',
    'rgba(76, 175, 80, 1)',
    'rgba(129, 199, 132, 1)',
    'rgba(165, 214, 167, 1)',
  ];

  // 图表数据配置 - 只显示柱状图，去掉线条
  const chartData = {
    labels,
    datasets: [
      {
        type: 'bar',
        label: '金额',
        data: amounts,
        backgroundColor: filteredData.map((_, index) => greenColors[index % greenColors.length]),
        borderColor: filteredData.map(
          (_, index) => greenBorderColors[index % greenBorderColors.length],
        ),
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false,
        maxBarThickness: 48,
      },
    ],
  };

  // 图表选项配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14,
          weight: 'medium',
        },
        bodyFont: {
          size: 12,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || '';
            if (context.datasetIndex === 0) {
              // 柱状图 - 显示金额
              return `${label}: ${context.parsed.y.toFixed(2)} 万元`;
            } else {
              // 折线图 - 显示百分比
              return `${label}: ${context.parsed.y.toFixed(2)}%`;
            }
          },
        },
      },
      datalabels: {
        display: true,
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          size: 11,
          weight: 'bold',
        },
        formatter(value) {
          return value.toFixed(2);
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 12,
          },
          maxRotation: labels.length > 4 ? 45 : 0,
          minRotation: labels.length > 4 ? 45 : 0,
        },
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: false,
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return value.toFixed(0);
          },
          // 让Chart.js自动计算合适的刻度间隔
          maxTicksLimit: 8,
          // 确保刻度值是整数
          stepSize: undefined, // 让Chart.js自动计算
        },
      },
    },
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    setDetailModalOpen(true);
    if (onDetailClick) {
      onDetailClick(filteredData);
    }
  };

  return (
    <Box
      className="p-6 bg-white rounded-lg shadow-md"
      sx={{
        position: 'relative',
        width: '100%',
        maxWidth: '100%',
        marginBottom: '20px',
        overflow: 'hidden',
      }}
    >
      {/* 更多信息按钮 */}
      <Button
        variant="text"
        size="small"
        startIcon={<InfoOutlinedIcon />}
        onClick={handleMoreInfo}
        sx={{
          position: 'absolute',
          top: 4,
          right: 4,
          zIndex: 1,
          color: 'primary.main',
          fontSize: '12px',
          minWidth: 'auto',
          padding: '4px 8px',
          '&:hover': {
            backgroundColor: 'rgba(76, 175, 80, 0.08)',
          },
        }}
      >
        详细信息
      </Button>

      {/* 图表容器 */}
      <Box
        sx={{
          height: '350px',
          width: '100%',
          pt: 2, // 为按钮留出空间
        }}
      >
        <Chart type="bar" data={chartData} options={options} />
      </Box>

      {/* 数据汇总信息 */}
      <Box
        sx={{
          mt: 2,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 2,
        }}
      >
        {/* 各处置方式详情 - 同一行显示，过滤掉资产抵债和其他方式 */}
        <Box
          sx={{
            display: 'flex',
            gap: 4,
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            mt: 2,
            flexWrap: 'nowrap', // 强制同一行显示
            minHeight: '40px', // 确保有足够的高度
          }}
        >
          {filteredData
            .filter(item => !['资产抵债', '其他方式'].includes(item.method))
            .map(item => (
              <Box
                key={item.method}
                sx={{
                  fontSize: '12px',
                  color: 'text.primary',
                  fontWeight: 'medium',
                  whiteSpace: 'nowrap',
                  flex: '0 0 auto', // 不允许收缩或扩展
                  textAlign: 'center',
                }}
              >
                {item.method}
              </Box>
            ))}
        </Box>
      </Box>

      {/* 详细信息弹窗 */}
      <DisposalMethodsDetailModal
        open={detailModalOpen}
        onClose={() => setDetailModalOpen(false)}
        data={filteredData}
      />
    </Box>
  );
};

DisposalMethodsChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      method: PropTypes.string.isRequired,
      amount: PropTypes.number.isRequired,
      percentage: PropTypes.number.isRequired,
    }),
  ),
  title: PropTypes.string,
  onDetailClick: PropTypes.func,
};

DisposalMethodsChart.defaultProps = {
  data: [],
  title: '存量债权清收方式',
};

export default DisposalMethodsChart;
