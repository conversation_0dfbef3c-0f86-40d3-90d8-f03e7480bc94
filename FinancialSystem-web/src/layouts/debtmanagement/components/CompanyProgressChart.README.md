# CompanyProgressChart 组件文档

## 概述

`CompanyProgressChart` 是一个用于显示各子公司存量债权回收完成情况的混合图表组件。该组件结合了柱状图和折线图，能够直观地展示公司的债权回收进度。

## 功能特性

- **混合图表**：同时显示柱状图（金额数据）和折线图（完成进度）
- **双 Y 轴**：左侧显示金额（万元），右侧显示完成进度（%）
- **响应式设计**：根据公司数量自动调整布局
- **横向滚动**：当公司数量超过 8 个时自动启用横向滚动
- **数据汇总**：底部显示总公司数、平均完成进度、最高/最低完成进度
- **交互功能**：鼠标悬停显示详细数值，支持"更多信息"按钮

## 使用方法

### 基本使用

```javascript
import CompanyProgressChart from './components/CompanyProgressChart';

const data = [
  {
    companyName: '中筑天佑',
    yearEndAmount: 11224, // 2024年末金额
    cumulativeRecovery: 10324, // 累计回收金额
    periodEndAmount: 900, // 期末债权金额
    completionRate: 103, // 完成进度百分比
    targetAmount: 10000 // 目标金额（可选）
  }
  // 更多公司数据...
];

<CompanyProgressChart data={data} />;
```

### 自定义标题

```javascript
<CompanyProgressChart data={data} title="2024年度债权回收进度报告" />
```

## 数据格式

### Props

| 属性  | 类型   | 必需 | 默认值                         | 描述             |
| ----- | ------ | ---- | ------------------------------ | ---------------- |
| data  | Array  | 是   | []                             | 公司进度数据数组 |
| title | String | 否   | "各子公司存量债权回收完成情况" | 图表标题         |

### 数据对象格式

```javascript
{
  companyName: String,       // 公司名称（必需）
  yearEndAmount: Number,     // 2024年末金额（万元）
  cumulativeRecovery: Number,// 累计回收金额（万元）
  periodEndAmount: Number,   // 期末债权金额（万元）
  completionRate: Number,    // 完成进度（%）
  targetAmount: Number       // 目标金额（万元，可选）
}
```

## 图表说明

### 柱状图（3 个数据系列）

- **蓝色柱**：2024 年末金额
- **绿色柱**：累计回收金额
- **红色柱**：期末债权金额

### 折线图

- **橙色线**：完成进度百分比（带圆点标记）

### 数据展示

- 金额显示：保留 2 位小数
- 百分比显示：保留 1 位小数
- 数据标签：柱状图顶部显示整数值

## 响应式行为

- **公司数量 ≤ 8**：图表宽度自适应容器
- **公司数量 > 8**：
  - 启用横向滚动
  - 图表宽度 = 公司数量 × 100px
  - X 轴标签旋转 45 度显示

## 特殊处理

### 公司名称过长

当公司名称超过 8 个字符时，自动截断并添加省略号：

```
"这是一个很长的公司名称" → "这是一个很长的..."
```

### 无数据显示

当没有数据或数据为空时，显示友好的"暂无数据"提示。

### 数据验证

- 自动过滤无效数据（没有公司名称的数据）
- 处理 null/undefined 值，使用默认值 0

## 样式定制

组件使用 Material-UI 的主题系统，可以通过以下方式定制：

```javascript
// 按钮悬停颜色
sx={{
  '&:hover': {
    backgroundColor: 'rgba(76, 175, 80, 0.08)',
  },
}}

// 图表容器样式
className="p-6 bg-white rounded-lg shadow-md"
```

## 性能优化

- 使用 React.memo 避免不必要的重渲染
- 图表数据计算只在数据变化时进行
- 大数据量时使用横向滚动而非缩小图表

## 注意事项

1. **数据排序**：建议在后端按完成进度降序排列数据
2. **数据精度**：金额数据建议保留 2 位小数
3. **更多信息按钮**：需要实现具体的点击处理逻辑
4. **依赖项**：
   - react-chartjs-2
   - chart.js
   - chartjs-plugin-datalabels
   - @mui/material

## 示例代码

查看 `CompanyProgressChartExample.js` 获取完整的使用示例。
