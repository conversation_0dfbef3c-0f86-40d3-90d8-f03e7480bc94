import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  Chip,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
} from '@mui/material';
import { formatCurrency } from '../../../utils/formatters';
import debtManagementAPI from '../../../services/debtManagementAPI';

/**
 * 处置方式详细信息弹窗组件
 * 参考存量债权清收情况的显示方式
 * @param {Object} props
 * @param {boolean} props.open - 弹窗是否打开
 * @param {Function} props.onClose - 关闭弹窗回调
 * @param {string} props.year - 年份
 * @param {string} props.month - 月份
 * @param {string} props.company - 公司
 * @param {Array} props.summaryData - 汇总数据
 * @returns {JSX.Element}
 */
const DisposalDetailsDialog = ({ open, onClose, year, month, company, summaryData }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [detailData, setDetailData] = useState([]);

  // 获取详细数据
  const fetchDetailData = async () => {
    if (!open || !year || !month) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await debtManagementAPI.getDebtDisposalDetails(
        year,
        month,
        company || '全部',
      );
      setDetailData(response || []);
    } catch (err) {
      console.error('获取处置详细数据失败:', err);
      setError('获取详细数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetailData();
  }, [open, year, month, company]);

  // 渲染汇总卡片
  const renderSummaryCards = () => {
    if (!summaryData || summaryData.length === 0) {
      return null;
    }

    return (
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {summaryData.map((item, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div" gutterBottom>
                  {item.method}
                </Typography>
                <Typography variant="h4" color="primary">
                  {formatCurrency(item.amount)}万
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  占比: {item.percentage}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // 渲染详细表格（参考存量债权清收情况的表格样式）
  const renderDetailTable = () => {
    if (!detailData || detailData.length === 0) {
      return (
        <Typography variant="body2" color="text.secondary" sx={{ p: 2 }}>
          暂无处置方式相关记录
        </Typography>
      );
    }

    return (
      <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              <TableCell>管理公司</TableCell>
              <TableCell>债权人</TableCell>
              <TableCell>债务人</TableCell>
              <TableCell>是否涉诉</TableCell>
              <TableCell>科目名称</TableCell>
              <TableCell>期间</TableCell>
              <TableCell align="right">现金处置(万)</TableCell>
              <TableCell align="right">资产抵债(万)</TableCell>
              <TableCell align="right">分期还款(万)</TableCell>
              <TableCell align="right">其他方式(万)</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                合计(万)
              </TableCell>
              <TableCell>备注</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {detailData.map((row, index) => (
              <TableRow key={index} hover>
                <TableCell>{row.managementCompany}</TableCell>
                <TableCell>{row.creditor}</TableCell>
                <TableCell>{row.debtor}</TableCell>
                <TableCell>
                  <Chip
                    label={row.isLitigation === '是' ? '涉诉' : '非涉诉'}
                    color={row.isLitigation === '是' ? 'error' : 'success'}
                    size="small"
                  />
                </TableCell>
                <TableCell>{row.subjectName}</TableCell>
                <TableCell>{row.period}</TableCell>
                <TableCell align="right">{formatCurrency(row.cashDisposal / 10000)}</TableCell>
                <TableCell align="right">{formatCurrency(row.assetDebt / 10000)}</TableCell>
                <TableCell align="right">
                  {formatCurrency(row.installmentRepayment / 10000)}
                </TableCell>
                <TableCell align="right">{formatCurrency(row.otherWays / 10000)}</TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                  {formatCurrency(row.total / 10000)}
                </TableCell>
                <TableCell>{row.remark || '-'}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' },
      }}
    >
      <DialogTitle>
        <Typography variant="h6">
          存量债权处置方式明细 - {year}年{month}月{company && company !== '全部' && ` - ${company}`}
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <>
            {/* 汇总卡片 */}
            {renderSummaryCards()}

            {/* 详细表格 - 移除标签页，直接显示表格 */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                处置方式明细列表 ({detailData.length}条记录)
              </Typography>
              {renderDetailTable()}
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

DisposalDetailsDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  year: PropTypes.string,
  month: PropTypes.string,
  company: PropTypes.string,
  summaryData: PropTypes.array,
};

DisposalDetailsDialog.defaultProps = {
  year: '',
  month: '',
  company: '全部',
  summaryData: [],
};

export default DisposalDetailsDialog;
