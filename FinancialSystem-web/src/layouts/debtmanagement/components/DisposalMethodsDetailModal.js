import React from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

/**
 * 存量债权清收方式明细弹窗组件
 * @param {Object} props
 * @param {Boolean} props.open - 是否打开弹窗
 * @param {Function} props.onClose - 关闭弹窗回调
 * @param {Array} props.data - 处置方式数据数组
 * @returns {JSX.Element}
 */
const DisposalMethodsDetailModal = ({ open, onClose, data }) => {
  // 如果没有数据，显示空状态
  if (!data || data.length === 0) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            minHeight: '400px',
          },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '1px solid #e0e0e0',
            pb: 2,
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
            存量债权清收方式明细
          </Typography>
          <IconButton
            onClick={onClose}
            sx={{
              color: 'grey.500',
              '&:hover': {
                backgroundColor: 'grey.100',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ py: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '200px',
              color: 'text.secondary',
            }}
          >
            <Typography variant="body1">暂无明细数据</Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={onClose} variant="outlined">
            关闭
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '500px',
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #e0e0e0',
          pb: 2,
        }}
      >
        <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
          存量债权清收方式明细
        </Typography>
        <IconButton
          onClick={onClose}
          sx={{
            color: 'grey.500',
            '&:hover': {
              backgroundColor: 'grey.100',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ py: 3 }}>
        {/* 汇总信息 */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
            处置方式汇总
          </Typography>
          <Box
            sx={{
              display: 'flex',
              gap: 3,
              flexWrap: 'wrap',
              p: 2,
              backgroundColor: 'grey.50',
              borderRadius: 1,
            }}
          >
            {data.map(item => (
              <Box key={item.method} sx={{ textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {item.method}
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  {item.amount?.toFixed(2) || '0.00'} 万元
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  占比: {item.percentage?.toFixed(2) || '0.00'}%
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>

        {/* 明细表格 */}
        <Box>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
            明细信息
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow sx={{ backgroundColor: 'grey.100' }}>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    处置方式
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    金额 (万元)
                  </TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    占比 (%)
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.map(item => (
                  <TableRow key={item.method} hover>
                    <TableCell align="center">{item.method}</TableCell>
                    <TableCell align="center">{item.amount?.toFixed(2) || '0.00'}</TableCell>
                    <TableCell align="center">{item.percentage?.toFixed(2) || '0.00'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} variant="outlined">
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

DisposalMethodsDetailModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      method: PropTypes.string.isRequired,
      amount: PropTypes.number.isRequired,
      percentage: PropTypes.number.isRequired,
    }),
  ),
};

DisposalMethodsDetailModal.defaultProps = {
  data: [],
};

export default DisposalMethodsDetailModal;
