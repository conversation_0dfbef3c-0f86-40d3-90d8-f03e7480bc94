import api from '../../../utils/api';

/**
 * 资产管理API服务
 * 提供资产管理相关的API调用功能
 */

/**
 * 获取资产列表（分页）
 * @param {Object} params - 查询参数
 * @param {string} params.managementCompany - 管理公司
 * @param {string} params.assetType - 资产类型 (PROPERTY/LAND)
 * @param {number} params.propertyOwnerId - 产权人ID
 * @param {boolean} params.hasPropertyCertificate - 是否有产权证
 * @param {number} params.page - 页码（从0开始）
 * @param {number} params.size - 每页大小
 * @returns {Promise} API响应
 */
export const getAssets = async (params = {}) => {
  try {
    const response = await api.get('/assets', { params });
    return response.data;
  } catch (error) {
    console.error('获取资产列表失败:', error);
    throw error;
  }
};

/**
 * 根据ID获取资产详情
 * @param {number} id - 资产ID
 * @returns {Promise} API响应
 */
export const getAssetById = async id => {
  try {
    const response = await api.get(`/assets/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取资产详情失败:', error);
    throw error;
  }
};

/**
 * 创建新资产
 * @param {Object} assetData - 资产数据
 * @returns {Promise} API响应
 */
export const createAsset = async assetData => {
  try {
    const response = await api.post('/assets', assetData);
    return response.data;
  } catch (error) {
    console.error('创建资产失败:', error);
    throw error;
  }
};

/**
 * 更新资产信息
 * @param {number} id - 资产ID
 * @param {Object} assetData - 更新的资产数据
 * @returns {Promise} API响应
 */
export const updateAsset = async (id, assetData) => {
  try {
    const response = await api.put(`/assets/${id}`, assetData);
    return response.data;
  } catch (error) {
    console.error('更新资产失败:', error);
    throw error;
  }
};

/**
 * 删除资产（软删除）
 * @param {number} id - 资产ID
 * @returns {Promise} API响应
 */
export const deleteAsset = async id => {
  try {
    const response = await api.delete(`/assets/${id}`);
    return response.data;
  } catch (error) {
    console.error('删除资产失败:', error);
    throw error;
  }
};

/**
 * 获取瑕疵资产列表
 * @param {string} managementCompany - 管理公司
 * @returns {Promise} API响应
 */
export const getDefectiveAssets = async managementCompany => {
  try {
    const params = managementCompany ? { managementCompany } : {};
    const response = await api.get('/assets/defective', { params });
    return response.data;
  } catch (error) {
    console.error('获取瑕疵资产失败:', error);
    throw error;
  }
};

/**
 * 获取资产统计信息
 * @param {string} managementCompany - 管理公司
 * @returns {Promise} API响应
 */
export const getAssetStatistics = async managementCompany => {
  try {
    const params = managementCompany ? { managementCompany } : {};
    const response = await api.get('/assets/statistics', { params });
    return response.data;
  } catch (error) {
    console.error('获取资产统计失败:', error);
    throw error;
  }
};

/**
 * 获取瑕疵资产统计
 * @returns {Promise} API响应
 */
export const getDefectiveAssetStatistics = async () => {
  try {
    const response = await api.get('/assets/statistics/defective');
    return response.data;
  } catch (error) {
    console.error('获取瑕疵资产统计失败:', error);
    // 返回模拟数据作为后备
    return [
      {
        managementCompany: '深圳万润科技股份有限公司',
        assetType: '房产',
        count: 1,
        totalArea: 2500,
      },
      {
        managementCompany: '深圳万润投资有限公司',
        assetType: '土地',
        count: 2,
        totalArea: 3200,
      },
    ];
  }
};

/**
 * 获取管理公司列表
 * @returns {Promise} API响应
 */
export const getManagementCompanies = async () => {
  try {
    const response = await api.get('/assets/management-companies');
    return response.data;
  } catch (error) {
    console.error('获取管理公司列表失败:', error);
    throw error;
  }
};

/**
 * 测试资产管理API连接
 * @returns {Promise} API响应
 */
export const testAssetAPI = async () => {
  try {
    const response = await api.get('/assets/test');
    return response.data;
  } catch (error) {
    console.error('测试资产API失败:', error);
    throw error;
  }
};

/**
 * 获取面积分布统计（用于圆饼图）
 * @param {string} managementCompany - 管理公司
 * @returns {Promise} API响应
 */
export const getAreaDistribution = async managementCompany => {
  try {
    const params = managementCompany ? { managementCompany } : {};
    const response = await api.get('/assets/statistics/area-distribution', { params });
    return response.data;
  } catch (error) {
    console.error('获取面积分布统计失败:', error);
    // 返回模拟数据作为后备
    return {
      totalArea: 35000,
      selfUseArea: 20000,
      rentalArea: 15000,
      idleArea: 0,
    };
  }
};

/**
 * 获取公司盘活统计（用于柱形图）
 * @returns {Promise} API响应
 */
export const getCompanyActivationStatistics = async () => {
  try {
    const response = await api.get('/assets/statistics/company-activation');
    return response.data;
  } catch (error) {
    console.error('获取公司盘活统计失败:', error);
    // 返回模拟数据作为后备
    return [
      {
        companyName: '深圳万润科技股份有限公司',
        totalArea: 35000,
        selfUseArea: 20000,
        rentalArea: 15000,
        activationRate: 100,
      },
      {
        companyName: '深圳万润投资有限公司',
        totalArea: 28000,
        selfUseArea: 15000,
        rentalArea: 10000,
        activationRate: 89.3,
      },
      {
        companyName: '万润控股集团',
        totalArea: 42000,
        selfUseArea: 25000,
        rentalArea: 12000,
        activationRate: 88.1,
      },
    ];
  }
};

/**
 * 资产类型枚举
 */
export const ASSET_TYPES = {
  PROPERTY: 'PROPERTY',
  LAND: 'LAND',
};

/**
 * 资产状态枚举
 */
export const ASSET_STATUS = {
  ACTIVE: 'ACTIVE',
  DISPOSED: 'DISPOSED',
  MAINTENANCE: 'MAINTENANCE',
};

/**
 * 资产类型描述映射
 */
export const ASSET_TYPE_DESCRIPTIONS = {
  PROPERTY: '房产',
  LAND: '土地',
};

/**
 * 资产状态描述映射
 */
export const ASSET_STATUS_DESCRIPTIONS = {
  ACTIVE: '正常',
  DISPOSED: '已处置',
  MAINTENANCE: '维护中',
};
