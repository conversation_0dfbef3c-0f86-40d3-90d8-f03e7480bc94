import React from 'react';
import { Snackbar, Alert } from '@mui/material';
import { AUTO_HIDE_DURATIONS } from 'constants/notificationConstants';

/**
 * NotificationSnackbar - 升级版通知组件
 *
 * 新特性：
 * 1. 居中显示
 * 2. 行业标准自动消失时长
 * 3. 向后兼容现有API
 */
const NotificationSnackbar = ({
  open,
  message,
  severity = 'success',
  onClose,
  autoHideDuration,
  centered = true, // 默认居中显示
  title,
}) => {
  // 根据severity自动设置行业标准时长
  const getAutoHideDuration = () => {
    if (autoHideDuration !== undefined) {
      return autoHideDuration;
    }

    switch (severity) {
    case 'success':
      return AUTO_HIDE_DURATIONS.success;
    case 'error':
      return AUTO_HIDE_DURATIONS.error;
    case 'warning':
      return AUTO_HIDE_DURATIONS.warning;
    case 'info':
      return AUTO_HIDE_DURATIONS.info;
    default:
      return AUTO_HIDE_DURATIONS.info;
    }
  };

  return (
    <Snackbar
      open={open}
      autoHideDuration={getAutoHideDuration()}
      onClose={onClose}
      anchorOrigin={
        centered
          ? {
            vertical: 'top',
            horizontal: 'center',
          }
          : {
            vertical: 'top',
            horizontal: 'right',
          }
      }
      sx={{
        ...(centered && {
          '& .MuiSnackbar-anchorOriginTopCenter': {
            top: '20%',
            left: '50%',
            transform: 'translateX(-50%)',
          },
        }),
      }}
    >
      <Alert
        onClose={onClose}
        severity={severity}
        sx={{
          width: '100%',
          minWidth: centered ? '300px' : 'auto',
          maxWidth: '500px',
        }}
      >
        {title && <div style={{ fontWeight: 600, marginBottom: '4px' }}>{title}</div>}
        {message}
      </Alert>
    </Snackbar>
  );
};

export default NotificationSnackbar;
