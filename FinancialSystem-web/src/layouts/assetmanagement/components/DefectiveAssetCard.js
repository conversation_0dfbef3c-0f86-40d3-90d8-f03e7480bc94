import React from 'react';
import { Card, CardContent, Typography, Box, Grid, Chip, LinearProgress } from '@mui/material';
import {
  Warning as WarningIcon,
  Home as HomeIcon,
  Landscape as LandscapeIcon,
} from '@mui/icons-material';

const DefectiveAssetCard = ({ data, title = '瑕疵资产统计', loading = false }) => {
  // 如果没有数据，显示空状态
  if (!data || data.length === 0) {
    return (
      <Card
        sx={{
          p: 2,
          border: loading ? '2px solid #ff9800' : '1px solid #e0e0e0',
          boxShadow: loading ? '0 4px 20px rgba(255, 152, 0, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease',
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <WarningIcon color="warning" sx={{ mr: 1 }} />
            <Typography variant="h6">
              {title}
              {loading && (
                <Typography variant="caption" color="warning.main" sx={{ ml: 1, display: 'block' }}>
                  (数据更新中...)
                </Typography>
              )}
            </Typography>
          </Box>
          <Typography color="text.secondary">暂无瑕疵资产数据</Typography>
        </CardContent>
      </Card>
    );
  }

  // 计算统计数据
  const totalDefectiveAssets = data.reduce((sum, item) => sum + (item.count || 0), 0);
  const totalDefectiveArea = data.reduce((sum, item) => sum + (item.totalArea || 0), 0);

  // 按资产类型分组
  const propertyData = data.filter(item => item.assetType === '房产');
  const landData = data.filter(item => item.assetType === '土地');

  const propertyCount = propertyData.reduce((sum, item) => sum + (item.count || 0), 0);
  const landCount = landData.reduce((sum, item) => sum + (item.count || 0), 0);

  const propertyArea = propertyData.reduce((sum, item) => sum + (item.totalArea || 0), 0);
  const landArea = landData.reduce((sum, item) => sum + (item.totalArea || 0), 0);

  // 按管理公司分组
  const companyStats = data.reduce((acc, item) => {
    const company = item.managementCompany || '未知公司';
    if (!acc[company]) {
      acc[company] = { count: 0, area: 0 };
    }
    acc[company].count += item.count || 0;
    acc[company].area += item.totalArea || 0;
    return acc;
  }, {});

  const getTypeIcon = type => {
    return type === '房产' ? <HomeIcon /> : <LandscapeIcon />;
  };

  return (
    <Card
      sx={{
        height: '100%',
        border: loading ? '2px solid #ff9800' : '1px solid #e0e0e0',
        boxShadow: loading ? '0 4px 20px rgba(255, 152, 0, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)',
        transition: 'all 0.3s ease',
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <WarningIcon color="warning" sx={{ mr: 1 }} />
          <Typography variant="h6">
            {title}
            {loading && (
              <Typography variant="caption" color="warning.main" sx={{ ml: 1, display: 'block' }}>
                (数据更新中...)
              </Typography>
            )}
          </Typography>
        </Box>

        {/* 总体统计 */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {totalDefectiveAssets}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                瑕疵资产数量
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {totalDefectiveArea.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                瑕疵资产面积(㎡)
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* 按类型统计 */}
        <Typography variant="subtitle1" gutterBottom>
          按资产类型分布
        </Typography>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <HomeIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
              <Typography variant="body2">房产</Typography>
            </Box>
            <Typography variant="h6" color="primary">
              {propertyCount} 项
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {propertyArea.toLocaleString()}㎡
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <LandscapeIcon color="success" sx={{ mr: 1, fontSize: 20 }} />
              <Typography variant="body2">土地</Typography>
            </Box>
            <Typography variant="h6" color="success.main">
              {landCount} 项
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {landArea.toLocaleString()}㎡
            </Typography>
          </Grid>
        </Grid>

        {/* 按公司统计 */}
        <Typography variant="subtitle1" gutterBottom>
          按管理公司分布
        </Typography>
        <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
          {Object.entries(companyStats).map(([company, stats]) => (
            <Box key={company} sx={{ mb: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 1,
                }}
              >
                <Typography variant="body2" noWrap sx={{ maxWidth: '60%' }}>
                  {company}
                </Typography>
                <Chip label={`${stats.count}项`} size="small" color="warning" variant="outlined" />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LinearProgress
                  variant="determinate"
                  value={totalDefectiveAssets > 0 ? (stats.count / totalDefectiveAssets) * 100 : 0}
                  sx={{ flexGrow: 1, mr: 1 }}
                  color="warning"
                />
                <Typography variant="caption" color="text.secondary">
                  {stats.area.toLocaleString()}㎡
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>

        {/* 详细列表 */}
        {data.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              详细信息
            </Typography>
            {data.map((item, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  py: 1,
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {getTypeIcon(item.assetType)}
                  <Typography variant="body2" sx={{ ml: 1 }}>
                    {item.managementCompany}
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" color="warning.main">
                    {item.count}项
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {(item.totalArea || 0).toLocaleString()}㎡
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default DefectiveAssetCard;
