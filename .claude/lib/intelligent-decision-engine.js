/**
 * 智能决策引擎 - AI驱动的Git操作决策系统
 * 基于上下文分析、风险评估和机器学习的智能决策
 */

const { exec } = require('child_process').promises;
const path = require('path');
const fs = require('fs').promises;

class IntelligentDecisionEngine {
  constructor() {
    this.contextAnalyzer = new ContextAnalyzer();
    this.riskAssessor = new RiskAssessor();
    this.strategySelector = new StrategySelector();
    this.learningEngine = new LearningEngine();
  }

  /**
   * 主决策入口
   */
  async makeDecision(context) {
    console.log('🧠 智能决策引擎启动...');
    
    // 1. 深度上下文分析
    const analysis = await this.contextAnalyzer.analyze(context);
    
    // 2. 风险评估
    const riskProfile = await this.riskAssessor.assess(analysis);
    
    // 3. 策略选择
    const strategy = await this.strategySelector.select(analysis, riskProfile);
    
    // 4. 从历史学习
    await this.learningEngine.learn(analysis, strategy);
    
    // 5. 生成决策
    return this.generateDecision(analysis, riskProfile, strategy);
  }

  generateDecision(analysis, riskProfile, strategy) {
    return {
      action: strategy.action,
      confidence: strategy.confidence,
      reasoning: {
        context: analysis.summary,
        risk: riskProfile.summary,
        strategy: strategy.reasoning
      },
      parameters: {
        qualityThreshold: this.calculateQualityThreshold(riskProfile),
        autoFixEnabled: riskProfile.level < 0.7,
        parallelAgents: this.determineAgentCount(analysis),
        timeoutMs: this.calculateTimeout(analysis.complexity)
      },
      recommendations: this.generateRecommendations(analysis, riskProfile)
    };
  }

  calculateQualityThreshold(riskProfile) {
    // 根据风险等级动态调整质量阈值
    const baseThreshold = 0.8;
    const riskAdjustment = riskProfile.level * 0.15;
    return Math.min(0.95, baseThreshold + riskAdjustment);
  }

  determineAgentCount(analysis) {
    // 根据复杂度决定并行Agent数量
    const complexity = analysis.complexity;
    if (complexity > 0.8) return 8;
    if (complexity > 0.6) return 6;
    if (complexity > 0.4) return 4;
    return 2;
  }

  calculateTimeout(complexity) {
    // 根据复杂度计算超时时间
    const baseTimeout = 60000; // 1分钟
    return baseTimeout * (1 + complexity * 2);
  }

  generateRecommendations(analysis, riskProfile) {
    const recommendations = [];
    
    if (riskProfile.level > 0.8) {
      recommendations.push({
        type: 'critical',
        message: '建议人工审核此次变更',
        reason: '风险等级过高'
      });
    }
    
    if (analysis.hasBreakingChanges) {
      recommendations.push({
        type: 'warning',
        message: '检测到破坏性变更，建议更新文档',
        reason: '接口或配置发生重大变化'
      });
    }
    
    if (analysis.testCoverage < 0.7) {
      recommendations.push({
        type: 'improvement',
        message: '建议增加测试覆盖率',
        reason: '当前测试覆盖率低于标准'
      });
    }
    
    return recommendations;
  }
}

/**
 * 上下文分析器
 */
class ContextAnalyzer {
  async analyze(context) {
    const { files, branch, history } = context;
    
    // 分析文件变更
    const changeAnalysis = await this.analyzeChanges(files);
    
    // 分析业务影响
    const businessImpact = await this.analyzeBusinessImpact(changeAnalysis);
    
    // 分析历史模式
    const historicalPatterns = await this.analyzeHistory(history);
    
    // 计算复杂度
    const complexity = this.calculateComplexity(changeAnalysis, businessImpact);
    
    return {
      changeAnalysis,
      businessImpact,
      historicalPatterns,
      complexity,
      hasBreakingChanges: this.detectBreakingChanges(changeAnalysis),
      testCoverage: await this.estimateTestCoverage(files),
      summary: this.generateSummary(changeAnalysis, businessImpact)
    };
  }

  async analyzeChanges(files) {
    const analysis = {
      totalFiles: files.length,
      fileTypes: {},
      changeTypes: {},
      hotspots: []
    };

    for (const file of files) {
      // 分析文件类型
      const ext = path.extname(file.path);
      analysis.fileTypes[ext] = (analysis.fileTypes[ext] || 0) + 1;
      
      // 分析变更类型
      analysis.changeTypes[file.changeType] = (analysis.changeTypes[file.changeType] || 0) + 1;
      
      // 识别热点文件
      if (file.changes > 100 || file.complexity > 0.8) {
        analysis.hotspots.push(file);
      }
    }
    
    return analysis;
  }

  async analyzeBusinessImpact(changeAnalysis) {
    // 基于文件路径和类型评估业务影响
    let impact = 0;
    
    // 核心模块影响更大
    if (changeAnalysis.hotspots.some(f => f.path.includes('/core/'))) {
      impact += 0.3;
    }
    
    // API变更影响大
    if (changeAnalysis.fileTypes['.controller.js'] > 0) {
      impact += 0.2;
    }
    
    // 数据库变更影响大
    if (changeAnalysis.fileTypes['.sql'] > 0 || changeAnalysis.fileTypes['.migration.js'] > 0) {
      impact += 0.3;
    }
    
    return {
      level: Math.min(1, impact),
      affectedModules: this.identifyAffectedModules(changeAnalysis),
      userImpact: impact > 0.5 ? 'high' : impact > 0.3 ? 'medium' : 'low'
    };
  }

  async analyzeHistory(history) {
    // 分析历史提交模式
    const patterns = {
      averageCommitSize: 0,
      commonIssues: [],
      successRate: 0,
      averageFixTime: 0
    };

    if (history && history.length > 0) {
      patterns.averageCommitSize = history.reduce((sum, h) => sum + h.fileCount, 0) / history.length;
      patterns.successRate = history.filter(h => h.success).length / history.length;
      
      // 识别常见问题
      const issues = history.flatMap(h => h.issues || []);
      patterns.commonIssues = this.findCommonPatterns(issues);
    }
    
    return patterns;
  }

  calculateComplexity(changeAnalysis, businessImpact) {
    let complexity = 0;
    
    // 文件数量影响
    complexity += Math.min(0.3, changeAnalysis.totalFiles / 100);
    
    // 热点文件影响
    complexity += Math.min(0.3, changeAnalysis.hotspots.length / 10);
    
    // 业务影响
    complexity += businessImpact.level * 0.4;
    
    return Math.min(1, complexity);
  }

  detectBreakingChanges(changeAnalysis) {
    // 检测可能的破坏性变更
    return changeAnalysis.changeTypes.deleted > 0 ||
           changeAnalysis.fileTypes['.api.js'] > 0 ||
           changeAnalysis.fileTypes['.config.js'] > 0;
  }

  async estimateTestCoverage(files) {
    // 估算测试覆盖率（简化版）
    const testFiles = files.filter(f => f.path.includes('.test.') || f.path.includes('.spec.'));
    const sourceFiles = files.filter(f => !f.path.includes('.test.') && !f.path.includes('.spec.'));
    
    if (sourceFiles.length === 0) return 1;
    return Math.min(1, testFiles.length / sourceFiles.length);
  }

  identifyAffectedModules(changeAnalysis) {
    // 识别受影响的模块
    const modules = new Set();
    
    Object.keys(changeAnalysis.fileTypes).forEach(ext => {
      if (ext.includes('controller')) modules.add('api');
      if (ext.includes('service')) modules.add('business');
      if (ext.includes('repository')) modules.add('data');
      if (ext.includes('component')) modules.add('ui');
    });
    
    return Array.from(modules);
  }

  findCommonPatterns(issues) {
    // 查找常见问题模式
    const patterns = {};
    issues.forEach(issue => {
      patterns[issue.type] = (patterns[issue.type] || 0) + 1;
    });
    
    return Object.entries(patterns)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([type, count]) => ({ type, count }));
  }

  generateSummary(changeAnalysis, businessImpact) {
    return `变更了${changeAnalysis.totalFiles}个文件，` +
           `业务影响${businessImpact.userImpact}，` +
           `主要影响${businessImpact.affectedModules.join('、')}模块`;
  }
}

/**
 * 风险评估器
 */
class RiskAssessor {
  async assess(analysis) {
    const risks = {
      code: await this.assessCodeRisk(analysis),
      business: await this.assessBusinessRisk(analysis),
      history: await this.assessHistoricalRisk(analysis),
      timing: await this.assessTimingRisk()
    };

    const overallRisk = this.calculateOverallRisk(risks);
    
    return {
      level: overallRisk,
      factors: risks,
      summary: this.generateRiskSummary(risks, overallRisk),
      mitigations: this.suggestMitigations(risks)
    };
  }

  async assessCodeRisk(analysis) {
    let risk = 0;
    
    // 复杂度风险
    risk += analysis.complexity * 0.3;
    
    // 测试覆盖率风险
    risk += (1 - analysis.testCoverage) * 0.3;
    
    // 破坏性变更风险
    if (analysis.hasBreakingChanges) {
      risk += 0.4;
    }
    
    return Math.min(1, risk);
  }

  async assessBusinessRisk(analysis) {
    return analysis.businessImpact.level;
  }

  async assessHistoricalRisk(analysis) {
    if (!analysis.historicalPatterns) return 0.5;
    
    // 基于历史成功率评估风险
    const successRate = analysis.historicalPatterns.successRate;
    return 1 - successRate;
  }

  async assessTimingRisk() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    
    // 非工作时间风险更高
    if (hour < 9 || hour > 18) return 0.7;
    
    // 周末风险更高
    if (day === 0 || day === 6) return 0.8;
    
    // 周五下午风险高
    if (day === 5 && hour > 15) return 0.6;
    
    return 0.3;
  }

  calculateOverallRisk(risks) {
    // 加权计算总体风险
    const weights = {
      code: 0.35,
      business: 0.35,
      history: 0.2,
      timing: 0.1
    };
    
    return Object.entries(risks).reduce((total, [type, risk]) => {
      return total + (risk * weights[type]);
    }, 0);
  }

  generateRiskSummary(risks, overallRisk) {
    const level = overallRisk > 0.7 ? '高' : overallRisk > 0.4 ? '中' : '低';
    const topRisk = Object.entries(risks)
      .sort((a, b) => b[1] - a[1])[0];
    
    return `风险等级：${level}，主要风险因素：${topRisk[0]}`;
  }

  suggestMitigations(risks) {
    const mitigations = [];
    
    if (risks.code > 0.7) {
      mitigations.push('增加代码审查');
      mitigations.push('运行更多测试');
    }
    
    if (risks.business > 0.7) {
      mitigations.push('通知相关团队');
      mitigations.push('准备回滚方案');
    }
    
    if (risks.timing > 0.7) {
      mitigations.push('延迟到工作时间提交');
      mitigations.push('确保有人值守');
    }
    
    return mitigations;
  }
}

/**
 * 策略选择器
 */
class StrategySelector {
  async select(analysis, riskProfile) {
    // 基于分析和风险选择最佳策略
    const strategies = await this.generateStrategies(analysis, riskProfile);
    const scored = await this.scoreStrategies(strategies, analysis, riskProfile);
    const best = scored[0];
    
    return {
      action: best.action,
      confidence: best.score,
      reasoning: best.reasoning,
      alternatives: scored.slice(1, 3),
      parameters: best.parameters
    };
  }

  async generateStrategies(analysis, riskProfile) {
    const strategies = [];
    
    // 标准提交策略
    strategies.push({
      action: 'standard_commit',
      reasoning: '标准流程，适合大多数情况',
      parameters: {
        preChecks: ['lint', 'test', 'build'],
        autoFix: true,
        parallelChecks: true
      }
    });
    
    // 快速提交策略（低风险）
    if (riskProfile.level < 0.3) {
      strategies.push({
        action: 'fast_commit',
        reasoning: '低风险变更，可以快速提交',
        parameters: {
          preChecks: ['lint'],
          autoFix: true,
          parallelChecks: false
        }
      });
    }
    
    // 谨慎提交策略（高风险）
    if (riskProfile.level > 0.7) {
      strategies.push({
        action: 'careful_commit',
        reasoning: '高风险变更，需要更多检查',
        parameters: {
          preChecks: ['lint', 'test', 'build', 'security', 'performance'],
          autoFix: false,
          parallelChecks: true,
          requireReview: true
        }
      });
    }
    
    // 分阶段提交策略（大型变更）
    if (analysis.changeAnalysis.totalFiles > 50) {
      strategies.push({
        action: 'staged_commit',
        reasoning: '大型变更，建议分阶段提交',
        parameters: {
          stages: this.planStages(analysis),
          preChecks: ['lint', 'test'],
          autoFix: true
        }
      });
    }
    
    return strategies;
  }

  async scoreStrategies(strategies, analysis, riskProfile) {
    // 为每个策略评分
    return strategies.map(strategy => {
      let score = 0.5; // 基础分
      
      // 根据风险调整
      if (strategy.action === 'fast_commit' && riskProfile.level < 0.3) {
        score += 0.3;
      } else if (strategy.action === 'careful_commit' && riskProfile.level > 0.7) {
        score += 0.3;
      }
      
      // 根据复杂度调整
      if (strategy.action === 'staged_commit' && analysis.complexity > 0.7) {
        score += 0.2;
      }
      
      // 根据时间因素调整
      if (strategy.action === 'fast_commit' && riskProfile.factors.timing > 0.7) {
        score -= 0.2; // 非工作时间不建议快速提交
      }
      
      return { ...strategy, score: Math.max(0, Math.min(1, score)) };
    }).sort((a, b) => b.score - a.score);
  }

  planStages(analysis) {
    // 规划分阶段提交
    const stages = [];
    
    // 按模块分组
    const modules = analysis.businessImpact.affectedModules;
    modules.forEach(module => {
      stages.push({
        name: `${module}模块变更`,
        files: `**/${module}/**`,
        order: this.getModulePriority(module)
      });
    });
    
    return stages.sort((a, b) => a.order - b.order);
  }

  getModulePriority(module) {
    // 定义模块优先级
    const priorities = {
      'data': 1,      // 数据层优先
      'business': 2,  // 然后业务层
      'api': 3,       // 然后API层
      'ui': 4         // 最后UI层
    };
    return priorities[module] || 5;
  }
}

/**
 * 学习引擎
 */
class LearningEngine {
  constructor() {
    this.historyFile = '.claude/data/decision-history.json';
  }

  async learn(analysis, strategy) {
    // 记录决策历史
    const record = {
      timestamp: new Date().toISOString(),
      analysis: this.summarizeAnalysis(analysis),
      strategy: strategy.action,
      confidence: strategy.confidence
    };
    
    await this.saveRecord(record);
    
    // 更新模型（简化版）
    await this.updateModel(record);
  }

  summarizeAnalysis(analysis) {
    return {
      fileCount: analysis.changeAnalysis.totalFiles,
      complexity: analysis.complexity,
      businessImpact: analysis.businessImpact.level,
      testCoverage: analysis.testCoverage
    };
  }

  async saveRecord(record) {
    try {
      const history = await this.loadHistory();
      history.push(record);
      
      // 保留最近1000条记录
      if (history.length > 1000) {
        history = history.slice(-1000);
      }
      
      await fs.writeFile(this.historyFile, JSON.stringify(history, null, 2));
    } catch (error) {
      console.error('保存决策历史失败:', error);
    }
  }

  async loadHistory() {
    try {
      const data = await fs.readFile(this.historyFile, 'utf8');
      return JSON.parse(data);
    } catch {
      return [];
    }
  }

  async updateModel(record) {
    // 简化的模型更新逻辑
    // 实际应用中可以使用更复杂的ML算法
    console.log('📊 更新学习模型...');
  }
}

module.exports = IntelligentDecisionEngine;