# AI自动化触发器配置

## 🚀 快照自动化触发

当用户发送以下任何格式的消息时，自动执行相应操作：

### 📸 快照创建触发器

**触发关键词**：
- `快照` / `snapshot`
- `检查点` / `checkpoint` 
- `保存进度` / `save progress`
- `创建快照：[描述]`
- `checkpoint: [描述]`

**自动执行操作**：
1. 检测当前是否在AI任务分支
2. 运行 `./scripts/ai-checkpoint.sh create "[描述]"`
3. 可选运行快速验证测试
4. 返回检查点创建结果

### 🔙 回滚触发器

**触发关键词**：
- `回滚` / `rollback`
- `撤销` / `undo` 
- `回到上一步` / `go back`
- `回滚：[步数]`
- `rollback [步数]`

**自动执行操作**：
1. 运行 `./scripts/ai-checkpoint.sh rollback [步数]`
2. 显示回滚后的状态
3. 提供进一步操作建议

### 📋 状态查看触发器

**触发关键词**：
- `检查点列表` / `list checkpoints`
- `任务状态` / `task status`
- `当前进度` / `current progress`

**自动执行操作**：
1. 运行 `./scripts/ai-checkpoint.sh list`
2. 显示当前任务信息
3. 显示可用的回滚点

### 🧪 测试验证触发器

**触发关键词**：
- `测试` / `test`
- `验证` / `verify`
- `检查` / `check`

**自动执行操作**：
1. 运行 `./scripts/test-startup.sh`
2. 显示测试结果
3. 如果测试失败，提供问题分析

## 🎯 智能识别规则

### 快照描述提取
- `快照：修复用户登录问题` → 描述："修复用户登录问题"
- `checkpoint: fix user authentication` → 描述："fix user authentication"
- `保存进度，完成了数据库连接` → 描述："完成了数据库连接"

### 回滚步数识别
- `回滚` → 默认回滚1步
- `回滚2步` / `rollback 2` → 回滚2步
- `回到3步前` → 回滚3步

### 上下文感知
- 如果不在AI任务分支，提示先启动AI任务
- 如果没有检测到代码更改，提示确认是否需要创建快照
- 如果测试失败，自动建议创建故障检查点

## 🔧 实现方式

Claude会检测用户消息中的这些关键词，并自动：
1. **识别意图** - 判断用户想要执行什么操作
2. **提取参数** - 提取描述、步数等参数
3. **执行脚本** - 调用相应的自动化脚本
4. **返回结果** - 显示操作结果和建议

## 📝 使用示例

**用户发送**：`快照：完成了债权导出功能修复`

**Claude自动执行**：
```bash
./scripts/ai-checkpoint.sh create "完成了债权导出功能修复"
```

**用户发送**：`回滚2步`

**Claude自动执行**：
```bash
./scripts/ai-checkpoint.sh rollback 2
```

**用户发送**：`测试`

**Claude自动执行**：
```bash
./scripts/test-startup.sh
```