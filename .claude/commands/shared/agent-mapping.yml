# Agent Mapping Configuration - 命令与子代理自动映射系统
# 定义每个命令应该使用的默认子代理类型

## 子代理类型说明
Agent_Types:
  general-purpose: "通用智能体 - 处理复杂的多步骤任务、代码搜索和执行"
  spec-gen-agent: "规范生成智能体 - 将业务需求转换为结构化需求文档"
  feature-dev-agent: "功能开发智能体 - 在不破坏现有功能的情况下扩展系统"
  # FinancialSystem 专业智能体
  requirements-analysis-agent: "需求分析专家 - 分析业务需求、生成用户故事和验收标准"
  technical-design-agent: "技术设计专家 - 将需求转化为详细技术方案和架构设计"
  implementation-planning-agent: "实施规划专家 - 任务分解、工时评估和依赖管理"
  # wshobson/agents 集成
  code-reviewer: "代码审查专家 - 质量、安全和可维护性审查"
  security-auditor: "安全审计专家 - 漏洞扫描和OWASP合规检查"
  performance-engineer: "性能工程师 - 应用优化和瓶颈分析"
  backend-architect: "后端架构师 - API设计、微服务和数据库架构"
  frontend-developer: "前端开发者 - React组件和响应式布局"
  devops-troubleshooter: "DevOps故障排查 - 生产问题调试和日志分析"

## 核心命令映射
Command_Agent_Mapping:
  # 开发命令
  analyze: 
    agent: "general-purpose"
    description: "代码分析、架构审查、性能分析"
  build:
    agent: "general-purpose" 
    description: "构建功能、创建组件、实现代码"
  dev-setup:
    agent: "general-purpose"
    description: "开发环境配置"
  test:
    agent: "general-purpose"
    description: "执行测试、验证功能"
    
  # 分析改进命令  
  review:
    agent: "general-purpose"
    description: "代码审查、质量检查"
  improve:
    agent: "general-purpose"
    description: "代码改进、性能优化"
  troubleshoot:
    agent: "general-purpose"
    description: "问题诊断、错误修复"
  explain:
    agent: "general-purpose"
    description: "代码解释、文档说明"
    
  # 运维命令
  deploy:
    agent: "general-purpose"
    description: "部署流程、环境管理"
  migrate:
    agent: "general-purpose"
    description: "数据迁移、版本升级"
  scan:
    agent: "general-purpose"
    description: "安全扫描、代码检查"
  cleanup:
    agent: "general-purpose"
    description: "清理临时文件、优化存储"
  git:
    agent: "general-purpose"
    description: "版本控制操作"
  estimate:
    agent: "spec-gen-agent"
    description: "工作量估算、资源规划"
    
  # 工作流命令
  design:
    agent: "technical-design-agent"
    description: "系统设计、架构规划、技术方案"
    fallback: "spec-gen-agent"
  document:
    agent: "spec-gen-agent"
    description: "文档编写、API文档、用户手册"
  load:
    agent: "general-purpose"
    description: "加载项目、上下文管理"
  spawn:
    agent: "general-purpose"
    description: "并行任务、子智能体管理"
  task:
    agent: "general-purpose"
    description: "任务管理、工作流程"

## 智能选择规则
Smart_Selection_Rules:
  # 基于关键词的智能选择
  keyword_triggers:
    requirements_keywords: ["需求分析", "用户故事", "验收标准", "业务需求", "功能需求"]
    design_keywords: ["技术设计", "架构设计", "系统架构", "API设计", "数据库设计"]
    planning_keywords: ["任务分解", "工时评估", "实施计划", "项目规划", "依赖管理"]
    spec_keywords: ["需求", "规范", "文档", "设计", "架构", "规划", "需要实现", "功能描述"]
    general_keywords: ["搜索", "查找", "修复", "执行", "运行", "调试", "实现"]
    
  # 基于任务复杂度的选择
  complexity_rules:
    simple_task: "general-purpose"  # 单一明确的任务
    complex_specification: "spec-gen-agent"  # 需要详细规范的任务
    multi_step_execution: "general-purpose"  # 多步骤执行任务
    
  # 基于输出类型的选择  
  output_type:
    code_generation: "general-purpose"
    document_generation: "spec-gen-agent"
    analysis_report: "general-purpose"
    requirement_spec: "spec-gen-agent"

## 覆盖机制
Override_Options:
  # 用户可以通过标志覆盖默认选择
  user_flags:
    --agent: "显式指定智能体类型"
    --no-agent: "禁用自动智能体调用"
    
  # 特殊情况处理
  special_cases:
    ultrathink_mode: "当使用 --ultrathink 时，优先使用 general-purpose"
    planning_mode: "当使用 --plan 时，可能需要 spec-gen-agent 生成计划"

## 集成配置
Integration_Config:
  # 与其他系统的集成
  persona_integration:
    architect: "优先使用 technical-design-agent 进行架构设计"
    analyst: "优先使用 requirements-analysis-agent 进行需求分析"
    planner: "优先使用 implementation-planning-agent 进行项目规划"
    frontend: "使用 general-purpose 进行组件开发"
    backend: "使用 general-purpose 进行API开发"
    
  # 执行优先级
  execution_priority:
    1: "检查用户显式标志"
    2: "应用命令默认映射"
    3: "根据内容智能选择"
    4: "使用 general-purpose 作为后备"

## 使用示例
Usage_Examples:
  # 自动使用 general-purpose
  - "/analyze --code --performance"
  - "/build --feature user-auth"
  - "/test --coverage"
  
  # 自动使用 spec-gen-agent
  - "/document --user-guide"
  - "/estimate --complexity large"
  
  # 自动使用新的专业智能体
  - "/design --api payment-system"  # 使用 technical-design-agent
  - "/analyze --requirements 债权管理功能"  # 可使用 requirements-analysis-agent
  - "/task --plan 实施债权导出功能"  # 可使用 implementation-planning-agent
  
  # 用户覆盖默认选择
  - "/analyze --agent requirements-analysis-agent"  # 强制使用需求分析智能体
  - "/design --agent general-purpose"   # 强制使用通用智能体
  - "/task --agent implementation-planning-agent"  # 强制使用实施规划智能体

---
*Agent Mapping v1.1 - 自动化命令与子代理映射系统 (已集成FinancialSystem专业智能体)*