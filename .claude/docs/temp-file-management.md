# 🗂️ AI临时文件管理规则

## 📋 概述

本文档定义了AI助手（Claude）在FinancialSystem项目中创建和管理临时文件的规则。

## 🎯 核心原则

1. **永不在根目录创建临时文件**
2. **所有临时文件集中管理**
3. **自动清理机制**
4. **明确的命名规范**

## 📁 临时文件目录结构

```
FinancialSystem/
├── .temp/                    # 所有AI临时文件的根目录
│   ├── analysis/            # 分析类临时文件
│   ├── build/              # 构建相关临时文件
│   ├── test/               # 测试相关临时文件
│   ├── export/             # 导出类临时文件
│   └── workspace/          # 通用工作空间
└── .gitignore              # 确保.temp被忽略
```

## 📝 命名规范

### 临时文件命名格式
```
{prefix}_{purpose}_{timestamp}.{ext}
```

示例：
- `analysis_debt_summary_20250803_141523.txt`
- `test_api_response_20250803_141523.json`
- `export_report_draft_20250803_141523.xlsx`

### 前缀定义
- `analysis_` - 分析类文件
- `test_` - 测试类文件
- `build_` - 构建类文件
- `export_` - 导出类文件
- `temp_` - 通用临时文件

## 🚫 禁止行为

1. **禁止在以下位置创建临时文件**：
   - 项目根目录
   - src目录
   - 任何源代码目录
   - 配置文件目录

2. **禁止创建的文件类型**：
   - 无扩展名的文件
   - 系统隐藏文件（除.gitignore等必要文件）
   - 二进制可执行文件（除非明确需要）

## ✅ 推荐做法

### 1. 创建临时文件前
```bash
# 确保临时目录存在
mkdir -p .temp/analysis
mkdir -p .temp/test
mkdir -p .temp/export
```

### 2. 创建临时文件时
```bash
# 使用明确的路径和命名
echo "分析结果" > .temp/analysis/debt_analysis_$(date +%Y%m%d_%H%M%S).txt
```

### 3. 使用完毕后
```bash
# 立即清理不再需要的文件
rm -f .temp/analysis/debt_analysis_*.txt
```

## 🔄 自动清理规则

### 清理时机
1. **即时清理**：任务完成后立即删除
2. **日常清理**：超过24小时的文件
3. **周期清理**：每周清理一次所有临时文件

### 清理脚本
```bash
#!/bin/bash
# scripts/clean-temp-files.sh

# 清理超过24小时的临时文件
find .temp -type f -mtime +1 -delete

# 清理空目录
find .temp -type d -empty -delete

# 保留目录结构
mkdir -p .temp/{analysis,build,test,export,workspace}
```

## 📋 AI助手检查清单

在创建任何文件前，AI应该：

- [ ] 确认是否真的需要创建文件
- [ ] 检查是否可以使用内存处理
- [ ] 确定正确的临时目录
- [ ] 使用规范的命名格式
- [ ] 计划清理策略

## 🛡️ Git配置

确保.gitignore包含：
```gitignore
# AI临时文件
.temp/
*.tmp
*.temp
*_temp.*
temp_*
```

## 📊 监控和报告

### 定期检查命令
```bash
# 查看临时文件状态
ls -la .temp/

# 统计临时文件数量
find .temp -type f | wc -l

# 查看占用空间
du -sh .temp/
```

### 异常处理
如果发现根目录有临时文件：
1. 立即移动到.temp目录
2. 更新创建逻辑
3. 记录在改进日志中

## 🚨 紧急情况

如果临时文件失控：
```bash
# 紧急清理脚本
./scripts/emergency-clean.sh

# 内容：
# 1. 备份重要的临时文件（如果有）
# 2. 清理所有临时文件
# 3. 重建临时目录结构
# 4. 生成清理报告
```

## 📝 示例场景

### 场景1：分析代码结构
```bash
# 错误 ❌
echo "分析结果" > analysis_result.txt

# 正确 ✅
echo "分析结果" > .temp/analysis/code_structure_$(date +%Y%m%d_%H%M%S).txt
```

### 场景2：生成测试报告
```bash
# 错误 ❌
mvn test > test_report.txt

# 正确 ✅
mvn test > .temp/test/maven_test_$(date +%Y%m%d_%H%M%S).txt
```

### 场景3：导出数据
```bash
# 错误 ❌
cp data.xlsx export_data.xlsx

# 正确 ✅
cp data.xlsx .temp/export/data_export_$(date +%Y%m%d_%H%M%S).xlsx
```

## 🔧 集成到CLAUDE.md

这些规则将被集成到CLAUDE.md中，确保AI助手始终遵守这些管理规则。

---

**记住**：良好的临时文件管理是专业开发的体现。保持项目目录的整洁，就像保持代码的整洁一样重要。