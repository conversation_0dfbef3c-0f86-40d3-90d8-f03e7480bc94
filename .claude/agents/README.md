# 🤖 FinancialSystem Agent Collection

专为FinancialSystem财务管理系统设计的智能Agent集合，涵盖需求分析、技术设计、功能开发、测试部署等完整开发流程。

## 🎯 设计理念

- **工作流导向**：按照软件开发生命周期组织Agent
- **安全第一**：内置多重保护机制，防止破坏性操作
- **协作高效**：Agent之间可以无缝协作和信息传递
- **中英双语**：支持中英文混合使用，适应不同场景

## 📦 Agent分类与调用方式

### 🔄 自动调用Agent（系统级保护）
这些Agent会在特定条件下自动触发，无需手动调用：

| Agent | 用途 | 自动触发时机 | 模型 |
|-------|------|--------------|------|
| 🛡️ **guardian-agent** | 系统守护者 | 检测到危险操作时（删除、大规模修改） | sonnet |
| ✅ **validator-agent** | 代码验证专家 | 代码文件被修改后立即验证 | sonnet |
| 📸 **snapshot-agent** | 快照管理专家 | 重要操作前后自动保存项目状态 | sonnet |
| 💡 **helper-agent** | 智能助手 | 检测到用户困惑、错误重复出现时 | sonnet |
| ✔️ **todo-agent** | 任务管理专家 | TODO.md文件更新、任务相关命令时 | sonnet |

### 👥 手动调用Agent（工作流专家）
需要明确调用的专业领域Agent：

#### 📋 需求与规划类（3个）
| Agent | 用途 | 使用场景 | 模型 |
|-------|------|----------|------|
| 📊 **requirements-analyst** | 需求分析专家 | 新功能需求、用户需求不清晰时 | opus |
| 🏗️ **technical-design-agent** | 技术设计专家 | 需要架构设计、API设计时 | opus |
| 📅 **implementation-planner** | 实施规划专家 | 需要任务分解、工时评估时 | opus |

#### 🔧 开发与维护类（4个）
| Agent | 用途 | 使用场景 | 模型 |
|-------|------|----------|------|
| ⚡ **feature-dev-agent** | 功能开发专家 | 开发新功能、扩展现有功能 | opus |
| 🔧 **fix-agent** | 问题修复专家 | 修复具体bug、解决问题 | sonnet |
| 🔍 **search-agent** | 智能搜索专家 | 查找代码、定位功能、模糊搜索 | sonnet |
| 📖 **explain-agent** | 代码解释专家 | 理解复杂代码、学习新技术 | opus |

#### ✅ 质量保证类（3个）
| Agent | 用途 | 使用场景 | 模型 |
|-------|------|----------|------|
| 🧪 **test-agent** | 测试验证专家 | 运行测试、验证功能正确性 | sonnet |
| 👀 **review-agent** | 代码审查专家 | 代码质量检查、最佳实践审查 | opus |
| 📝 **document-agent** | 文档编写专家 | 生成API文档、添加代码注释 | sonnet |

#### 🚀 运维部署类（2个）
| Agent | 用途 | 使用场景 | 模型 |
|-------|------|----------|------|
| 🚀 **deploy-agent** | 部署专家 | 安全部署、环境配置 | opus |
| 📋 **plan-agent** | 实施规划专家 | 制定开发计划、风险评估 | opus |

#### 🎓 学习辅助类（2个）
| Agent | 用途 | 使用场景 | 模型 |
|-------|------|----------|------|
| 🎓 **tutor-agent** | 编程导师 | 教学模式、边做边教 | opus |
| 🏃 **practice-agent** | 练习生成专家 | 提供编程练习、技能巩固 | sonnet |

#### 🔨 工具辅助类（1个）
| Agent | 用途 | 使用场景 | 模型 |
|-------|------|----------|------|
| 🔨 **batch-agent-creator** | 批量Agent创建工具 | 快速创建多个相似Agent | sonnet |

## 🚀 完整开发工作流

### 阶段1：需求分析与设计
```bash
1. 用户提出需求 → requirements-analyst（需求分析）
2. 生成需求文档 → technical-design-agent（技术设计）  
3. 设计完成 → implementation-planner（任务规划）
```

### 阶段2：开发实施
```bash
4. 开始开发 → feature-dev-agent（功能开发）
5. 遇到问题 → fix-agent（问题修复）
6. 需要搜索 → search-agent（代码搜索）
7. 理解代码 → explain-agent（代码解释）
```

### 阶段3：质量保证
```bash
8. 开发完成 → test-agent（功能测试）
9. 代码审查 → review-agent（质量检查）
10. 添加文档 → document-agent（文档生成）
```

### 阶段4：部署上线
```bash
11. 准备部署 → deploy-agent（安全部署）
12. 制定计划 → plan-agent（规划协调）
```

### 全程保护：自动运行Agent
```bash
# 在整个流程中自动运行
- guardian-agent：防止危险操作
- validator-agent：实时代码验证  
- snapshot-agent：关键点快照
- helper-agent：困惑时自动帮助
- todo-agent：任务管理联动
```

## 💡 调用示例

### 手动调用示例
```bash
# 需求分析
"我需要添加一个批量导入债权数据的功能"

# 功能开发
"帮我实现用户认证模块，要求安全且不影响现有功能"

# 代码搜索
"搜索所有处理Excel导出的代码"

# 问题修复
"修复登录按钮点击无响应的问题"

# 代码解释
"解释一下这个债权管理模块是怎么工作的"

# 代码测试
"测试用户登录功能是否正常"

# 代码审查
"审查我刚完成的代码"

# 生成文档
"为这个API接口生成使用文档"

# 安全部署
"部署到生产环境"
```

### 自动触发示例
```bash
# 当你执行危险操作时
rm -rf src/  # guardian-agent会自动阻止并创建备份

# 当你修改代码后
# validator-agent会自动检查语法和类型错误

# 当你感到困惑时
"不知道怎么办..." # helper-agent会主动提供相关帮助

# 当你更新TODO时
# todo-agent会自动分析和管理任务状态
```

## 🛡️ 安全保护机制

### 1. 多层防护体系
- **guardian-agent**：阻止危险的文件操作
- **validator-agent**：实时验证代码语法
- **snapshot-agent**：关键操作点自动备份
- **feature-dev-agent**：保护核心代码区域

### 2. 受保护的代码区域
- Controller层：现有API端点不可修改
- Service层：公共方法签名不可变更
- Repository层：现有查询方法保持不变
- Entity层：现有字段和关系不可删除
- Database层：现有表结构不可破坏

### 3. 安全开发原则
- **扩展优于修改**：添加新功能而不改变现有代码
- **备份优先**：重要操作前自动创建备份
- **渐进式开发**：小步快跑，频繁验证
- **权限控制**：敏感操作需要明确授权

## 🔧 Agent协作模式

### 流水线协作
```
需求分析 → 技术设计 → 实施规划 → 功能开发 → 质量保证 → 部署上线
```

### 并行协作
```bash
# 多个Agent可以同时工作
- search-agent：搜索相关代码
- explain-agent：解释复杂逻辑
- feature-dev-agent：实现新功能
```

### 保护性协作
```bash
# 保护Agent与开发Agent配合
- guardian-agent监控feature-dev-agent的操作
- validator-agent验证fix-agent的修复结果
- snapshot-agent为deploy-agent提供回滚能力
```

## 📚 学习路径建议

### 初学者路线
1. **理解系统**：用explain-agent学习现有代码
2. **安全实践**：在保护下尝试小改动
3. **测试验证**：用test-agent验证结果
4. **渐进提升**：逐步承担更复杂的任务

### 进阶开发者路线
1. **需求分析**：熟练使用requirements-analyst
2. **设计思维**：掌握technical-design-agent
3. **规划能力**：使用implementation-planner
4. **协作开发**：多Agent配合完成复杂项目

## 🎯 最佳实践

### 1. 工作流最佳实践
- 🔄 **先研究再动手**：用search-agent和explain-agent理解现有代码
- 📋 **制定详细计划**：用implementation-planner分解任务
- 🛡️ **保护式开发**：信任自动保护Agent的判断
- ✅ **频繁验证**：每个阶段都用相应的验证Agent

### 2. Agent使用最佳实践
- 🎯 **明确目标**：清楚描述你要做什么
- 🔍 **上下文充分**：提供足够的背景信息
- 🤝 **信任协作**：让Agent之间互相配合
- 📝 **记录过程**：重要决策要有文档记录

### 3. 安全开发最佳实践
- 🚫 **永不忽略警告**：guardian-agent的警告必须重视
- 💾 **经常备份**：善用snapshot-agent的快照功能
- 🧪 **测试优先**：新功能必须通过test-agent验证
- 👀 **代码审查**：重要代码必须经过review-agent检查

## 🤝 常见问题

### Q: 如何知道应该调用哪个Agent？
A: 按照开发阶段选择：需求分析→技术设计→实施规划→功能开发→质量保证→部署上线

### Q: 自动Agent没有触发怎么办？
A: 检查文件是否在正确位置，某些Agent有特定的触发条件

### Q: Agent之间会产生冲突吗？
A: 不会，Agent被设计为协作模式，保护Agent优先级最高

### Q: 可以跳过某些阶段吗？
A: 建议遵循完整流程，但简单修改可以直接使用fix-agent

### Q: 如何自定义Agent行为？
A: 大部分Agent支持通过对话进行配置，无需修改Agent文件

## 📞 获取帮助

- 🆘 **即时帮助**：使用helper-agent获取上下文相关的帮助
- 📖 **详细说明**：查看各Agent的.md文件了解详细功能
- 🎓 **学习模式**：使用tutor-agent获得教学式指导
- 🏃 **练习巩固**：使用practice-agent进行技能练习

---

💪 **记住**：这些Agent是为了让你成为更好的开发者，而不是替代你的思考。目标是提高效率、降低风险、保证质量。

🎯 **FinancialSystem专用**：所有Agent都针对FinancialSystem的技术栈和业务领域进行了优化，能更好地理解项目需求。

祝开发顺利！🚀