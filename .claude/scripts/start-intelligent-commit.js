#!/usr/bin/env node

/**
 * 智能提交系统启动脚本
 * 启动文件监控和自动提交服务
 */

const { IntelligentDecisionEngine } = require('../lib/intelligent-decision-engine');
const { MultiAgentOrchestrator } = require('../lib/multi-agent-orchestrator');
const chokidar = require('chokidar');
const path = require('path');
const fs = require('fs');

class IntelligentCommitSystem {
  constructor() {
    this.decisionEngine = new IntelligentDecisionEngine();
    this.orchestrator = new MultiAgentOrchestrator();
    this.changeBuffer = new Map();
    this.debounceTimer = null;
    this.config = this.loadConfig();
    this.stats = {
      startTime: Date.now(),
      totalCommits: 0,
      successfulCommits: 0,
      failedCommits: 0,
      totalTimesSaved: 0
    };
  }

  loadConfig() {
    try {
      const configPath = path.join(__dirname, '../config/intelligent-commit-config.json');
      return JSON.parse(fs.readFileSync(configPath, 'utf8'));
    } catch (error) {
      console.warn('⚠️ 使用默认配置');
      return {
        automation: {
          commitInterval: 300000, // 5分钟
          humanInterventionThreshold: 0.3
        },
        monitoring: {
          realtime: true,
          dashboardPort: 3000
        }
      };
    }
  }

  start() {
    console.log('🚀 智能提交系统启动中...');
    console.log('📊 配置信息:');
    console.log(`  - 自动提交间隔: ${this.config.automation.commitInterval / 1000}秒`);
    console.log(`  - 人工介入阈值: ${this.config.automation.humanInterventionThreshold}`);
    console.log(`  - 监控面板端口: ${this.config.monitoring.dashboardPort}`);
    console.log('');
    
    // 启动文件监控
    this.startFileWatcher();
    
    // 启动监控服务
    if (this.config.monitoring.realtime) {
      this.startMonitoringDashboard();
    }
    
    // 设置定期状态报告
    this.startPeriodicReporting();
    
    // 优雅退出处理
    this.setupGracefulShutdown();
    
    console.log('✅ 系统启动完成！正在监控文件变化...\n');
  }

  startFileWatcher() {
    const watchPath = process.cwd();
    
    this.watcher = chokidar.watch(watchPath, {
      ignored: [
        /(^|[\/\\])\../, // 隐藏文件
        /node_modules/,
        /\.git/,
        /dist/,
        /build/,
        /coverage/,
        /\.log$/
      ],
      persistent: true,
      ignoreInitial: true,
      awaitWriteFinish: {
        stabilityThreshold: 2000,
        pollInterval: 100
      }
    });

    this.watcher
      .on('add', path => this.handleFileChange('add', path))
      .on('change', path => this.handleFileChange('change', path))
      .on('unlink', path => this.handleFileChange('unlink', path))
      .on('error', error => console.error('❌ 监控错误:', error));
  }

  handleFileChange(event, filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    // 记录变更
    this.changeBuffer.set(filePath, {
      event,
      path: filePath,
      relativePath,
      timestamp: Date.now()
    });
    
    console.log(`📝 检测到变更: ${event} ${relativePath}`);
    
    // 重置定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    // 设置新定时器
    const waitTime = Math.min(
      this.config.automation.commitInterval,
      30000 // 最多等待30秒
    );
    
    this.debounceTimer = setTimeout(() => {
      this.processBufferedChanges();
    }, waitTime);
  }

  async processBufferedChanges() {
    if (this.changeBuffer.size === 0) return;
    
    console.log('\n' + '='.repeat(60));
    console.log(`🤖 开始处理 ${this.changeBuffer.size} 个文件变更`);
    console.log('='.repeat(60));
    
    const changes = Array.from(this.changeBuffer.values());
    this.changeBuffer.clear();
    
    const startTime = Date.now();
    
    try {
      // 1. 智能决策
      console.log('\n📊 Phase 1: 智能决策分析...');
      const decision = await this.decisionEngine.makeDecision({
        changes,
        branch: await this.getCurrentBranch(),
        author: await this.getCurrentAuthor()
      });
      
      console.log(`  ✅ 决策完成: ${decision.strategy}`);
      console.log(`  📈 风险等级: ${decision.execution.priority}`);
      console.log(`  ⏱️ 预计时间: ${decision.execution.estimatedTime}ms`);
      
      // 2. 多Agent执行
      console.log('\n🎯 Phase 2: 多Agent协同执行...');
      const result = await this.orchestrator.executeWorkflow({
        changes,
        decision,
        autoMode: true
      });
      
      // 3. 处理结果
      const duration = Date.now() - startTime;
      
      if (result.action === 'proceed') {
        await this.performCommit(decision, result);
        this.stats.successfulCommits++;
        this.stats.totalTimesSaved += duration;
        
        console.log('\n✅ 自动提交成功！');
        console.log(`  📊 质量评分: ${(result.confidence * 100).toFixed(1)}%`);
        console.log(`  ⏱️ 总耗时: ${duration}ms`);
        console.log(`  💾 已保存时间: ${(this.stats.totalTimesSaved / 1000).toFixed(1)}秒`);
        
      } else if (result.action === 'review_warnings') {
        console.log('\n⚠️ 需要审查警告:');
        result.warnings.forEach((w, i) => {
          console.log(`  ${i + 1}. ${w}`);
        });
        
      } else if (result.action === 'improve_quality') {
        console.log('\n📈 需要改进质量:');
        result.areas.forEach((area, i) => {
          console.log(`  ${i + 1}. ${area}`);
        });
        
      } else {
        console.log('\n❓ 需要人工介入');
        console.log(`  原因: ${result.reason}`);
        this.notifyHumanIntervention(result);
      }
      
      this.stats.totalCommits++;
      
    } catch (error) {
      console.error('\n❌ 处理失败:', error.message);
      this.stats.failedCommits++;
      
      // 保存失败的变更以便后续处理
      for (const change of changes) {
        this.changeBuffer.set(change.path, change);
      }
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
  }

  async performCommit(decision, result) {
    // 构建提交命令
    const commitMessage = decision.commitStrategy.message;
    
    // 执行git操作
    const { execSync } = require('child_process');
    
    try {
      // 添加文件
      execSync('git add .');
      
      // 提交
      const commitCmd = `git commit -m "${commitMessage.replace(/"/g, '\\"')}"`;
      execSync(commitCmd);
      
      // 可选：推送到远程
      if (this.config.automation.autoPush) {
        execSync('git push');
      }
      
      return true;
      
    } catch (error) {
      console.error('Git操作失败:', error.message);
      throw error;
    }
  }

  async getCurrentBranch() {
    try {
      const { execSync } = require('child_process');
      return execSync('git branch --show-current').toString().trim();
    } catch {
      return 'unknown';
    }
  }

  async getCurrentAuthor() {
    try {
      const { execSync } = require('child_process');
      const name = execSync('git config user.name').toString().trim();
      const email = execSync('git config user.email').toString().trim();
      return { name, email };
    } catch {
      return { name: 'Unknown', email: '<EMAIL>' };
    }
  }

  startMonitoringDashboard() {
    const express = require('express');
    const app = express();
    const http = require('http').createServer(app);
    const io = require('socket.io')(http);
    
    // 静态文件服务
    app.use(express.static(path.join(__dirname, '../dashboard')));
    
    // API端点
    app.get('/api/stats', (req, res) => {
      res.json({
        ...this.stats,
        uptime: Date.now() - this.stats.startTime,
        successRate: this.stats.totalCommits > 0 
          ? (this.stats.successfulCommits / this.stats.totalCommits * 100).toFixed(1) 
          : 0
      });
    });
    
    // WebSocket连接
    io.on('connection', (socket) => {
      console.log('📊 监控面板已连接');
      
      // 发送初始状态
      socket.emit('stats', this.stats);
      
      // 定期更新
      const interval = setInterval(() => {
        socket.emit('stats', this.stats);
      }, 1000);
      
      socket.on('disconnect', () => {
        clearInterval(interval);
      });
    });
    
    // 监听决策引擎事件
    this.decisionEngine.on('decision:complete', (plan) => {
      io.emit('decision', plan);
    });
    
    this.orchestrator.on('workflow:complete', (result) => {
      io.emit('workflow', result);
    });
    
    const port = this.config.monitoring.dashboardPort;
    http.listen(port, () => {
      console.log(`📊 监控面板已启动: http://localhost:${port}`);
    });
  }

  startPeriodicReporting() {
    // 每小时报告一次统计
    setInterval(() => {
      console.log('\n📈 === 小时统计报告 ===');
      console.log(`运行时间: ${((Date.now() - this.stats.startTime) / 3600000).toFixed(1)} 小时`);
      console.log(`总提交数: ${this.stats.totalCommits}`);
      console.log(`成功提交: ${this.stats.successfulCommits}`);
      console.log(`失败提交: ${this.stats.failedCommits}`);
      console.log(`成功率: ${(this.stats.successfulCommits / this.stats.totalCommits * 100 || 0).toFixed(1)}%`);
      console.log(`节省时间: ${(this.stats.totalTimesSaved / 60000).toFixed(1)} 分钟`);
      console.log('==================\n');
    }, 3600000); // 1小时
  }

  notifyHumanIntervention(result) {
    // 这里可以集成各种通知方式
    console.log('\n🔔 === 需要人工介入 ===');
    console.log('请检查以下问题并手动处理:');
    console.log(JSON.stringify(result, null, 2));
    console.log('====================\n');
    
    // 可以添加：发送邮件、Slack通知等
  }

  setupGracefulShutdown() {
    const cleanup = () => {
      console.log('\n👋 正在优雅关闭系统...');
      
      // 停止文件监控
      if (this.watcher) {
        this.watcher.close();
      }
      
      // 清理定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      
      // 保存统计数据
      this.saveStats();
      
      console.log('✅ 系统已安全关闭');
      process.exit(0);
    };
    
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
  }

  saveStats() {
    try {
      const statsPath = path.join(__dirname, '../logs/stats.json');
      fs.writeFileSync(statsPath, JSON.stringify(this.stats, null, 2));
    } catch (error) {
      console.error('保存统计失败:', error);
    }
  }
}

// 主程序入口
if (require.main === module) {
  const system = new IntelligentCommitSystem();
  system.start();
}

module.exports = { IntelligentCommitSystem };