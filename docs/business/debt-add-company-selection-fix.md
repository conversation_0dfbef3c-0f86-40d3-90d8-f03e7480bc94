# 逾期债权新增录入界面管理公司选择逻辑修复

## 修复日期
2025-01-05

## 问题描述
前端逾期债权新增录入界面的管理公司选项逻辑存在以下问题：
1. 默认情况下没有正确显示用户所属的管理公司
2. 管理员角色判断不完整，只判断了`ADMIN`，没有判断`ROLE_ADMIN`
3. 用户对象中的公司字段可能是`company`或`companyname`，需要兼容处理

## 解决方案

### 1. 添加管理员角色判断逻辑
```javascript
// 判断用户是否是管理员
const isAdmin = user?.role === 'ADMIN' || user?.role === 'ROLE_ADMIN';
```

### 2. 修改管理公司默认值逻辑
```javascript
// 初始化表单时
managementCompany: isAdmin ? '' : user?.company || user?.companyname || '',

// 重置表单时
managementCompany: isAdmin ? '' : user?.company || user?.companyname || '',
```

### 3. 修改管理公司选择框选项逻辑
```javascript
options={
  isAdmin
    ? managementCompanies.map(company => ({
        value: company,
        label: company,
      }))
    : [
        {
          value: user?.company || user?.companyname || '',
          label: user?.company || user?.companyname || '请联系管理员设置公司',
        },
      ]
}
disabled={fieldsReadOnly.managementCompany || !isAdmin}
```

## 技术细节

### 角色系统
- 后端JWT中的角色信息带有`ROLE_`前缀（如`ROLE_ADMIN`）
- 前端需要同时判断`ADMIN`和`ROLE_ADMIN`以确保兼容性

### 用户信息字段
- 用户对象中的公司信息可能存在于`company`或`companyname`字段
- 使用`||`运算符进行兼容性处理

### 业务逻辑
1. **普通用户**：
   - 默认显示其所属的管理公司
   - 不能修改管理公司选项（disabled状态）
   - 只能看到自己所属公司的选项

2. **管理员用户**：
   - 初始状态管理公司为空
   - 可以选择所有可用的管理公司
   - 拥有完整的编辑权限

## 影响范围
- 文件：`src/layouts/debtmanagement/pages/OverdueDebtAdd.js`
- 功能：逾期债权新增录入界面的管理公司选择

## 测试要点
1. 普通用户登录后，管理公司应默认显示其所属公司
2. 管理员登录后，管理公司初始为空，可选择所有公司
3. 普通用户不能修改管理公司选项
4. 管理员可以自由选择管理公司