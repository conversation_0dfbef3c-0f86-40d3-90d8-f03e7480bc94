# 业务文档

本目录包含FinancialSystem项目的业务逻辑、功能实现和需求分析文档。

## 📋 文档分类

### 🏢 核心业务实体
- [业务实体说明](entities.md) - 系统核心业务实体定义和关系

### 💰 债权管理
- [债权删除业务逻辑](debt-deletion-business-logic.md) - 债权删除功能的业务逻辑设计
- [债权删除实现细节](债权删除实现细节.md) - 债权删除功能的技术实现细节
- [债权删除功能实现总结](债权删除功能实现总结.md) - 债权删除功能开发总结
- [为什么需要独立的删除功能](why-separate-delete-function.md) - 债权删除功能设计理念
- [债权处置流程分析](debt-disposal-flow-analysis.md) - 债权处置业务流程分析

### 🔄 债权转换系统
- [债权转换系统](debt-conversion-system.md) - 诉讼与非诉讼债权转换功能设计
- [债权新增公司选择修复](debt-add-company-selection-fix.md) - 债权新增时公司选择功能优化

### 📊 业务分析与规划
- [债权功能统一规划分析](debt-feature-unified-planning-analysis.md) - 债权功能整体规划分析
- [债权功能统一规划分析报告](债权功能统一规划分析报告.md) - 详细的债权功能规划报告
- [项目状态](project-status.md) - 项目业务功能完成状态

### 🗄️ 数据管理
- [前端字段与后端表字段映射分析](前端字段与后端表字段映射分析.md) - 前后端数据映射关系分析
- [后续月份更新逻辑分析](后续月份更新逻辑分析.md) - 月度数据更新业务逻辑
- [用户系统数据库迁移与问题修复记录](用户系统数据库迁移与问题修复记录.md) - 用户系统数据迁移记录

## 🎯 业务功能概览

### 主要业务模块
1. **债权管理** - 债权的新增、修改、删除、查询
2. **债权处置** - 债权处置流程和状态管理
3. **债权转换** - 诉讼与非诉讼债权之间的转换
4. **减值准备** - 债权减值准备的计算和管理
5. **数据分析** - 债权数据的统计分析和报表生成

### 核心业务流程
1. **债权录入** → **债权审核** → **债权确认**
2. **债权处置** → **处置跟踪** → **结果记录**
3. **减值计算** → **减值确认** → **减值调整**
4. **数据统计** → **报表生成** → **分析展示**

## 📈 业务规则

### 数据一致性规则
- 债权金额必须为正数
- 处置金额不能超过债权本金
- 减值准备不能超过债权余额
- 转换操作必须保持数据完整性

### 业务逻辑规则
- 已处置债权不能再次处置
- 已删除债权不能进行任何操作
- 债权转换需要满足特定条件
- 月度更新按照既定逻辑执行

## 🔍 快速查找

### 按功能查找
- **债权删除** → [债权删除业务逻辑](debt-deletion-business-logic.md)
- **债权转换** → [债权转换系统](debt-conversion-system.md)
- **数据映射** → [前端字段映射分析](前端字段与后端表字段映射分析.md)
- **业务规划** → [债权功能统一规划分析](debt-feature-unified-planning-analysis.md)

### 按类型查找
- **设计文档** → 查看各功能的业务逻辑文档
- **实现细节** → 查看技术实现相关文档
- **分析报告** → 查看业务分析和规划文档
- **问题修复** → 查看功能优化和修复记录

## 📝 文档贡献

如果您需要添加或更新业务文档，请遵循以下规范：

1. **文档分类**: 按功能模块分类
2. **命名规范**: 使用描述性名称
3. **内容结构**: 包含业务背景、逻辑设计、实现方案
4. **更新索引**: 在本README中添加链接

---

**最后更新**: 2025-08-03  
**维护者**: FinancialSystem开发团队
