-- 资产管理系统数据库设计
-- 创建时间: 2025-08-01
-- 作者: laoshu198838

-- 1. 资产基本信息表
CREATE TABLE asset_basic_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_name VARCHAR(200) NOT NULL COMMENT '资产名称',
    property_owner_id BIGINT NOT NULL COMMENT '产权人ID(关联companies表)',
    management_company VARCHAR(100) NOT NULL COMMENT '管理公司',
    property_certificate_no VARCHAR(100) COMMENT '权属证号',
    has_property_certificate TINYINT(1) DEFAULT 1 COMMENT '是否有产权证(1:有 0:无)',
    acquisition_date DATE COMMENT '获取时间',
    purchase_contract_no VARCHAR(200) COMMENT '购买合同编号',
    purchase_price DECIMAL(20,2) COMMENT '购买价格',
    location VARCHAR(500) COMMENT '位置',
    total_area DECIMAL(15,2) NOT NULL COMMENT '总面积(平方米)',
    property_years INT COMMENT '产权年限',
    used_years INT COMMENT '已使用年限',
    remaining_years INT COMMENT '剩余使用年限',
    asset_type ENUM('PROPERTY', 'LAND') NOT NULL COMMENT '资产类型：房产/土地',
    status ENUM('ACTIVE', 'INACTIVE', 'DISPOSED') DEFAULT 'ACTIVE' COMMENT '资产状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    INDEX idx_asset_name (asset_name),
    INDEX idx_property_owner (property_owner_id),
    INDEX idx_management_company (management_company),
    INDEX idx_asset_type (asset_type),
    INDEX idx_has_certificate (has_property_certificate)
) COMMENT='资产基本信息表';

-- 2. 资产状态表(历史快照模式)
CREATE TABLE asset_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    management_company VARCHAR(100) NOT NULL COMMENT '管理公司',
    status_year INT NOT NULL COMMENT '状态年份',
    status_month INT NOT NULL COMMENT '状态月份',
    total_area DECIMAL(15,2) NOT NULL COMMENT '总面积',
    self_use_area DECIMAL(15,2) DEFAULT 0 COMMENT '自用面积',
    rental_area DECIMAL(15,2) DEFAULT 0 COMMENT '出租面积',
    idle_area DECIMAL(15,2) DEFAULT 0 COMMENT '闲置面积(自动计算)',
    remark TEXT COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_management_company (management_company),
    INDEX idx_status_period (status_year, status_month),
    UNIQUE KEY uk_asset_status_period (asset_id, status_year, status_month)
) COMMENT='资产状态表';

-- 3. 出租信息表
CREATE TABLE rental_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    management_company VARCHAR(100) NOT NULL COMMENT '管理公司',
    lessor VARCHAR(100) NOT NULL COMMENT '出租人',
    lessee VARCHAR(100) NOT NULL COMMENT '承租人',
    contract_no VARCHAR(100) COMMENT '合同编号',
    contract_sign_date DATE COMMENT '合同签订时间',
    rental_start_date DATE NOT NULL COMMENT '出租起始日期',
    rental_end_date DATE NOT NULL COMMENT '出租截止日期',
    rental_area DECIMAL(15,2) NOT NULL COMMENT '出租面积',
    monthly_rent DECIMAL(15,2) NOT NULL COMMENT '月租金',
    contract_status ENUM('ACTIVE', 'EXPIRED', 'TERMINATED') DEFAULT 'ACTIVE' COMMENT '合同状态',
    is_expiring_soon TINYINT(1) DEFAULT 0 COMMENT '是否即将到期(30天内)',
    remark TEXT COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_management_company (management_company),
    INDEX idx_rental_dates (rental_start_date, rental_end_date),
    INDEX idx_contract_status (contract_status),
    INDEX idx_expiring_soon (is_expiring_soon)
) COMMENT='出租信息表';

-- 4. 资产财务信息表
CREATE TABLE asset_financial_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    management_company VARCHAR(100) NOT NULL COMMENT '管理公司',
    accounting_date DATE NOT NULL COMMENT '入账时间',
    original_value DECIMAL(20,2) NOT NULL COMMENT '入账原值',
    depreciation_years INT NOT NULL COMMENT '折旧年限',
    depreciation_method ENUM('STRAIGHT_LINE', 'WORK_LOAD', 'DOUBLE_DECLINING_BALANCE', 'SUM_OF_YEARS') DEFAULT 'STRAIGHT_LINE' COMMENT '折旧方法',
    residual_rate DECIMAL(5,4) DEFAULT 0.05 COMMENT '残值率',
    book_value DECIMAL(20,2) NOT NULL COMMENT '账面价值',
    financial_year INT NOT NULL COMMENT '财务年度',
    financial_month INT NOT NULL COMMENT '财务月份',
    accumulated_depreciation DECIMAL(20,2) DEFAULT 0 COMMENT '累计折旧',
    monthly_depreciation DECIMAL(20,2) DEFAULT 0 COMMENT '月折旧额',
    is_auto_generated TINYINT(1) DEFAULT 0 COMMENT '是否自动生成',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_management_company (management_company),
    INDEX idx_financial_period (financial_year, financial_month),
    INDEX idx_auto_generated (is_auto_generated),
    UNIQUE KEY uk_asset_financial_period (asset_id, financial_year, financial_month)
) COMMENT='资产财务信息表';

-- 5. 文件附件表
CREATE TABLE asset_attachments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    asset_id BIGINT NOT NULL COMMENT '资产ID',
    management_company VARCHAR(100) NOT NULL COMMENT '管理公司',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_file_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    attachment_type ENUM('PURCHASE_CONTRACT', 'RENTAL_CONTRACT', 'PROPERTY_CERTIFICATE', 'OTHER') NOT NULL COMMENT '附件类型',
    contract_no VARCHAR(100) COMMENT '关联合同编号',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    uploaded_by VARCHAR(50) COMMENT '上传人',
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
    sync_time DATETIME COMMENT '同步时间',
    local_path VARCHAR(500) COMMENT 'Mac本地路径',
    FOREIGN KEY (asset_id) REFERENCES asset_basic_info(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_management_company (management_company),
    INDEX idx_attachment_type (attachment_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_contract_no (contract_no)
) COMMENT='文件附件表';

-- 6. 创建视图：瑕疵资产统计
CREATE VIEW v_defective_assets AS
SELECT 
    management_company,
    asset_type,
    COUNT(*) as asset_count,
    SUM(total_area) as total_area
FROM asset_basic_info 
WHERE has_property_certificate = 0 
  AND status = 'ACTIVE'
GROUP BY management_company, asset_type;

-- 7. 创建视图：资产盘活统计
CREATE VIEW v_asset_activation_stats AS
SELECT 
    a.management_company,
    a.asset_type,
    COUNT(DISTINCT a.id) as total_assets,
    SUM(a.total_area) as total_area,
    SUM(COALESCE(s.self_use_area, 0) + COALESCE(s.rental_area, 0)) as activated_area,
    ROUND(
        SUM(COALESCE(s.self_use_area, 0) + COALESCE(s.rental_area, 0)) / SUM(a.total_area) * 100, 2
    ) as activation_rate
FROM asset_basic_info a
LEFT JOIN asset_status s ON a.id = s.asset_id 
    AND s.status_year = YEAR(CURDATE()) 
    AND s.status_month = MONTH(CURDATE())
WHERE a.status = 'ACTIVE'
GROUP BY a.management_company, a.asset_type;

-- 8. 创建触发器：自动计算闲置面积
DELIMITER //
CREATE TRIGGER tr_calculate_idle_area_insert
BEFORE INSERT ON asset_status
FOR EACH ROW
BEGIN
    SET NEW.idle_area = NEW.total_area - NEW.self_use_area - NEW.rental_area;
END//

CREATE TRIGGER tr_calculate_idle_area_update
BEFORE UPDATE ON asset_status
FOR EACH ROW
BEGIN
    SET NEW.idle_area = NEW.total_area - NEW.self_use_area - NEW.rental_area;
END//
DELIMITER ;

-- 9. 创建触发器：合同到期提醒
DELIMITER //
CREATE TRIGGER tr_check_contract_expiring
BEFORE INSERT ON rental_info
FOR EACH ROW
BEGIN
    IF DATEDIFF(NEW.rental_end_date, CURDATE()) <= 30 AND DATEDIFF(NEW.rental_end_date, CURDATE()) >= 0 THEN
        SET NEW.is_expiring_soon = 1;
    END IF;
END//

CREATE TRIGGER tr_check_contract_expiring_update
BEFORE UPDATE ON rental_info
FOR EACH ROW
BEGIN
    IF DATEDIFF(NEW.rental_end_date, CURDATE()) <= 30 AND DATEDIFF(NEW.rental_end_date, CURDATE()) >= 0 THEN
        SET NEW.is_expiring_soon = 1;
    ELSE
        SET NEW.is_expiring_soon = 0;
    END IF;
END//
DELIMITER ;

-- 10. 插入初始数据示例
-- INSERT INTO asset_basic_info (asset_name, property_owner_id, management_company, asset_type, total_area, has_property_certificate) 
-- VALUES ('示例房产', 1, '万润科技', 'PROPERTY', 1000.00, 1);
