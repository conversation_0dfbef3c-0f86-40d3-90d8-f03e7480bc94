# 开发流程命令指南 - FinancialSystem

本文档详细说明了在FinancialSystem项目中使用的所有简化命令，以及每个命令背后自动执行的任务。按照实际开发流程组织，方便查阅和改进。

## 📋 目录

1. [项目准备阶段](#1-项目准备阶段)
2. [需求分析阶段](#2-需求分析阶段)
3. [开发实现阶段](#3-开发实现阶段)
4. [测试验证阶段](#4-测试验证阶段)
5. [代码提交阶段](#5-代码提交阶段)
6. [合并集成阶段](#6-合并集成阶段)
7. [部署发布阶段](#7-部署发布阶段)
8. [维护优化阶段](#8-维护优化阶段)

---

## 1. 项目准备阶段

### 同步最新代码
**命令**: `同步代码`
**自动执行**:
1. 检查当前分支状态
2. 暂存未提交的更改（如果有）
3. 拉取远程最新代码
4. 自动合并（如果无冲突）
5. 恢复暂存的更改
6. 显示同步结果

### 查看项目状态
**命令**: `查看改动`
**自动执行**:
1. 显示未提交的文件列表
2. 显示具体的代码改动
3. 统计改动行数
4. 标记文件状态（新增/修改/删除）

### 清理工作环境
**命令**: `清理工作区`
**自动执行**:
1. 暂存当前所有更改
2. 删除所有未跟踪文件
3. 清理.gitignore中的文件
4. 压缩Git仓库对象
5. 清理过期的reflog
6. 显示清理报告和释放的空间

---

## 2. 需求分析阶段

### 查看待办任务
**命令**: `查看todo`
**自动执行**:
1. 读取TODO.md文件
2. 格式化显示所有任务
3. 高亮显示优先级
4. 统计完成进度
5. 显示最近更新的任务

### 查看紧急任务
**命令**: `查看todo 紧急`
**自动执行**:
1. 读取TODO.md文件
2. 筛选高优先级任务
3. 筛选包含"紧急"标记的任务
4. 按优先级排序显示
5. 显示预计工时

### 添加新需求
**命令**: `添加todo：[描述]`
**示例**: `添加todo：优化数据库查询性能`
**自动执行**:
1. 解析任务描述
2. 自动分配任务编号
3. 设置默认优先级
4. 添加创建时间戳
5. 更新TODO.md文件
6. 显示添加成功确认

---

## 3. 开发实现阶段

### 开始新功能开发
**命令**: `新功能：[功能名]`
**示例**: `新功能：用户权限管理`
**自动执行**:
1. 暂存当前工作（如果有）
2. 切换到develop分支
3. 拉取最新develop代码
4. 创建feature/user-permission-management分支
5. 设置上游分支
6. 恢复暂存的工作
7. 显示分支创建成功信息

### 开始修复Bug
**命令**: `快速修复：[描述]`
**示例**: `快速修复：登录页面崩溃`
**自动执行**:
1. 暂存当前工作
2. 切换到main分支
3. 拉取最新main代码
4. 创建hotfix/fix-login-page-crash分支
5. 设置上游分支
6. 显示修复指导信息

### 切换分支（智能）
**命令**: `切换：[分支名/描述]`
**示例**: `切换：优化性能`
**自动执行**:
1. 检查分支是否存在
2. 如果不存在：
   - 分析描述内容
   - 判断分支类型（feature/hotfix/release）
   - 转换中文为英文分支名
   - 选择正确的基础分支
   - 创建新分支
3. 如果存在：
   - 暂存当前更改
   - 切换到目标分支
   - 恢复暂存内容
4. 显示当前分支信息

### 实现TODO任务
**命令**: `实现todo：[关键词]` 或 `实现todo #[编号]`
**示例**: `实现todo 权限管理` 或 `实现todo #001`
**自动执行**:
1. 读取TODO.md文件
2. 查找匹配的任务
3. 提取任务详细需求
4. 创建对应的分支（如feature/permission-management）
5. 创建TodoWrite任务列表追踪进度
6. 显示任务需求和技术方案
7. 开始执行实现

### 保存工作进度
**命令**: `保存进度`
**自动执行**:
1. 添加所有改动到暂存区
2. 创建带时间戳的stash
3. 保存stash描述
4. 显示保存成功信息
5. 提示如何恢复

### 恢复工作进度
**命令**: `恢复进度`
**自动执行**:
1. 列出所有stash
2. 应用最新的stash
3. 处理可能的冲突
4. 删除已应用的stash
5. 显示恢复的文件列表

---

## 4. 测试验证阶段

### 运行快速测试
**命令**: `测试代码`
**自动执行**:
1. 检测项目类型（Java/JavaScript）
2. 运行单元测试
3. 只测试最近修改的模块
4. 显示测试结果摘要
5. 标记失败的测试
6. 计算测试覆盖率

### 运行完整测试
**命令**: `完整测试`
**自动执行**:
1. 清理测试环境
2. 运行所有单元测试
3. 运行集成测试
4. 运行端到端测试
5. 生成测试报告
6. 检查测试覆盖率是否达标（>60%）
7. 生成覆盖率报告

### 构建项目
**命令**: `构建项目`
**自动执行**:
1. 清理之前的构建产物
2. 安装/更新依赖
3. 编译后端代码（Maven）
4. 构建前端资源（npm）
5. 打包应用
6. 优化资源文件
7. 生成构建报告

---

## 5. 代码提交阶段

### 智能提交
**命令**: `提交：[描述]`
**示例**: `提交：修复用户登录验证问题`
**自动执行**:
1. 检查是否有改动
2. 运行pre-commit hooks：
   - ESLint代码检查
   - Prettier格式化
   - 单元测试（可选）
3. 自动识别提交类型：
   - feat: 新功能
   - fix: 修复
   - docs: 文档
   - refactor: 重构
   - test: 测试
   - chore: 杂项
4. 生成规范的提交信息
5. 添加Co-authored-by标识
6. 执行提交
7. 显示提交成功信息

### 查看提交历史
**命令**: `查看历史`
**自动执行**:
1. 显示最近的提交记录
2. 格式化显示（单行模式）
3. 显示分支图
4. 高亮当前分支
5. 显示标签信息

---

## 6. 合并集成阶段

### 合并到目标分支
**命令**: `合并：[分支名]`
**示例**: `合并：develop`
**自动执行**:
1. 检查当前分支状态
2. 自动提交未保存的更改
3. 切换到目标分支（develop）
4. 拉取目标分支最新代码
5. 合并当前分支到目标分支
6. 处理合并冲突（如果有）
7. 运行合并后测试
8. 删除已合并的源分支
9. 自动创建新的feature分支（默认行为）
10. 推送到远程仓库

### 完成TODO任务
**命令**: `完成todo #[编号]`
**示例**: `完成todo #001`
**自动执行**:
1. 更新TODO.md文件
2. 标记任务为完成
3. 添加完成时间
4. 更新完成进度统计
5. 提交TODO.md更改
6. 可选：合并相关分支

---

## 7. 部署发布阶段

### 部署到测试环境
**命令**: `部署测试`
**自动执行**:
1. 运行部署前检查
2. 执行快速测试
3. 构建测试版本
4. 连接测试服务器
5. 备份当前测试环境
6. 上传新版本
7. 重启测试服务
8. 执行健康检查
9. 显示部署结果

### 发布到生产环境
**命令**: `发布` 或 `发布：[版本号]`
**示例**: `发布：1.2.0`
**自动执行**:
1. 确认发布（需要输入yes）
2. 运行完整测试套件
3. 构建生产版本
4. 创建Git标签
5. 生成发布日志
6. 备份生产服务器
7. 部署到生产环境
8. 执行烟雾测试
9. 监控服务状态
10. 发送部署报告

### 紧急回滚
**命令**: `回滚生产`
**自动执行**:
1. 确认回滚操作
2. 停止生产服务
3. 恢复上一个备份
4. 重启服务
5. 验证服务状态
6. 更新部署记录
7. 发送回滚通知

---

## 8. 维护优化阶段

### 安全回退版本
**命令**: `回退：[commit-id]`
**示例**: `回退：abc123`
**自动执行**:
1. 保存当前所有工作
2. 创建备份分支
3. 清理未跟踪文件
4. 执行硬重置到指定版本
5. 验证工作区状态
6. 显示回退结果
7. 提供恢复选项

### 版本标签管理
**命令**: `创建标签：[版本号]`
**示例**: `创建标签：v1.2.0`
**自动执行**:
1. 验证版本号格式
2. 检查标签是否存在
3. 创建带注释的标签
4. 包含发布说明
5. 推送标签到远程
6. 更新版本文件

### 分支清理
**命令**: `清理分支`
**自动执行**:
1. 列出所有本地分支
2. 检查合并状态
3. 删除已合并的分支
4. 清理远程已删除的分支
5. 保留保护分支（main/develop）
6. 显示清理结果

---

## 🔧 配置和自定义

### 修改自动化行为

所有自动化配置都在 `.claude/settings.json` 中：

```json
{
  "git_workflow": {
    "automation": {
      "auto_stash": true,      // 自动暂存
      "auto_pull": true,       // 自动拉取
      "auto_push": false,      // 自动推送（默认关闭）
      "auto_tag": true         // 自动标签
    }
  }
}
```

### 修改质量门控

```json
{
  "quality_gates": {
    "pre_commit": {
      "run_tests": false,      // 提交前测试
      "format_code": true,     // 代码格式化
      "lint_check": true       // 代码检查
    }
  }
}
```

### 修改部署配置

在 `scripts/quick-deploy.sh` 中修改：
- DEPLOY_HOST：服务器地址
- DEPLOY_PATH：部署路径
- SERVICE_NAME：服务名称

---

## 📝 命令速查表

| 开发阶段 | 命令 | 用途 |
|---------|------|------|
| 准备 | `同步代码` | 获取最新代码 |
| 准备 | `查看改动` | 查看未提交更改 |
| 准备 | `清理工作区` | 清理工作环境 |
| 需求 | `查看todo` | 查看所有任务 |
| 需求 | `添加todo：描述` | 添加新任务 |
| 开发 | `新功能：名称` | 开始新功能 |
| 开发 | `快速修复：描述` | 开始修复bug |
| 开发 | `切换：分支名` | 智能切换分支 |
| 开发 | `实现todo #001` | 实现指定任务 |
| 开发 | `保存进度` | 暂存当前工作 |
| 测试 | `测试代码` | 快速测试 |
| 测试 | `完整测试` | 完整测试 |
| 测试 | `构建项目` | 构建应用 |
| 提交 | `提交：描述` | 智能提交 |
| 合并 | `合并：分支名` | 合并分支 |
| 合并 | `完成todo #001` | 标记任务完成 |
| 部署 | `部署测试` | 部署测试环境 |
| 部署 | `发布` | 发布生产 |
| 部署 | `回滚生产` | 紧急回滚 |
| 维护 | `回退：commit` | 回退版本 |
| 维护 | `清理分支` | 清理已合并分支 |

---

## 🚀 最佳实践

1. **每日工作流程**：
   ```
   同步代码 → 查看todo → 切换：新功能 → 开发 → 测试代码 → 提交：完成功能 → 合并：develop
   ```

2. **紧急修复流程**：
   ```
   快速修复：问题描述 → 修复代码 → 测试代码 → 提交：修复问题 → 合并：main → 发布
   ```

3. **功能开发流程**：
   ```
   实现todo #001 → 开发 → 保存进度 → 测试代码 → 提交：实现功能 → 完成todo #001
   ```

---

*最后更新：2025-01-31 | 持续优化中*