# Git工作流程可视化

本文档使用流程图展示FinancialSystem项目的开发工作流程，帮助理解命令之间的关系和自动化流程。

## 🎯 整体工作流程

```mermaid
graph TB
    Start[开始工作] --> Sync[同步代码]
    Sync --> CheckTodo[查看todo]
    
    CheckTodo --> NewFeature{选择任务类型}
    
    NewFeature -->|新功能| CreateFeature[新功能：功能名]
    NewFeature -->|修复Bug| CreateHotfix[快速修复：描述]
    NewFeature -->|实现TODO| ImplementTodo[实现todo #001]
    
    CreateFeature --> Development[开发阶段]
    CreateHotfix --> Development
    ImplementTodo --> Development
    
    Development --> Test[测试代码]
    Test -->|通过| Commit[提交：描述]
    Test -->|失败| Development
    
    Commit --> Merge{合并目标}
    Merge -->|功能/修复| MergeDevelop[合并：develop]
    Merge -->|紧急修复| MergeMain[合并：main]
    
    MergeDevelop --> Deploy{部署环境}
    MergeMain --> Deploy
    
    Deploy -->|测试| DeployTest[部署测试]
    Deploy -->|生产| DeployProd[发布]
    
    DeployTest --> End[结束]
    DeployProd --> End
    
    style Start fill:#e1f5fe
    style End fill:#c8e6c9
    style Development fill:#fff3e0
    style Test fill:#f3e5f5
    style DeployProd fill:#ffebee
```

## 🔄 分支管理流程

```mermaid
graph LR
    Main[main] --> Hotfix[hotfix/*]
    Main --> Release[release/*]
    
    Develop[develop] --> Feature[feature/*]
    Develop --> Bugfix[bugfix/*]
    
    Feature --> Develop
    Bugfix --> Develop
    Release --> Main
    Release --> Develop
    Hotfix --> Main
    Hotfix --> Develop
    
    style Main fill:#ff6b6b,color:#fff
    style Develop fill:#4ecdc4,color:#fff
    style Feature fill:#95e1d3
    style Hotfix fill:#ffa502
    style Release fill:#a29bfe
```

## 🤖 智能分支创建逻辑

```mermaid
graph TD
    Input[用户输入] --> Analyze{分析输入}
    
    Analyze -->|包含"修复/fix"| TypeHotfix[类型: hotfix]
    Analyze -->|包含"功能/feature"| TypeFeature[类型: feature]
    Analyze -->|包含"发布/release"| TypeRelease[类型: release]
    Analyze -->|其他| TypeFeature
    
    TypeHotfix --> BaseMain[基于: main]
    TypeFeature --> BaseDevelop[基于: develop]
    TypeRelease --> BaseDevelop
    
    BaseMain --> CreateBranch[创建分支]
    BaseDevelop --> CreateBranch
    
    CreateBranch --> TranslateName[转换中文为英文]
    TranslateName --> FinalBranch[生成: type/name]
    
    style Input fill:#e3f2fd
    style FinalBranch fill:#c8e6c9
```

## 📦 提交流程自动化

```mermaid
sequenceDiagram
    participant User as 用户
    participant Claude as Claude
    participant Git as Git系统
    participant Hooks as Git Hooks
    
    User->>Claude: 提交：修复登录问题
    Claude->>Git: 检查工作区状态
    Git-->>Claude: 返回改动文件
    
    Claude->>Hooks: 触发pre-commit
    Hooks->>Hooks: ESLint检查
    Hooks->>Hooks: Prettier格式化
    Hooks-->>Claude: 检查通过
    
    Claude->>Claude: 识别提交类型(fix)
    Claude->>Git: git commit -m "fix: 修复登录问题"
    Git->>Git: 创建提交
    Git-->>Claude: 提交成功
    
    Claude-->>User: ✅ 提交完成
```

## 🚀 部署流程自动化

```mermaid
graph TD
    Deploy[发布命令] --> Confirm{确认部署?}
    Confirm -->|否| Cancel[取消]
    Confirm -->|是| Tests[运行完整测试]
    
    Tests -->|失败| TestFail[❌ 测试失败]
    Tests -->|通过| Build[构建项目]
    
    Build --> Tag[创建版本标签]
    Tag --> Backup[备份服务器]
    Backup --> Upload[上传新版本]
    Upload --> Restart[重启服务]
    Restart --> Health[健康检查]
    
    Health -->|失败| Rollback[自动回滚]
    Health -->|成功| Success[✅ 部署成功]
    
    Rollback --> RollbackDone[恢复备份]
    
    style Deploy fill:#e3f2fd
    style Success fill:#c8e6c9
    style TestFail fill:#ffcdd2
    style Rollback fill:#fff3e0
```

## 🔗 TODO集成工作流

```mermaid
graph LR
    TodoCommand[实现todo 权限管理] --> ReadTodo[读取TODO.md]
    ReadTodo --> FindTask{查找任务}
    
    FindTask -->|找到| ExtractDetails[提取任务详情]
    FindTask -->|未找到| NotFound[❌ 任务不存在]
    
    ExtractDetails --> ShowDetails[显示需求和技术方案]
    ShowDetails --> CreateBranch[创建feature/permission-management]
    CreateBranch --> SetupTracking[设置TodoWrite追踪]
    SetupTracking --> StartDev[开始开发]
    
    StartDev --> Complete[完成todo #001]
    Complete --> UpdateTodo[更新TODO.md]
    UpdateTodo --> MergeBranch[可选：合并分支]
    
    style TodoCommand fill:#e3f2fd
    style StartDev fill:#fff3e0
    style Complete fill:#c8e6c9
```

## 📊 命令执行时间线

```mermaid
gantt
    title 典型功能开发时间线
    dateFormat HH:mm
    section 准备阶段
    同步代码          :done, sync, 09:00, 1m
    查看todo          :done, todo, after sync, 30s
    切换分支          :done, branch, after todo, 30s
    
    section 开发阶段
    编写代码          :active, dev, after branch, 2h
    测试代码          :test, after dev, 10m
    
    section 提交阶段
    代码检查          :check, after test, 2m
    提交代码          :commit, after check, 1m
    
    section 集成阶段
    合并到develop     :merge, after commit, 2m
    部署测试环境      :deploy, after merge, 5m
```

## 🎨 状态转换图

```mermaid
stateDiagram-v2
    [*] --> 工作区干净
    
    工作区干净 --> 有未提交更改: 修改文件
    有未提交更改 --> 暂存更改: 保存进度
    有未提交更改 --> 已提交: 提交代码
    暂存更改 --> 工作区干净: 恢复进度
    已提交 --> 已推送: 推送远程
    已推送 --> 已合并: 合并分支
    已合并 --> 已部署: 部署环境
    已部署 --> [*]
    
    有未提交更改 --> 工作区干净: 清理工作区
```

## 🔍 错误处理流程

```mermaid
graph TD
    Error[发生错误] --> ErrorType{错误类型}
    
    ErrorType -->|合并冲突| Conflict[处理冲突]
    ErrorType -->|测试失败| TestFail[修复测试]
    ErrorType -->|构建失败| BuildFail[修复构建]
    ErrorType -->|部署失败| DeployFail[回滚版本]
    
    Conflict --> Manual[手动解决]
    TestFail --> FixCode[修改代码]
    BuildFail --> CheckDep[检查依赖]
    DeployFail --> AutoRollback[自动回滚]
    
    Manual --> Retry[重试操作]
    FixCode --> Retry
    CheckDep --> Retry
    AutoRollback --> Investigate[调查原因]
    
    style Error fill:#ffcdd2
    style Retry fill:#c8e6c9
```

## 📈 效率提升对比

### 传统方式 vs 简化命令

| 操作 | 传统方式（步骤） | 简化命令（步骤） | 节省时间 |
|------|-----------------|-----------------|----------|
| 创建功能分支 | 5步 | 1步 | 80% |
| 提交代码 | 4步 | 1步 | 75% |
| 合并分支 | 7步 | 1步 | 85% |
| 部署生产 | 10步 | 1步 | 90% |
| 实现TODO | 8步 | 1步 | 87% |

### 自动化收益

```mermaid
pie title 开发时间分配优化
    "实际编码" : 60
    "测试验证" : 20
    "Git操作" : 5
    "部署发布" : 5
    "其他" : 10
```

*优化后，Git操作和部署发布时间从30%降低到10%，让您专注于核心开发工作*

---

## 🎯 快速开始建议

1. **先熟悉这5个命令**：
   - `同步代码` - 每天开始工作
   - `切换：[描述]` - 创建/切换分支
   - `提交：[描述]` - 提交代码
   - `合并：develop` - 合并到开发
   - `查看改动` - 查看状态

2. **逐步学习高级功能**：
   - TODO集成
   - 智能分支
   - 一键部署

3. **根据需要定制**：
   - 修改 `.claude/settings.json`
   - 调整脚本参数
   - 添加自定义命令

---

*可视化文档 - 帮助理解工作流程和命令关系*