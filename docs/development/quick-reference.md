# 开发快速参考手册

## 🚀 快速启动命令

### 编译和启动
```bash
# 清理编译
cd /Volumes/ExternalSSD-1T/08.program/FinancialSystem
mvn clean compile -DskipTests

# 启动后端应用
mvn spring-boot:run -pl api-gateway

# 启动前端应用 (另一个终端)
cd FinancialSystem-web
npm start
```

### 访问地址
- 前端应用: http://localhost:3001
- 后端API: http://localhost:8080
- 数据一致性检查: http://localhost:3001/consistency-check
- 资产管理: http://localhost:3001/asset-management

## 🔧 当前已知问题

### 1. 应用启动失败
**错误**: `Could not resolve attribute 'currentSelfUseArea' of 'com.laoshu198838.entity.asset.AssetBasicInfo'`

**修复步骤**:
1. 添加字段到实体类 `AssetBasicInfo.java`
2. 执行数据库迁移脚本
3. 恢复Repository查询
4. 重新编译启动

**详细指南**: `docs/troubleshooting/application-startup-issues.md`

## 📁 重要文件位置

### 数据一致性检查系统
```
后端:
- Repository: shared/data-access/src/main/java/com/laoshu198838/repository/overdue_debt/ConsistencyCheckRepository.java
- Service: services/debt-management/src/main/java/com/laoshu198838/service/ConsistencyCheckService.java
- Controller: services/debt-management/src/main/java/com/laoshu198838/controller/ConsistencyCheckController.java

前端:
- 主组件: FinancialSystem-web/src/layouts/consistency-check/index.js
- 路由配置: FinancialSystem-web/src/components/AppRoutes.js
- 菜单配置: FinancialSystem-web/src/routes.jsx
```

### 资产管理可视化
```
后端:
- Service: services/asset-management/src/main/java/com/laoshu198838/service/AssetBasicInfoService.java
- Controller: api-gateway/src/main/java/com/laoshu198838/controller/AssetBasicInfoController.java

前端:
- 主页面: FinancialSystem-web/src/layouts/assetmanagement/index.js
- 图表组件: FinancialSystem-web/src/layouts/assetmanagement/components/
  - AssetAreaPieChart.js
  - AssetActivationBarChart.js
  - DefectiveAssetCard.js
```

### 数据库迁移脚本
```
- 资产字段添加: services/data-maintenance/sql/add_asset_area_fields.sql
```

## 🛠️ 常用开发命令

### Maven命令
```bash
# 清理项目
mvn clean

# 编译项目
mvn compile -DskipTests

# 安装依赖
mvn install -DskipTests

# 运行特定模块
mvn spring-boot:run -pl api-gateway

# 查看依赖树
mvn dependency:tree
```

### 前端命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 构建生产版本
npm run build

# 检查代码格式
npm run lint
```

### 数据库命令
```bash
# 连接MySQL
mysql -u root -p

# 执行SQL脚本
mysql -u root -p financial_system < script.sql

# 查看表结构
DESCRIBE table_name;

# 查看数据库列表
SHOW DATABASES;
```

## 📊 API接口快速参考

### 数据一致性检查API
```
GET /api/consistency/check?year=2025&month=8
GET /api/consistency/report?year=2025&month=8
GET /api/consistency/check/new-amount?year=2025&month=8
GET /api/consistency/check/disposed-amount?year=2025&month=8
GET /api/consistency/check/ending-balance?year=2025&month=8
GET /api/consistency/check/year-to-date?year=2025&month=8
GET /api/consistency/health
```

### 资产管理API
```
GET /api/assets/test
GET /api/assets?page=0&size=10
GET /api/assets/{id}
POST /api/assets
PUT /api/assets/{id}
DELETE /api/assets/{id}
GET /api/assets/statistics/area-distribution
GET /api/assets/statistics/company-activation
GET /api/assets/statistics/defective
```

## 🔍 调试技巧

### 后端调试
```bash
# 查看应用日志
tail -f var/log/application.log

# 启动时显示详细信息
mvn spring-boot:run -pl api-gateway -X

# 查看JVM参数
jps -v
```

### 前端调试
```bash
# 查看网络请求
# 打开浏览器开发者工具 -> Network

# 查看控制台日志
# 打开浏览器开发者工具 -> Console

# React开发工具
# 安装React Developer Tools浏览器扩展
```

### 数据库调试
```sql
-- 查看表结构
SHOW CREATE TABLE asset_basic_info;

-- 查看索引
SHOW INDEX FROM asset_basic_info;

-- 查看进程
SHOW PROCESSLIST;

-- 查看错误日志
SHOW VARIABLES LIKE 'log_error';
```

## 📚 文档快速导航

### 开发文档
- 系统状态: `docs/development/consistency-check-system-status.md`
- 进度日志: `docs/development/development-progress-log.md`
- 问题修复: `docs/troubleshooting/application-startup-issues.md`

### 业务文档
- 资产管理: `docs/business/asset-management-requirements.md`
- 数据一致性: `docs/business/data-consistency-requirements.md`

### 技术文档
- 数据库设计: `docs/development/asset-management-database-design.sql`
- API文档: `docs/api/`

## 🎯 下一步开发计划

### 立即执行 (今日)
1. 修复应用启动问题
2. 测试数据一致性检查功能
3. 验证资产管理可视化

### 短期计划 (本周)
1. 完善API文档
2. 添加单元测试
3. 性能优化

### 中期计划 (本月)
1. 添加历史记录功能
2. 实现数据导出
3. 集成测试自动化

## 🆘 紧急联系

### 技术支持
- 开发者: laoshu198838
- 文档位置: `docs/`
- 问题记录: GitHub Issues

### 常见问题
1. **应用启动失败**: 查看 `docs/troubleshooting/application-startup-issues.md`
2. **数据库连接问题**: 检查 `application.yml` 配置
3. **前端编译错误**: 删除 `node_modules` 重新安装
4. **API调用失败**: 检查后端服务是否启动

---

**最后更新**: 2025-08-04
**版本**: v1.0
