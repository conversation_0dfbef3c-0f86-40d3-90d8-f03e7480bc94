# 工程文档管理规则配置
# Engineering Document Management Rules Configuration

version: "1.0"
last_updated: "2025-01-05"

# 文档类型定义
document_types:
  requirements:
    prefix: "REQ"
    output_file: "requirements.md"
    agent: "requirements-analyst"
    description: "需求分析文档"
    
  designs:
    prefix: "TD"
    output_file: "design.md"
    agent: "technical-design-agent"
    description: "技术设计文档"
    
  plans:
    prefix: "PLAN"
    output_file: "tasks.md"
    agent: "implementation-planner"
    description: "实施计划文档"

# 状态定义
statuses:
  active:
    description: "进行中 - 文档正在编写或相关工作正在进行"
    next_actions:
      - "继续完善文档"
      - "等待评审/确认"
      - "执行实施"
      
  completed:
    description: "已完成 - 文档已定稿且相关工作已完成"
    next_actions:
      - "归档"
      - "作为参考"

# 状态流转规则
transition_rules:
  requirements:
    active_to_completed:
      conditions:
        - "用户已确认需求无误"
        - "需求文档状态标记为 confirmed"
        - "已经准备交付给技术设计阶段"
      
  designs:
    active_to_completed:
      conditions:
        - "技术设计已经定稿"
        - "所有技术决策已确定"
        - "已经准备交付给实施规划阶段"
      
  plans:
    active_to_completed:
      conditions:
        - "所有任务已完成"
        - "功能已上线或合并到主分支"
        - "项目/功能已经结束"

# 文件命名规则
naming_convention:
  pattern: "{prefix}-{date}-{feature_name}.md"
  date_format: "YYYYMMDD"
  examples:
    - "REQ-20250105-批量导入.md"
    - "TD-20250105-批量导入.md"
    - "PLAN-20250105-批量导入.md"

# 文档元数据模板
metadata_template: |
  ---
  status: {status}
  created: {created_date}
  updated: {updated_date}
  author: {agent_name}
  feature: {feature_name}
  version: {version}
  ---

# 自动化规则
automation_rules:
  # 新文档创建时
  on_create:
    - action: "add_metadata"
    - action: "place_in_active_folder"
    - action: "create_todo_item"
    
  # 状态变更时
  on_status_change:
    - action: "update_metadata"
    - action: "move_to_appropriate_folder"
    - action: "update_todo_status"
    
  # 工作流流转时
  on_workflow_transition:
    requirements_to_design:
      - action: "create_design_document"
      - action: "link_to_requirements"
    design_to_plan:
      - action: "create_plan_document"
      - action: "link_to_design"