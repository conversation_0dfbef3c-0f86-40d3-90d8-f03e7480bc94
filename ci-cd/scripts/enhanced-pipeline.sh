#!/bin/bash
# Enhanced CI/CD Pipeline Script for FinancialSystem
# Author: Professional CI/CD Architect
# Version: 2.0

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 全局变量
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BRANCH=$(git rev-parse --abbrev-ref HEAD)
COMMIT_SHA=$(git rev-parse --short HEAD)
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
VERSION="${TIMESTAMP}-${COMMIT_SHA}"

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查必要工具
check_requirements() {
    log "检查CI/CD环境要求..."
    
    local required_tools=(
        "git"
        "docker"
        "mvn"
        "npm"
        "jq"
        "curl"
    )
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    success "环境检查通过"
}

# 分支策略检查
check_branch_policy() {
    log "检查分支策略..."
    
    case "$BRANCH" in
        main)
            log "生产分支 - 执行完整流程"
            PIPELINE_TYPE="production"
            ;;
        develop)
            log "开发分支 - 执行集成测试"
            PIPELINE_TYPE="development"
            ;;
        feature/*)
            log "功能分支 - 执行基础检查"
            PIPELINE_TYPE="feature"
            ;;
        hotfix/*)
            log "热修复分支 - 快速流程"
            PIPELINE_TYPE="hotfix"
            ;;
        *)
            warning "未知分支类型: $BRANCH"
            PIPELINE_TYPE="basic"
            ;;
    esac
}

# 代码质量检查
run_quality_checks() {
    log "执行代码质量检查..."
    
    # Java代码检查
    log "检查Java代码..."
    cd "$PROJECT_ROOT"
    mvn clean compile checkstyle:check spotbugs:check || {
        error "Java代码质量检查失败"
        return 1
    }
    
    # JavaScript代码检查
    log "检查JavaScript代码..."
    cd "$PROJECT_ROOT/FinancialSystem-web"
    npm run lint || {
        error "JavaScript代码质量检查失败"
        return 1
    }
    
    npm run type:check || {
        error "TypeScript类型检查失败"
        return 1
    }
    
    success "代码质量检查通过"
}

# 安全扫描
run_security_scan() {
    log "执行安全扫描..."
    
    # 依赖漏洞扫描
    log "扫描Java依赖漏洞..."
    cd "$PROJECT_ROOT"
    mvn org.owasp:dependency-check-maven:check || {
        warning "发现Java依赖漏洞"
    }
    
    log "扫描npm依赖漏洞..."
    cd "$PROJECT_ROOT/FinancialSystem-web"
    npm audit --production || {
        warning "发现npm依赖漏洞"
    }
    
    # Git secrets扫描
    log "扫描敏感信息..."
    if command -v gitleaks &> /dev/null; then
        gitleaks detect --source="$PROJECT_ROOT" || {
            error "发现敏感信息泄露"
            return 1
        }
    fi
    
    success "安全扫描完成"
}

# 单元测试
run_unit_tests() {
    log "执行单元测试..."
    
    # Java单元测试
    log "运行Java单元测试..."
    cd "$PROJECT_ROOT"
    mvn test || {
        error "Java单元测试失败"
        return 1
    }
    
    # JavaScript单元测试
    log "运行JavaScript单元测试..."
    cd "$PROJECT_ROOT/FinancialSystem-web"
    npm run test:coverage -- --watchAll=false || {
        error "JavaScript单元测试失败"
        return 1
    }
    
    # 检查覆盖率
    local coverage=$(grep -oP '(?<=All files[^|]*\|[^|]*\|)\s*\d+' coverage/lcov-report/index.html | tr -d ' ')
    if [ "$coverage" -lt 80 ]; then
        warning "代码覆盖率低于80%: ${coverage}%"
    else
        success "代码覆盖率: ${coverage}%"
    fi
    
    success "单元测试通过"
}

# 集成测试
run_integration_tests() {
    log "执行集成测试..."
    
    # 启动测试环境
    log "启动测试数据库..."
    docker-compose -f docker-compose.test.yml up -d mysql redis
    
    # 等待服务就绪
    sleep 10
    
    # 运行集成测试
    cd "$PROJECT_ROOT"
    mvn verify -Pintegration-test || {
        error "集成测试失败"
        docker-compose -f docker-compose.test.yml down
        return 1
    }
    
    # 清理测试环境
    docker-compose -f docker-compose.test.yml down
    
    success "集成测试通过"
}

# 性能测试
run_performance_tests() {
    log "执行性能测试..."
    
    # 使用JMeter进行API性能测试
    if [ -f "$PROJECT_ROOT/tests/performance/api-test.jmx" ]; then
        jmeter -n -t "$PROJECT_ROOT/tests/performance/api-test.jmx" \
               -l "$PROJECT_ROOT/target/jmeter-results.jtl" \
               -e -o "$PROJECT_ROOT/target/jmeter-report" || {
            warning "性能测试未达到预期"
        }
    fi
    
    # 前端性能测试
    cd "$PROJECT_ROOT/FinancialSystem-web"
    npm run lighthouse || {
        warning "前端性能测试未达到预期"
    }
    
    success "性能测试完成"
}

# 构建Docker镜像
build_docker_images() {
    log "构建Docker镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建后端镜像
    log "构建后端镜像..."
    docker build -t financial-system-backend:$VERSION \
                 -f Dockerfile.backend . || {
        error "后端镜像构建失败"
        return 1
    }
    
    # 构建前端镜像
    log "构建前端镜像..."
    docker build -t financial-system-frontend:$VERSION \
                 -f Dockerfile.frontend . || {
        error "前端镜像构建失败"
        return 1
    }
    
    # 安全扫描镜像
    log "扫描Docker镜像安全漏洞..."
    if command -v trivy &> /dev/null; then
        trivy image financial-system-backend:$VERSION
        trivy image financial-system-frontend:$VERSION
    fi
    
    # 标记镜像
    docker tag financial-system-backend:$VERSION financial-system-backend:latest
    docker tag financial-system-frontend:$VERSION financial-system-frontend:latest
    
    success "Docker镜像构建完成"
}

# 部署策略
deploy_application() {
    local environment=$1
    log "部署到环境: $environment"
    
    case "$environment" in
        development)
            deploy_to_dev
            ;;
        staging)
            deploy_to_staging
            ;;
        production)
            deploy_to_production
            ;;
        *)
            error "未知环境: $environment"
            return 1
            ;;
    esac
}

# 开发环境部署
deploy_to_dev() {
    log "部署到开发环境..."
    
    # 直接部署
    docker-compose -f docker-compose.dev.yml up -d
    
    # 健康检查
    sleep 30
    if ! check_health "http://localhost:8080/actuator/health"; then
        error "开发环境部署失败"
        docker-compose -f docker-compose.dev.yml logs
        return 1
    fi
    
    success "开发环境部署成功"
}

# 预发布环境部署
deploy_to_staging() {
    log "部署到预发布环境..."
    
    # 蓝绿部署
    local current_color=$(get_current_deployment_color staging)
    local new_color=$([[ "$current_color" == "blue" ]] && echo "green" || echo "blue")
    
    log "当前环境: $current_color, 部署到: $new_color"
    
    # 部署新版本
    deploy_color_environment staging "$new_color"
    
    # 健康检查
    if ! check_health "http://staging-$new_color.financial-system.com/health"; then
        error "预发布环境部署失败"
        return 1
    fi
    
    # 切换流量
    switch_traffic staging "$new_color"
    
    # 保留旧版本30分钟
    schedule_cleanup staging "$current_color" 30
    
    success "预发布环境部署成功"
}

# 生产环境部署
deploy_to_production() {
    log "部署到生产环境..."
    
    # 创建备份
    create_production_backup
    
    # 金丝雀部署
    log "开始金丝雀部署..."
    
    # 部署到金丝雀节点（10%流量）
    deploy_canary_version
    
    # 监控30分钟
    log "监控金丝雀部署(30分钟)..."
    if ! monitor_canary_deployment 30; then
        error "金丝雀部署失败，开始回滚"
        rollback_production
        return 1
    fi
    
    # 逐步扩大部署
    log "扩大部署范围..."
    for percentage in 25 50 75 100; do
        log "部署到${percentage}%节点..."
        scale_deployment "$percentage"
        
        # 每个阶段监控10分钟
        if ! monitor_deployment 10; then
            error "部署失败在${percentage}%，开始回滚"
            rollback_production
            return 1
        fi
    done
    
    success "生产环境部署成功"
}

# 健康检查
check_health() {
    local url=$1
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -sf "$url" > /dev/null; then
            return 0
        fi
        
        attempt=$((attempt + 1))
        sleep 2
    done
    
    return 1
}

# 监控部署
monitor_deployment() {
    local duration_minutes=$1
    local end_time=$(($(date +%s) + duration_minutes * 60))
    
    while [ $(date +%s) -lt $end_time ]; do
        # 检查错误率
        local error_rate=$(get_error_rate)
        if (( $(echo "$error_rate > 5" | bc -l) )); then
            error "错误率过高: ${error_rate}%"
            return 1
        fi
        
        # 检查响应时间
        local response_time=$(get_response_time_p95)
        if (( $(echo "$response_time > 1000" | bc -l) )); then
            error "响应时间过长: ${response_time}ms"
            return 1
        fi
        
        sleep 30
    done
    
    return 0
}

# 回滚
rollback_production() {
    log "执行生产环境回滚..."
    
    # 获取上一个稳定版本
    local previous_version=$(get_previous_stable_version)
    
    # 回滚到上一版本
    kubectl rollout undo deployment/financial-system -n production
    
    # 等待回滚完成
    kubectl rollout status deployment/financial-system -n production
    
    # 发送告警
    send_alert "生产环境已回滚到版本: $previous_version"
    
    success "回滚完成"
}

# 清理
cleanup() {
    log "执行清理..."
    
    # 清理测试数据
    rm -rf target/
    rm -rf coverage/
    
    # 清理Docker
    docker system prune -f
    
    success "清理完成"
}

# 生成报告
generate_report() {
    log "生成CI/CD报告..."
    
    local report_file="$PROJECT_ROOT/target/cicd-report-${VERSION}.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>CI/CD Report - ${VERSION}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .failure { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>CI/CD Pipeline Report</h1>
    <p><strong>Version:</strong> ${VERSION}</p>
    <p><strong>Branch:</strong> ${BRANCH}</p>
    <p><strong>Timestamp:</strong> $(date)</p>
    
    <h2>Pipeline Results</h2>
    <table>
        <tr>
            <th>Stage</th>
            <th>Status</th>
            <th>Duration</th>
            <th>Details</th>
        </tr>
        <!-- 动态生成的内容 -->
    </table>
    
    <h2>Code Quality Metrics</h2>
    <!-- 代码质量指标 -->
    
    <h2>Test Results</h2>
    <!-- 测试结果 -->
    
    <h2>Security Scan Results</h2>
    <!-- 安全扫描结果 -->
</body>
</html>
EOF
    
    success "报告已生成: $report_file"
}

# 主流程
main() {
    log "开始CI/CD流程 - 版本: $VERSION"
    
    # 检查环境
    check_requirements
    check_branch_policy
    
    # 根据分支类型执行不同流程
    case "$PIPELINE_TYPE" in
        feature)
            run_quality_checks
            run_unit_tests
            ;;
        development)
            run_quality_checks
            run_security_scan
            run_unit_tests
            run_integration_tests
            build_docker_images
            deploy_application development
            ;;
        production)
            run_quality_checks
            run_security_scan
            run_unit_tests
            run_integration_tests
            run_performance_tests
            build_docker_images
            deploy_application production
            ;;
        hotfix)
            run_unit_tests
            run_integration_tests
            build_docker_images
            deploy_application production
            ;;
        *)
            run_quality_checks
            run_unit_tests
            ;;
    esac
    
    # 生成报告
    generate_report
    
    # 清理
    cleanup
    
    success "CI/CD流程完成!"
}

# 执行主流程
main "$@"