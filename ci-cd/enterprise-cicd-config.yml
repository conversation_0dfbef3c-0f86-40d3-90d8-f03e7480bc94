# Enterprise CI/CD Configuration for FinancialSystem
# Version: 2.0
# Author: Professional CI/CD Architect

name: Enterprise CI/CD Pipeline

# 全局配置
global:
  project: FinancialSystem
  environments:
    - development
    - staging
    - production
  
  quality_gates:
    code_coverage: 80
    sonar_quality_gate: PASS
    security_severity: MEDIUM
    performance_threshold: 95

# 分支策略
branch_policies:
  main:
    protected: true
    require_pr: true
    require_reviews: 2
    dismiss_stale_reviews: true
    require_up_to_date: true
    
  develop:
    protected: true
    require_pr: true
    require_reviews: 1
    
  feature/*:
    auto_delete_after_merge: true
    
  hotfix/*:
    fast_forward_to_main: true
    require_emergency_approval: true

# 阶段定义
stages:
  - name: validation
    parallel: true
    jobs:
      - lint
      - type_check
      - unit_test
      - security_scan
      
  - name: build
    jobs:
      - compile
      - docker_build
      - artifact_store
      
  - name: quality
    jobs:
      - sonarqube_analysis
      - dependency_check
      - coverage_report
      
  - name: test
    parallel: true
    jobs:
      - integration_test
      - api_test
      - ui_test
      
  - name: performance
    jobs:
      - load_test
      - stress_test
      - benchmark
      
  - name: security
    jobs:
      - sast_scan
      - dast_scan
      - container_scan
      
  - name: deploy
    jobs:
      - deploy_strategy
      - health_check
      - smoke_test
      
  - name: release
    jobs:
      - version_tag
      - changelog
      - notification

# 作业定义
jobs:
  # 验证阶段
  lint:
    script:
      - npm run lint
      - mvn checkstyle:check
    fail_fast: true
    
  type_check:
    script:
      - npm run type:check
      - mvn compile
      
  unit_test:
    script:
      - npm run test:coverage
      - mvn test
    artifacts:
      - coverage/
      - target/surefire-reports/
      
  security_scan:
    script:
      - npm audit
      - mvn org.owasp:dependency-check-maven:check
      
  # 构建阶段
  compile:
    script:
      - mvn clean package -DskipTests
      - npm run build
    cache:
      - .m2/
      - node_modules/
      
  docker_build:
    script:
      - docker build -t $PROJECT_NAME:$VERSION .
      - docker tag $PROJECT_NAME:$VERSION $REGISTRY/$PROJECT_NAME:$VERSION
      - trivy image $PROJECT_NAME:$VERSION
      
  # 质量阶段
  sonarqube_analysis:
    script:
      - mvn sonar:sonar -Dsonar.projectKey=$PROJECT_NAME
    quality_gate:
      timeout: 300
      
  dependency_check:
    script:
      - mvn org.owasp:dependency-check-maven:aggregate
      - npm audit --production
      
  # 测试阶段
  integration_test:
    script:
      - mvn verify -Pintegration-test
    services:
      - mysql:8.0
      - redis:6.2
      
  api_test:
    script:
      - newman run postman_collection.json
      - ./scripts/api-test.sh
      
  ui_test:
    script:
      - npm run test:e2e
      - npm run test:visual
    browser:
      - chrome
      - firefox
      - safari
      
  # 性能阶段
  load_test:
    script:
      - k6 run load-test.js
      - jmeter -n -t test-plan.jmx
    thresholds:
      response_time_p95: 500ms
      error_rate: 0.1%
      
  # 部署阶段
  deploy_strategy:
    development:
      strategy: direct
      auto_deploy: true
      
    staging:
      strategy: blue_green
      manual_approval: false
      
    production:
      strategy: canary
      canary_percentage: 10
      canary_duration: 30m
      manual_approval: true
      rollback_on_failure: true
      
# 部署配置
deployments:
  development:
    trigger:
      - branch: develop
      - schedule: "0 2 * * *"  # 每天凌晨2点
    environment:
      url: https://dev.financial-system.com
      kubernetes_namespace: financial-dev
      
  staging:
    trigger:
      - branch: release/*
      - manual: true
    environment:
      url: https://staging.financial-system.com
      kubernetes_namespace: financial-staging
    pre_deploy:
      - backup_database
      - notify_team
      
  production:
    trigger:
      - branch: main
      - manual: true
    environment:
      url: https://financial-system.com
      kubernetes_namespace: financial-prod
    pre_deploy:
      - approval_required: ["tech-lead", "product-owner"]
      - backup_full_system
      - maintenance_mode: true
    post_deploy:
      - verify_deployment
      - update_monitoring
      - notify_stakeholders
      
# 监控和告警
monitoring:
  metrics:
    - response_time
    - error_rate
    - cpu_usage
    - memory_usage
    - database_connections
    
  alerts:
    - name: high_error_rate
      condition: error_rate > 5%
      duration: 5m
      severity: critical
      
    - name: slow_response
      condition: response_time_p95 > 1s
      duration: 10m
      severity: warning
      
  dashboards:
    - deployment_status
    - application_health
    - business_metrics
    
# 回滚策略
rollback:
  automatic_triggers:
    - health_check_failure
    - error_rate > 10%
    - response_time > 2s
    
  strategy:
    development: immediate
    staging: immediate
    production: gradual
    
  post_rollback:
    - create_incident_report
    - notify_on_call
    - preserve_logs
    
# 通知配置
notifications:
  channels:
    slack:
      webhook: ${SLACK_WEBHOOK}
      channels:
        - "#deployments"
        - "#alerts"
        
    email:
      smtp: ${SMTP_SERVER}
      recipients:
        - <EMAIL>
        - <EMAIL>
        
  events:
    - deployment_started
    - deployment_completed
    - deployment_failed
    - rollback_initiated
    - quality_gate_failed
    
# 安全配置
security:
  secrets_management:
    provider: vault
    path: /secret/financial-system
    
  scanning:
    sast:
      - sonarqube
      - checkmarx
    dast:
      - owasp_zap
      - burp_suite
    container:
      - trivy
      - anchore
      
  compliance:
    - pci_dss
    - gdpr
    - sox
    
# 文档和报告
reporting:
  deployment_report:
    format: html
    include:
      - test_results
      - coverage_report
      - security_scan
      - performance_metrics
      
  changelog:
    format: markdown
    auto_generate: true
    include_commits: true
    include_pr_links: true
    
  metrics_export:
    format: prometheus
    endpoint: /metrics
    retention: 90d