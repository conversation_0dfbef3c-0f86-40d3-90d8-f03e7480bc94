package com.laoshu198838.service;

import com.aspose.cells.*;
import com.laoshu198838.dto.report.OperationalDashboardDTO;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.repository.overdue_debt.DebtDisposalActionRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 经营调度会看板导出服务
 * 负责处理逾期债权数据导出到经营调度会看板的业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
public class ManagementBoardExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ManagementBoardExportService.class);
    
    @Autowired
    private OverdueDebtDetailRepository overdueDebtDetailRepository;
    
    @Autowired
    private DebtDisposalActionRepository debtDisposalActionRepository;
    
    @Autowired
    private DebtDetailsExportRepository debtDetailsExportRepository;
    
    /**
     * 导出经营调度会看板数据（统一调用入口）
     * 
     * @param year 年份
     * @param month 月份
     * @param minAmount 最小金额（万元）
     * @return Excel文件的字节数组响应
     */
    public ResponseEntity<byte[]> exportManagementBoard(String year, String month, String minAmount) {
        return exportManagementBoardData(year, month, minAmount);
    }
    
    /**
     * 导出经营调度会看板数据
     * 
     * @param year 年份
     * @param month 月份
     * @param minAmount 最小金额（万元）
     * @return Excel文件的字节数组响应
     */
    public ResponseEntity<byte[]> exportManagementBoardData(String year, String month, String minAmount) {
        try {
            logger.info("开始导出经营调度会看板数据 - 年份: {}, 月份: {}, 最小金额: {}万元", year, month, minAmount);
            
            // 1. 获取债权数据
            List<OperationalDashboardDTO> dashboardData = fetchDashboardData(year, month, minAmount);
            
            if (dashboardData.isEmpty()) {
                logger.warn("未找到符合条件的数据");
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"no_data.txt\"")
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("未找到符合条件的数据".getBytes(StandardCharsets.UTF_8));
            }
            
            // 2. 使用模板文件创建Excel工作簿
            Workbook workbook = loadTemplateWorkbook();
            Worksheet worksheet = workbook.getWorksheets().get(0);
            
            // 3. 填充数据到Excel
            fillExcelData(workbook, dashboardData, year, month);
            
            // 4. 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);
            byte[] excelData = byteArrayOutputStream.toByteArray();
            
            // 5. 生成文件名（优化编码处理）
            String filename = String.format("经营调度会看板_%s年%02d月.xlsx", year, Integer.parseInt(month));
            String encodedFilename = generateSafeFilename(filename);
            
            logger.info("成功导出经营调度会看板数据，记录数: {}", dashboardData.size());
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFilename)
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CACHE_CONTROL, "must-revalidate, post-check=0, pre-check=0")
                .header(HttpHeaders.PRAGMA, "public")
                .header(HttpHeaders.EXPIRES, "0")
                .body(excelData);
                
        } catch (Exception e) {
            logger.error("导出经营调度会看板数据失败", e);
            
            // 返回更友好的错误信息
            String errorMessage;
            if (e instanceof java.io.FileNotFoundException) {
                errorMessage = "Excel模板文件未找到，请联系管理员";
            } else if (e.getMessage() != null && e.getMessage().contains("format")) {
                errorMessage = "Excel格式处理失败，请稍后重试";
            } else {
                errorMessage = "导出失败: " + e.getMessage();
            }
            
            return ResponseEntity.internalServerError()
                .contentType(MediaType.TEXT_PLAIN)
                .body(errorMessage.getBytes(StandardCharsets.UTF_8));
        }
    }
    
    /**
     * 加载Excel模板文件
     */
    private Workbook loadTemplateWorkbook() throws Exception {
        try {
            // 从classpath加载模板文件
            Resource templateResource = new ClassPathResource("templates/经营调度会看板模板.xlsx");
            
            if (!templateResource.exists()) {
                logger.warn("模板文件不存在，使用动态创建的方式");
                return createDefaultWorkbook();
            }
            
            try (InputStream inputStream = templateResource.getInputStream()) {
                Workbook workbook = new Workbook(inputStream);
                logger.info("成功加载Excel模板文件");
                return workbook;
            }
            
        } catch (Exception e) {
            logger.error("加载模板文件失败，使用动态创建的方式", e);
            return createDefaultWorkbook();
        }
    }
    
    /**
     * 创建默认的工作簿（备用方案）
     */
    private Workbook createDefaultWorkbook() throws Exception {
        Workbook workbook = new Workbook();
        Worksheet worksheet = workbook.getWorksheets().get(0);
        worksheet.setName("经营调度会看板");
        
        // 设置基本的列宽
        for (int i = 0; i <= 10; i++) {
            worksheet.getCells().setColumnWidth(i, 15);
        }
        
        return workbook;
    }
    
    /**
     * 获取仪表板数据
     */
    private List<OperationalDashboardDTO> fetchDashboardData(String year, String month, String minAmount) {
        List<OperationalDashboardDTO> result = new ArrayList<>();
        
        try {
            // 转换最小金额为数值
            BigDecimal minAmountValue = new BigDecimal(minAmount).multiply(new BigDecimal("10000"));
            
            // 查询债权基础数据
            List<Map<String, Object>> debtList = overdueDebtDetailRepository.findDebtsByYearMonthAndMinAmount(
                year, month, minAmountValue
            );
            
            int sequenceNumber = 1;
            for (Map<String, Object> debt : debtList) {
                OperationalDashboardDTO dto = new OperationalDashboardDTO();
                dto.setSequenceNumber(sequenceNumber++);
                
                // 设置基础信息
                dto.setCreditor((String) debt.get("creditor"));
                dto.setDebtor((String) debt.get("debtor"));
                
                // 设置金额（转换为万元）
                BigDecimal debtAmount = toBigDecimal(debt.get("debt_amount"));
                BigDecimal debtBalance = toBigDecimal(debt.get("debt_balance"));
                BigDecimal cumulativeAmount = toBigDecimal(debt.get("cumulative_amount"));
                
                dto.setDebtAmount(debtAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                dto.setDebtBalance(debtBalance.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                
                // 如果有累计金额，使用累计金额
                if (cumulativeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    dto.setDebtAmount(cumulativeAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                }
                
                // 获取处置金额明细
                Long debtId = toLong(debt.get("debt_id"));
                if (debtId != null) {
                    Map<String, BigDecimal> disposalDetails = getDisposalDetails(debtId, year, month);
                    
                    // 设置处置金额（转换为万元）
                    dto.setDisposedPrincipal(disposalDetails.get("principal").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                    dto.setDisposedInterest(disposalDetails.get("interest").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                    dto.setDisposedPenalty(disposalDetails.get("penalty").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                    dto.setDisposedOther(disposalDetails.get("other").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                }
                
                // 计算合计和处置比例
                dto.calculateTotalDisposedAmount();
                dto.calculateDisposalRatio();
                
                result.add(dto);
            }
            
        } catch (Exception e) {
            logger.error("获取仪表板数据失败", e);
        }
        
        return result;
    }
    
    /**
     * 获取债权的处置金额明细
     */
    private Map<String, BigDecimal> getDisposalDetails(Long debtId, String year, String month) {
        // 查询处置记录
        List<Map<String, Object>> disposalActions = debtDisposalActionRepository.findByDebtIdAndYearMonth(
            debtId, year, month
        );
        
        BigDecimal principal = BigDecimal.ZERO;
        BigDecimal interest = BigDecimal.ZERO;
        BigDecimal penalty = BigDecimal.ZERO;
        BigDecimal other = BigDecimal.ZERO;
        
        for (Map<String, Object> action : disposalActions) {
            // 获取各种处置方式的金额
            BigDecimal cashDisposal = toBigDecimal(action.get("cash_disposal"));
            BigDecimal installmentRepayment = toBigDecimal(action.get("installment_repayment"));
            BigDecimal assetDebt = toBigDecimal(action.get("asset_debt"));
            BigDecimal otherWays = toBigDecimal(action.get("other_ways"));
            
            // 现金处置和分期还款主要用于本金回收
            principal = principal.add(cashDisposal).add(installmentRepayment);
            
            // 资产抵债按比例分配：70%本金，20%利息，10%违约金
            if (assetDebt.compareTo(BigDecimal.ZERO) > 0) {
                principal = principal.add(assetDebt.multiply(new BigDecimal("0.70")));
                interest = interest.add(assetDebt.multiply(new BigDecimal("0.20")));
                penalty = penalty.add(assetDebt.multiply(new BigDecimal("0.10")));
            }
            
            // 其他方式按比例分配
            if (otherWays.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, BigDecimal> splitAmounts = intelligentSplitAmount(debtId, otherWays);
                principal = principal.add(splitAmounts.get("principal"));
                interest = interest.add(splitAmounts.get("interest"));
                penalty = penalty.add(splitAmounts.get("penalty"));
                other = other.add(splitAmounts.get("other"));
            }
        }
        
        return Map.of(
            "principal", principal,
            "interest", interest,
            "penalty", penalty,
            "other", other
        );
    }
    
    /**
     * 智能拆分处置金额
     * 根据债权的历史数据和比例进行拆分
     */
    private Map<String, BigDecimal> intelligentSplitAmount(Long debtId, BigDecimal totalAmount) {
        // 默认拆分比例：本金70%，利息20%，违约金8%，其他2%
        BigDecimal principal = totalAmount.multiply(new BigDecimal("0.70"));
        BigDecimal interest = totalAmount.multiply(new BigDecimal("0.20"));
        BigDecimal penalty = totalAmount.multiply(new BigDecimal("0.08"));
        BigDecimal other = totalAmount.multiply(new BigDecimal("0.02"));
        
        // 可以根据实际业务规则调整拆分逻辑
        // 例如：查询历史拆分比例，或根据债权类型调整
        
        return Map.of(
            "principal", principal,
            "interest", interest,
            "penalty", penalty,
            "other", other
        );
    }
    
    /**
     * 填充Excel数据
     */
    private void fillExcelData(Workbook workbook, List<OperationalDashboardDTO> data, String year, String month) throws Exception {
        Worksheet worksheet = workbook.getWorksheets().get(0);
        Cells cells = worksheet.getCells();
        
        try {
            // 检查是否使用模板（模板可能已有标题和表头）
            boolean hasTemplate = checkIfTemplate(cells);
            int startRow = hasTemplate ? findDataStartRow(cells) : setupHeaders(cells, year, month);
            
            logger.info("开始填充数据，起始行: {}, 数据条数: {}", startRow, data.size());
            
            // 填充数据
            for (int i = 0; i < data.size(); i++) {
                int rowIndex = startRow + i;
                OperationalDashboardDTO dto = data.get(i);
                
                // 安全地设置单元格值，避免空指针异常
                setCellValueSafely(cells, rowIndex, 0, dto.getSequenceNumber());
                setCellValueSafely(cells, rowIndex, 1, dto.getCreditor());
                setCellValueSafely(cells, rowIndex, 2, dto.getDebtor());
                setCellValueSafely(cells, rowIndex, 3, dto.getDebtAmount());
                setCellValueSafely(cells, rowIndex, 4, dto.getDebtBalance());
                setCellValueSafely(cells, rowIndex, 5, dto.getDisposedPrincipal());
                setCellValueSafely(cells, rowIndex, 6, dto.getDisposedInterest());
                setCellValueSafely(cells, rowIndex, 7, dto.getDisposedPenalty());
                setCellValueSafely(cells, rowIndex, 8, dto.getDisposedOther());
                setCellValueSafely(cells, rowIndex, 9, dto.getTotalDisposedAmount());
                
                // 处置比例格式化
                String ratioText = String.format("%.2f%%", dto.getDisposalRatio().doubleValue());
                cells.get(rowIndex, 10).setValue(ratioText);
            }
            
            // 添加合计行（如果有数据）
            if (!data.isEmpty()) {
                addSummaryRow(cells, startRow, startRow + data.size() - 1, workbook);
            }
            
            // 自动调整列宽
            worksheet.autoFitColumns();
            
            logger.info("Excel数据填充完成");
            
        } catch (Exception e) {
            logger.error("填充Excel数据时出错", e);
            throw new RuntimeException("Excel数据填充失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查是否使用了模板
     */
    private boolean checkIfTemplate(Cells cells) {
        try {
            // 检查第一行或第二行是否有预设内容
            Cell firstCell = cells.get(0, 0);
            Cell secondRowCell = cells.get(1, 0);
            
            return (firstCell.getValue() != null && !firstCell.getValue().toString().trim().isEmpty()) ||
                   (secondRowCell.getValue() != null && !secondRowCell.getValue().toString().trim().isEmpty());
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 查找数据开始行
     */
    private int findDataStartRow(Cells cells) {
        // 在模板中查找数据开始的行，通常在表头之后
        for (int row = 0; row < 10; row++) {
            Cell cell = cells.get(row, 0);
            if (cell.getValue() == null || cell.getValue().toString().trim().isEmpty()) {
                // 找到第一个空行，假设这是数据开始行
                return Math.max(row, 2); // 至少从第3行开始
            }
        }
        return 2; // 默认从第3行开始
    }
    
    /**
     * 设置表头（非模板情况）
     */
    private int setupHeaders(Cells cells, String year, String month) throws Exception {
        // 设置标题
        cells.get(0, 0).setValue(String.format("经营调度会看板 - %s年%s月", year, month));
        
        // 创建表头
        String[] headers = {
            "序号", "债权人", "债务人", "债权金额（万元）", "债权余额（万元）",
            "处置本金（万元）", "处置利息（万元）", "处置违约金（万元）", "处置其他（万元）",
            "处置合计（万元）", "处置比例"
        };
        
        // 设置表头样式
        Style headerStyle = cells.get(1, 0).getStyle();
        headerStyle.getFont().setBold(true);
        headerStyle.setBackgroundColor(com.aspose.cells.Color.getLightGray());
        headerStyle.setForegroundColor(com.aspose.cells.Color.getLightGray());
        headerStyle.setPattern(BackgroundType.SOLID);
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = cells.get(1, i);
            cell.setValue(headers[i]);
            cell.setStyle(headerStyle);
        }
        
        return 2; // 数据从第3行开始
    }
    
    /**
     * 安全地设置单元格值
     */
    private void setCellValueSafely(Cells cells, int row, int col, Object value) {
        try {
            Cell cell = cells.get(row, col);
            if (value == null) {
                cell.setValue("");
            } else if (value instanceof BigDecimal) {
                cell.setValue(((BigDecimal) value).doubleValue());
            } else if (value instanceof Number) {
                cell.setValue(((Number) value).doubleValue());
            } else {
                cell.setValue(value.toString());
            }
        } catch (Exception e) {
            logger.warn("设置单元格值失败 [{}:{}]: {}", row, col, e.getMessage());
        }
    }
    
    /**
     * 添加合计行
     */
    private void addSummaryRow(Cells cells, int dataStartRow, int dataEndRow, Workbook workbook) throws Exception {
        int summaryRow = dataEndRow + 1;
        
        // 设置"合计"标签
        cells.get(summaryRow, 2).setValue("合计");
        
        // 设置合计公式（使用Excel的列标识符）
        String startRowNum = String.valueOf(dataStartRow + 1); // Excel行号从1开始
        String endRowNum = String.valueOf(dataEndRow + 1);
        
        cells.get(summaryRow, 3).setFormula(String.format("SUM(D%s:D%s)", startRowNum, endRowNum));
        cells.get(summaryRow, 4).setFormula(String.format("SUM(E%s:E%s)", startRowNum, endRowNum));
        cells.get(summaryRow, 5).setFormula(String.format("SUM(F%s:F%s)", startRowNum, endRowNum));
        cells.get(summaryRow, 6).setFormula(String.format("SUM(G%s:G%s)", startRowNum, endRowNum));
        cells.get(summaryRow, 7).setFormula(String.format("SUM(H%s:H%s)", startRowNum, endRowNum));
        cells.get(summaryRow, 8).setFormula(String.format("SUM(I%s:I%s)", startRowNum, endRowNum));
        cells.get(summaryRow, 9).setFormula(String.format("SUM(J%s:J%s)", startRowNum, endRowNum));
        
        // 合计行样式
        Style summaryStyle = workbook.createStyle();
        summaryStyle.getFont().setBold(true);
        summaryStyle.setBackgroundColor(com.aspose.cells.Color.getLightYellow());
        summaryStyle.setForegroundColor(com.aspose.cells.Color.getLightYellow());
        summaryStyle.setPattern(BackgroundType.SOLID);
        
        for (int colIndex = 0; colIndex <= 10; colIndex++) {
            cells.get(summaryRow, colIndex).setStyle(summaryStyle);
        }
    }
    
    /**
     * 生成安全的文件名
     */
    private String generateSafeFilename(String originalFilename) {
        try {
            // 方法1：URL编码（推荐）
            String encoded = URLEncoder.encode(originalFilename, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");
            
            // 方法2：如果浏览器不支持中文，提供备用英文名
            String fallbackName = originalFilename
                .replaceAll("[^\\w\\.\\-]", "_")  // 替换特殊字符
                .replaceAll("_{2,}", "_");       // 合并多个下划线
            
            logger.debug("文件名编码: 原始={}, 编码={}, 备用={}", originalFilename, encoded, fallbackName);
            
            return encoded;
            
        } catch (Exception e) {
            logger.warn("文件名编码失败，使用备用方案: {}", e.getMessage());
            // 备用方案：生成简单的英文文件名
            LocalDateTime now = LocalDateTime.now();
            return String.format("ManagementBoard_%s.xlsx", 
                now.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        }
    }
    
    // 工具方法
    private BigDecimal toBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        return BigDecimal.ZERO;
    }
    
    private Long toLong(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }
}