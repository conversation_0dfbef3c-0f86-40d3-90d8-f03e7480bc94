package com.laoshu198838.service;

import com.laoshu198838.dto.asset.AssetStatusDTO;
import com.laoshu198838.entity.asset.AssetStatus;
import com.laoshu198838.repository.asset.AssetStatusRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 资产状态服务类
 * 
 * <AUTHOR>
 */
@Service
@Transactional
public class AssetStatusService {

    private static final Logger logger = LoggerFactory.getLogger(AssetStatusService.class);

    @Autowired
    private AssetStatusRepository assetStatusRepository;

    /**
     * 创建或更新资产状态
     */
    public AssetStatusDTO saveAssetStatus(AssetStatusDTO dto, String currentUser) {
        logger.info("保存资产状态: 资产ID={}, 年月={}-{}, 操作人={}", 
            dto.getAssetId(), dto.getStatusYear(), dto.getStatusMonth(), currentUser);
        
        // 验证面积数据
        validateAreaData(dto);
        
        // 查找是否已存在该年月的状态记录
        Optional<AssetStatus> existingOpt = assetStatusRepository.findByAssetIdAndStatusYearAndStatusMonth(
            dto.getAssetId(), dto.getStatusYear(), dto.getStatusMonth());
        
        AssetStatus entity;
        if (existingOpt.isPresent()) {
            // 更新现有记录
            entity = existingOpt.get();
            entity.setTotalArea(dto.getTotalArea());
            entity.setSelfUseArea(dto.getSelfUseArea());
            entity.setRentalArea(dto.getRentalArea());
            entity.setRemark(dto.getRemark());
            entity.setUpdatedBy(currentUser);
            logger.info("更新现有资产状态记录，ID: {}", entity.getId());
        } else {
            // 创建新记录
            entity = dto.toEntity();
            entity.setCreatedBy(currentUser);
            entity.setUpdatedBy(currentUser);
            logger.info("创建新的资产状态记录");
        }
        
        AssetStatus saved = assetStatusRepository.save(entity);
        logger.info("资产状态保存成功，ID: {}", saved.getId());
        
        return AssetStatusDTO.fromEntity(saved);
    }

    /**
     * 验证面积数据
     */
    private void validateAreaData(AssetStatusDTO dto) {
        BigDecimal totalArea = dto.getTotalArea();
        BigDecimal selfUseArea = dto.getSelfUseArea() != null ? dto.getSelfUseArea() : BigDecimal.ZERO;
        BigDecimal rentalArea = dto.getRentalArea() != null ? dto.getRentalArea() : BigDecimal.ZERO;
        
        if (totalArea == null || totalArea.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("总面积必须大于0");
        }
        
        if (selfUseArea.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("自用面积不能为负数");
        }
        
        if (rentalArea.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("出租面积不能为负数");
        }
        
        if (selfUseArea.add(rentalArea).compareTo(totalArea) > 0) {
            throw new RuntimeException("自用面积和出租面积之和不能超过总面积");
        }
    }

    /**
     * 根据资产ID和年月查询状态
     */
    @Transactional(readOnly = true)
    public AssetStatusDTO getAssetStatus(Long assetId, Integer year, Integer month) {
        Optional<AssetStatus> entityOpt = assetStatusRepository.findByAssetIdAndStatusYearAndStatusMonth(
            assetId, year, month);
        return entityOpt.map(AssetStatusDTO::fromEntity).orElse(null);
    }

    /**
     * 根据资产ID查询所有状态记录
     */
    @Transactional(readOnly = true)
    public List<AssetStatusDTO> getAssetStatusHistory(Long assetId) {
        List<AssetStatus> entities = assetStatusRepository.findByAssetIdOrderByStatusYearDescStatusMonthDesc(assetId);
        return entities.stream()
                .map(AssetStatusDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取资产的最新状态
     */
    @Transactional(readOnly = true)
    public AssetStatusDTO getLatestAssetStatus(Long assetId) {
        Optional<AssetStatus> entityOpt = assetStatusRepository.findLatestStatusByAssetId(assetId);
        return entityOpt.map(AssetStatusDTO::fromEntity).orElse(null);
    }

    /**
     * 根据管理公司和年月查询状态列表
     */
    @Transactional(readOnly = true)
    public List<AssetStatusDTO> getAssetStatusByYearMonth(String managementCompany, Integer year, Integer month) {
        List<AssetStatus> entities = assetStatusRepository.findByManagementCompanyAndStatusYearAndStatusMonth(
            managementCompany, year, month);
        return entities.stream()
                .map(AssetStatusDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 分页查询资产状态
     */
    @Transactional(readOnly = true)
    public Page<AssetStatusDTO> getAssetStatusByPage(String managementCompany, Integer year, Integer month, Pageable pageable) {
        Page<AssetStatus> entities = assetStatusRepository.findByManagementCompanyAndStatusYearAndStatusMonth(
            managementCompany, year, month, pageable);
        return entities.map(AssetStatusDTO::fromEntity);
    }

    /**
     * 获取面积分布统计（用于圆饼图）
     */
    @Transactional(readOnly = true)
    public Map<String, BigDecimal> getAreaDistribution(String managementCompany, Integer year, Integer month) {
        List<Object[]> results = assetStatusRepository.getAreaDistributionByYearMonth(managementCompany, year, month);
        
        if (results.isEmpty() || results.get(0)[0] == null) {
            return Map.of(
                "totalArea", BigDecimal.ZERO,
                "selfUseArea", BigDecimal.ZERO,
                "rentalArea", BigDecimal.ZERO,
                "idleArea", BigDecimal.ZERO
            );
        }
        
        Object[] result = results.get(0);
        return Map.of(
            "totalArea", (BigDecimal) result[0],
            "selfUseArea", (BigDecimal) result[1],
            "rentalArea", (BigDecimal) result[2],
            "idleArea", (BigDecimal) result[3]
        );
    }

    /**
     * 获取当前月份的面积分布
     */
    @Transactional(readOnly = true)
    public Map<String, BigDecimal> getCurrentMonthAreaDistribution(String managementCompany) {
        LocalDate now = LocalDate.now();
        return getAreaDistribution(managementCompany, now.getYear(), now.getMonthValue());
    }

    /**
     * 获取各管理公司的盘活统计（用于柱形图）
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getActivationStatistics(Integer year, Integer month) {
        List<Object[]> results = assetStatusRepository.getActivationStatsByYearMonth(year, month);
        
        return results.stream().map(row -> Map.of(
            "managementCompany", row[0],
            "totalArea", row[1],
            "activatedArea", row[2],
            "activationRate", row[3]
        )).collect(Collectors.toList());
    }

    /**
     * 获取当前月份的盘活统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getCurrentMonthActivationStatistics() {
        LocalDate now = LocalDate.now();
        return getActivationStatistics(now.getYear(), now.getMonthValue());
    }

    /**
     * 获取待盘活资产（闲置面积大于0的资产）
     */
    @Transactional(readOnly = true)
    public List<AssetStatusDTO> getIdleAssets(String managementCompany, Integer year, Integer month) {
        List<AssetStatus> entities = assetStatusRepository.findIdleAssetsByYearMonth(managementCompany, year, month);
        return entities.stream()
                .map(AssetStatusDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取有状态记录的年月列表
     */
    @Transactional(readOnly = true)
    public List<Map<String, Integer>> getAvailableYearMonths(String managementCompany) {
        List<Object[]> results = assetStatusRepository.findDistinctYearMonthByManagementCompany(managementCompany);
        
        return results.stream().map(row -> Map.of(
            "year", (Integer) row[0],
            "month", (Integer) row[1]
        )).collect(Collectors.toList());
    }

    /**
     * 删除资产状态记录
     */
    public void deleteAssetStatus(Long assetId, Integer year, Integer month, String currentUser) {
        logger.info("删除资产状态: 资产ID={}, 年月={}-{}, 操作人={}", assetId, year, month, currentUser);
        
        Optional<AssetStatus> entityOpt = assetStatusRepository.findByAssetIdAndStatusYearAndStatusMonth(
            assetId, year, month);
        
        if (entityOpt.isPresent()) {
            assetStatusRepository.delete(entityOpt.get());
            logger.info("资产状态删除成功");
        } else {
            throw new RuntimeException("资产状态记录不存在");
        }
    }
}
