package com.laoshu198838.service;

import com.laoshu198838.dto.asset.RentalInfoDTO;
import com.laoshu198838.entity.asset.RentalInfo;
import com.laoshu198838.repository.asset.RentalInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 出租信息服务类
 * 
 * <AUTHOR>
 */
@Service
@Transactional
public class RentalInfoService {

    private static final Logger logger = LoggerFactory.getLogger(RentalInfoService.class);

    @Autowired
    private RentalInfoRepository rentalInfoRepository;

    /**
     * 创建出租信息
     */
    public RentalInfoDTO createRentalInfo(RentalInfoDTO dto, String currentUser) {
        logger.info("创建出租信息: 资产ID={}, 承租人={}, 操作人={}", 
            dto.getAssetId(), dto.getLessee(), currentUser);
        
        // 验证出租信息
        validateRentalInfo(dto);
        
        // 验证合同编号是否重复
        if (dto.getContractNo() != null && 
            rentalInfoRepository.existsByContractNoAndManagementCompanyAndIdNot(
                dto.getContractNo(), dto.getManagementCompany(), 0L)) {
            throw new RuntimeException("合同编号已存在: " + dto.getContractNo());
        }

        RentalInfo entity = dto.toEntity();
        entity.setCreatedBy(currentUser);
        entity.setUpdatedBy(currentUser);
        
        RentalInfo saved = rentalInfoRepository.save(entity);
        logger.info("出租信息创建成功，ID: {}", saved.getId());
        
        return RentalInfoDTO.fromEntity(saved);
    }

    /**
     * 更新出租信息
     */
    public RentalInfoDTO updateRentalInfo(Long id, RentalInfoDTO dto, String currentUser) {
        logger.info("更新出租信息: {}, 操作人: {}", id, currentUser);
        
        Optional<RentalInfo> existingOpt = rentalInfoRepository.findById(id);
        if (existingOpt.isEmpty()) {
            throw new RuntimeException("出租信息不存在，ID: " + id);
        }
        
        // 验证出租信息
        validateRentalInfo(dto);
        
        // 验证合同编号是否重复
        if (dto.getContractNo() != null && 
            rentalInfoRepository.existsByContractNoAndManagementCompanyAndIdNot(
                dto.getContractNo(), dto.getManagementCompany(), id)) {
            throw new RuntimeException("合同编号已存在: " + dto.getContractNo());
        }

        RentalInfo entity = dto.toEntity();
        entity.setId(id);
        entity.setUpdatedBy(currentUser);
        
        RentalInfo saved = rentalInfoRepository.save(entity);
        logger.info("出租信息更新成功，ID: {}", saved.getId());
        
        return RentalInfoDTO.fromEntity(saved);
    }

    /**
     * 验证出租信息
     */
    private void validateRentalInfo(RentalInfoDTO dto) {
        if (dto.getRentalStartDate() == null) {
            throw new RuntimeException("出租起始日期不能为空");
        }
        
        if (dto.getRentalEndDate() == null) {
            throw new RuntimeException("出租截止日期不能为空");
        }
        
        if (dto.getRentalEndDate().isBefore(dto.getRentalStartDate())) {
            throw new RuntimeException("出租截止日期不能早于起始日期");
        }
        
        if (dto.getRentalArea() == null || dto.getRentalArea().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("出租面积必须大于0");
        }
        
        if (dto.getMonthlyRent() == null || dto.getMonthlyRent().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("月租金必须大于0");
        }
    }

    /**
     * 删除出租信息
     */
    public void deleteRentalInfo(Long id, String currentUser) {
        logger.info("删除出租信息: {}, 操作人: {}", id, currentUser);
        
        Optional<RentalInfo> existingOpt = rentalInfoRepository.findById(id);
        if (existingOpt.isEmpty()) {
            throw new RuntimeException("出租信息不存在，ID: " + id);
        }
        
        rentalInfoRepository.deleteById(id);
        logger.info("出租信息删除成功，ID: {}", id);
    }

    /**
     * 根据ID查询出租信息
     */
    @Transactional(readOnly = true)
    public RentalInfoDTO getRentalInfoById(Long id) {
        Optional<RentalInfo> entityOpt = rentalInfoRepository.findById(id);
        return entityOpt.map(RentalInfoDTO::fromEntity).orElse(null);
    }

    /**
     * 根据资产ID查询出租信息
     */
    @Transactional(readOnly = true)
    public List<RentalInfoDTO> getRentalInfoByAssetId(Long assetId) {
        List<RentalInfo> entities = rentalInfoRepository.findByAssetIdOrderByRentalStartDateDesc(assetId);
        return entities.stream()
                .map(RentalInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 根据资产ID和合同状态查询出租信息
     */
    @Transactional(readOnly = true)
    public List<RentalInfoDTO> getRentalInfoByAssetIdAndStatus(Long assetId, RentalInfo.ContractStatus status) {
        List<RentalInfo> entities = rentalInfoRepository.findByAssetIdAndContractStatusOrderByRentalStartDateDesc(
            assetId, status);
        return entities.stream()
                .map(RentalInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 根据管理公司查询出租信息
     */
    @Transactional(readOnly = true)
    public List<RentalInfoDTO> getRentalInfoByManagementCompany(String managementCompany) {
        List<RentalInfo> entities = rentalInfoRepository.findByManagementCompanyOrderByRentalStartDateDesc(managementCompany);
        return entities.stream()
                .map(RentalInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 分页查询出租信息
     */
    @Transactional(readOnly = true)
    public Page<RentalInfoDTO> getRentalInfoByPage(String managementCompany, Pageable pageable) {
        Page<RentalInfo> entities = rentalInfoRepository.findByManagementCompanyOrderByRentalStartDateDesc(
            managementCompany, pageable);
        return entities.map(RentalInfoDTO::fromEntity);
    }

    /**
     * 根据条件查询出租信息
     */
    @Transactional(readOnly = true)
    public Page<RentalInfoDTO> getRentalInfoByConditions(
            String managementCompany,
            Long assetId,
            RentalInfo.ContractStatus contractStatus,
            String lessee,
            Pageable pageable) {
        
        Page<RentalInfo> entities = rentalInfoRepository.findByConditions(
            managementCompany, assetId, contractStatus, lessee, pageable);
        return entities.map(RentalInfoDTO::fromEntity);
    }

    /**
     * 查询即将到期的合同
     */
    @Transactional(readOnly = true)
    public List<RentalInfoDTO> getExpiringContracts(String managementCompany) {
        List<RentalInfo> entities = rentalInfoRepository.findByIsExpiringSoonTrueAndManagementCompanyOrderByRentalEndDate(
            managementCompany);
        return entities.stream()
                .map(RentalInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 查询指定日期范围内到期的合同
     */
    @Transactional(readOnly = true)
    public List<RentalInfoDTO> getContractsExpiringInRange(
            String managementCompany, LocalDate startDate, LocalDate endDate) {
        List<RentalInfo> entities = rentalInfoRepository.findByRentalEndDateBetweenAndManagementCompanyAndContractStatus(
            startDate, endDate, managementCompany, RentalInfo.ContractStatus.ACTIVE);
        return entities.stream()
                .map(RentalInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定资产的有效出租面积总和
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalRentalAreaByAssetId(Long assetId) {
        return rentalInfoRepository.getTotalRentalAreaByAssetId(assetId, RentalInfo.ContractStatus.ACTIVE);
    }

    /**
     * 获取指定资产的有效月租金总和
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalMonthlyRentByAssetId(Long assetId) {
        return rentalInfoRepository.getTotalMonthlyRentByAssetId(assetId, RentalInfo.ContractStatus.ACTIVE);
    }

    /**
     * 获取租金收入统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getRentalStatistics() {
        List<Object[]> results = rentalInfoRepository.getRentalStatsByManagementCompany(RentalInfo.ContractStatus.ACTIVE);
        
        return results.stream().map(row -> Map.of(
            "managementCompany", row[0],
            "contractCount", row[1],
            "totalRentalArea", row[2],
            "totalMonthlyRent", row[3]
        )).collect(Collectors.toList());
    }

    /**
     * 获取当前有效的出租合同
     */
    @Transactional(readOnly = true)
    public List<RentalInfoDTO> getCurrentActiveRentals(Long assetId) {
        List<RentalInfo> entities = rentalInfoRepository.findCurrentActiveRentalsByAssetId(
            assetId, RentalInfo.ContractStatus.ACTIVE);
        return entities.stream()
                .map(RentalInfoDTO::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取出租人列表
     */
    @Transactional(readOnly = true)
    public List<String> getLessors(String managementCompany) {
        return rentalInfoRepository.findDistinctLessorsByManagementCompany(managementCompany);
    }

    /**
     * 获取承租人列表
     */
    @Transactional(readOnly = true)
    public List<String> getLessees(String managementCompany) {
        return rentalInfoRepository.findDistinctLesseesByManagementCompany(managementCompany);
    }
}
