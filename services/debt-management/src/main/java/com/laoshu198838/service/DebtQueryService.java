package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 债权查询服务
 * 专门负责债权相关的查询操作
 * 
 * <AUTHOR>
 */
@Service
public class DebtQueryService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtQueryService.class);
    
    @Autowired
    private OverdueDebtAddService overdueDebtAddService;
    
    @Autowired
    private OverdueDebtDecreaseService overdueDebtDecreaseService;
    
    /**
     * 根据债权人和债务人查询债权记录
     */
    public List<Map<String, Object>> findDebtRecordsByCreditorAndDebtor(String creditor, String debtor) {
        logger.info("查询债权记录: 债权人={}, 债务人={}", creditor, debtor);
        return overdueDebtAddService.findDebtRecordsByCreditorAndDebtor(creditor, debtor);
    }
    
    /**
     * 获取当年所有新增债权记录
     */
    public List<Map<String, Object>> getAllAddRecordsByYear(int year) {
        logger.info("获取{}年所有新增债权记录", year);
        return overdueDebtAddService.findAddDebtorInfoByYear(year);
    }
    
    /**
     * 获取当年所有债权处置记录
     */
    public List<Map<String, Object>> getAllDisposalRecordsByYear(int year) {
        logger.info("查询{}年的所有债权处置记录", year);
        return overdueDebtDecreaseService.findDecreaseDebtorInfoByYear(year);
    }
    
    /**
     * 根据债权人和债务人查询债权处置记录
     */
    public List<Map<String, Object>> findDebtorInfoByCreditorAndDebtor(String creditor, String debtor) {
        logger.info("查询债权处置记录: 债权人={}, 债务人={}", creditor, debtor);
        return overdueDebtDecreaseService.findDebtorInfoByCreditorAndDebtor(creditor, debtor);
    }
}