package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.model.overduedebt.dto.entity.OverdueDebtAddDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 债权新增服务
 * 专门负责债权新增相关的业务操作
 * 
 * <AUTHOR>
 */
@Service
public class DebtAdditionService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtAdditionService.class);
    
    @Autowired
    private OverdueDebtAddService overdueDebtAddService;
    
    @Autowired
    private RefactoredOverdueDebtAddService refactoredOverdueDebtAddService;
    
    /**
     * 新增逾期债权 - 原有版本
     */
    @Transactional
    public OverdueDebtAdd addOverdueDebt(OverdueDebtAddDTO dto) {
        logger.info("新增逾期债权: {}", dto);
        return overdueDebtAddService.addOverdueDebt(dto);
    }
    
    /**
     * 新增逾期债权 - 重构版本
     */
    @Transactional
    public OverdueDebtAdd addOverdueDebtRefactored(com.laoshu198838.dto.debt.entity.OverdueDebtAddDTO dto) {
        logger.info("新增逾期债权(重构版本): {}", dto);
        return refactoredOverdueDebtAddService.addOverdueDebt(dto);
    }
}