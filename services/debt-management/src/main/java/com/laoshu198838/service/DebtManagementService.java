package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;
import com.laoshu198838.model.overduedebt.dto.entity.OverdueDebtAddDTO;
import com.laoshu198838.model.overduedebt.dto.query.DebtStatisticsDTO;
import com.laoshu198838.model.overduedebt.dto.entity.DebtStatisticsDetailDTO;
import com.laoshu198838.model.datamonitor.dto.*;
import com.laoshu198838.dto.debt.DebtDeletionDTO;
import com.laoshu198838.dto.debt.DebtDeletionResult;
import com.laoshu198838.dto.CompanyCollectionProgressDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.util.List;
import java.util.Map;

/**
 * 债权管理统一服务类
 * 整合所有债权相关的业务操作
 *
 * <AUTHOR>
 */
@Service
public class DebtManagementService implements BusinessService<OverdueDebtAdd, String> {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtManagementService.class);
    
    @Autowired
    private OverdueDebtAddService overdueDebtAddService;

    @Autowired
    private OverdueDebtDecreaseService overdueDebtDecreaseService;

    // 新增：重构后的Service类
    @Autowired
    private RefactoredOverdueDebtAddService refactoredOverdueDebtAddService;

    @Autowired
    private RefactoredOverdueDebtDecreaseService refactoredOverdueDebtDecreaseService;
    
    @Autowired
    private OverdueDebtService overdueDebtService;
    
    @Autowired
    private DataConsistencyCheckService dataConsistencyCheckService;
    
    @Autowired
    private NonLitigationUpdateService nonLitigationUpdateService;
    
    @Autowired
    private ExcelExportService excelExportService;
    
    @Autowired(required = false)
    private DebtDeletionService debtDeletionService;
    
    // ==================== 债权新增相关 ====================

    /**
     * 新增逾期债权（原有方法，保持兼容性）
     */
    @Transactional
    public OverdueDebtAdd addOverdueDebt(OverdueDebtAddDTO dto) {
        logger.info("开始新增逾期债权: 债权人={}, 债务人={}", dto.getCreditor(), dto.getDebtor());
        try {
            return overdueDebtAddService.addOverdueDebt(dto);
        } catch (Exception e) {
            logger.error("新增逾期债权失败", e);
            throw new RuntimeException("新增逾期债权失败: " + e.getMessage(), e);
        }
    }

    /**
     * 新增逾期债权（重构后方法，使用新的业务逻辑）
     * 注意：这个方法使用重构后的Service，提供更强的业务规则验证和数据一致性检查
     */
    @Transactional
    public OverdueDebtAdd addOverdueDebtRefactored(OverdueDebtAddDTO dto) {
        logger.info("开始新增逾期债权（重构版本）: 债权人={}, 债务人={}", dto.getCreditor(), dto.getDebtor());
        try {
            // 转换DTO类型
            com.laoshu198838.dto.debt.entity.OverdueDebtAddDTO refactoredDto = convertToRefactoredDto(dto);
            return refactoredOverdueDebtAddService.addOverdueDebt(refactoredDto);
        } catch (Exception e) {
            logger.error("新增逾期债权失败（重构版本）", e);
            throw new RuntimeException("新增逾期债权失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据债权人和债务人查询债权记录
     */
    public List<Map<String, Object>> findDebtRecordsByCreditorAndDebtor(String creditor, String debtor) {
        return overdueDebtAddService.findDebtRecordsByCreditorAndDebtor(creditor, debtor);
    }

    /**
     * 获取当年所有新增债权记录
     */
    public List<Map<String, Object>> getAllAddRecordsByYear(int year) {
        logger.info("开始查询{}年的所有新增债权记录", year);
        return overdueDebtAddService.findAddDebtorInfoByYear(year);
    }
    
    // ==================== 债权处置相关 ====================

    /**
     * 更新债权处置数据（原有方法，保持兼容性）
     */
    @Transactional
    public ResponseEntity<?> updateDebtReductionData(Map<String, Object> inputData) {
        logger.info("开始更新债权处置数据");
        try {
            
            // 验证关键字段
            if (inputData.get("creditor") == null || inputData.get("creditor").toString().trim().isEmpty()) {
                logger.error("债权人字段缺失或为空");
                return ResponseEntity.badRequest().body("债权人不能为空");
            }
            if (inputData.get("debtor") == null || inputData.get("debtor").toString().trim().isEmpty()) {
                logger.error("债务人字段缺失或为空");
                return ResponseEntity.badRequest().body("债务人不能为空");
            }
            if (inputData.get("yearMonth") == null || inputData.get("yearMonth").toString().trim().isEmpty()) {
                logger.error("年月字段缺失或为空");
                return ResponseEntity.badRequest().body("年月不能为空");
            }
            
            return overdueDebtDecreaseService.updateDebtReductionData(inputData);
        } catch (Exception e) {
            logger.error("更新债权处置数据失败", e);
            throw new RuntimeException("更新债权处置数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处置债权（重构后方法，使用新的业务逻辑）
     */
    @Transactional
    public OverdueDebtDecrease disposeDebtRefactored(Map<String, Object> frontendData) {
        logger.info("开始处置债权（重构版本）");
        try {
            return refactoredOverdueDebtDecreaseService.disposeDebt(frontendData);
        } catch (Exception e) {
            logger.error("处置债权失败（重构版本）", e);
            throw new RuntimeException("处置债权失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当年所有债权处置记录
     */
    public List<Map<String, Object>> getAllDisposalRecordsByYear(int year) {
        logger.info("开始查询{}年的所有债权处置记录", year);
        return overdueDebtDecreaseService.findDecreaseDebtorInfoByYear(year);
    }

    /**
     * 根据债权人和债务人查询债权处置记录
     */
    public List<Map<String, Object>> findDebtorInfoByCreditorAndDebtor(String creditor, String debtor) {
        logger.info("开始查询债权处置记录: 债权人={}, 债务人={}", creditor, debtor);
        return overdueDebtDecreaseService.findDebtorInfoByCreditorAndDebtor(creditor, debtor);
    }

    /**
     * 真正删除处置记录
     * 物理删除处置表记录，并反向更新相关表数据
     */
    @Transactional
    public ResponseEntity<?> deleteDisposalRecord(Map<String, Object> deleteData) {
        logger.info("开始删除处置记录: {}", deleteData);
        try {
            return overdueDebtDecreaseService.deleteDisposalRecord(deleteData);
        } catch (Exception e) {
            logger.error("删除处置记录失败", e);
            throw new RuntimeException("删除处置记录失败: " + e.getMessage(), e);
        }
    }
    
    // ==================== 债权统计相关 ====================
    
    /**
     * 获取债权统计数据
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDTO getDebtStatistics(String year, String month, String company) {
        return overdueDebtService.getDebtStatistics(year, month, company);
    }

    /**
     * 获取债权统计详细数据
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDetailDTO getDebtStatisticsDetail(String year, String month, String company) {
        return overdueDebtService.getDebtStatisticsDetail(year, month, company);
    }
    

    /**
     * 获取存量债权清收情况统计
     */
    public Map<String, Object> getDebtCollectionStatus(String year, String month, String company) {
        logger.info("获取存量债权清收情况统计: year={}, month={}, company={}", year, month, company);
        return overdueDebtService.getDebtCollectionStatus(year, month, company);
    }
    
    /**
     * 获取清收处置方式统计
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getDebtDisposalMethods(String year, String month, String company) {
        return overdueDebtService.getDebtDisposalMethods(year, month, company);
    }
    
    /**
     * 获取各子公司回收完成情况
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getCompanyRecoveryProgress(String year, String month) {
        return overdueDebtService.getCompanyRecoveryProgress(year, month);
    }
    
    // ==================== 新增债权统计相关 ====================
    
    /**
     * 获取新增债权情况统计（月度、年度）
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Map<String, Object> getNewDebtTrendStatistics(String year, String month, String company) {
        return overdueDebtService.getNewDebtTrendStatistics(year, month, company);
    }
    
    /**
     * 获取各单位新增债权余额统计
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtBalanceByCompany(String year, String month, String company) {
        return overdueDebtService.getNewDebtBalanceByCompany(year, month, company);
    }
    
    /**
     * 获取各子公司新增债权回收完成情况对比
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtRecoveryComparison(String year, String month) {
        return overdueDebtService.getNewDebtRecoveryComparison(year, month);
    }
    
    /**
     * 获取新增债权处置方式统计
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtDisposalMethodStatistics(String year, String month, String company) {
        return overdueDebtService.getNewDebtDisposalMethodStatistics(year, month, company);
    }
    
    /**
     * 获取各子公司年度清收目标完成情况
     * 注意：此方法不使用事务，避免autoCommit冲突
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<CompanyCollectionProgressDTO> getCompanyCollectionProgress(String year, String month, String displayMode) {
        logger.info("获取各子公司年度清收目标完成情况: year={}, month={}, displayMode={}", year, month, displayMode);
        return overdueDebtService.getCompanyCollectionProgress(year, month, displayMode);
    }

    
    // ==================== 数据一致性检查相关 ====================

    /**
     * 检查新增表数据一致性
     */
    public AddTableConsistencyResult checkAddTableConsistency(String year, String month) {
        logger.info("开始检查新增表数据一致性: year={}, month={}", year, month);
        return dataConsistencyCheckService.checkAddTableConsistency(year, month);
    }

    /**
     * 检查处置表数据一致性
     */
    public DisposalTableConsistencyResult checkDisposalTableConsistency(String year, String month) {
        logger.info("开始检查处置表数据一致性: year={}, month={}", year, month);
        return dataConsistencyCheckService.checkDisposalTableConsistency(year, month);
    }

    /**
     * 检查债务变动一致性
     */
    public DebtChangeConsistencyResult checkDebtChangeConsistency(String year, String month) {
        logger.info("开始检查债务变动一致性: year={}, month={}", year, month);
        return dataConsistencyCheckService.checkDebtChangeConsistency(year, month);
    }

    /**
     * 获取数据总览
     */
    public DataOverviewResult getDataOverview(String year, String month) {
        logger.info("开始获取数据总览: year={}, month={}", year, month);
        return dataConsistencyCheckService.getDataOverview(year, month);
    }

    /**
     * 获取一致性检查明细
     */
    public List<ConsistencyDetailItem> getConsistencyDetail(String checkKey, String yearMonth) {
        logger.info("开始获取一致性检查明细: checkKey={}, yearMonth={}", checkKey, yearMonth);
        return dataConsistencyCheckService.getConsistencyDetail(checkKey, yearMonth);
    }
    
    // ==================== 非诉讼更新相关 ====================
    
    /**
     * 记录非诉讼表状态
     */
    public void logNonLitigationTableStatus() {
        nonLitigationUpdateService.logNonLitigationTableStatus();
    }
    
    // ==================== Excel导出相关 ====================
    
    /**
     * 导出新增债权明细
     */
    public ResponseEntity<byte[]> exportNewDebtDetails(String year, String month, String company) {
        return excelExportService.exportNewDebtDetails(year, month, company);
    }
    
    /**
     * 导出处置债权明细
     */
    public ResponseEntity<byte[]> exportReductionDebtDetails(String year, String month, String company) {
        return excelExportService.exportReductionDebtDetails(year, month, company);
    }
    
    /**
     * 导出完整的逾期债权清收统计表
     * 包含8个子表的完整报表，支持年份、月份和金额限制参数
     */
    public ResponseEntity<byte[]> exportCompleteOverdueReport(String year, String month, String amount) {
        return excelExportService.exportCompleteOverdueReport(year, month, amount);
    }
    
    /**
     * 导出经营调度会看板数据
     * 包含债权处置情况和管理看板数据
     */
    public ResponseEntity<byte[]> exportManagementBoard(String year, String month) {
        logger.info("开始导出经营调度会看板数据: year={}, month={}", year, month);

        // 调用Excel导出服务
        try {
            return excelExportService.exportManagementBoard(year, month);
        } catch (Exception e) {
            logger.error("导出经营调度会看板数据失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .header("Content-Type", "application/json")
                .body(String.format("{\"error\": \"导出失败: %s\"}", e.getMessage()).getBytes());
        }
    }
    
    // ==================== 债权删除相关 ====================
    
    /**
     * 删除债权（统一入口）
     * 通过负数处置实现删除功能
     */
    @Transactional
    public DebtDeletionResult deleteDebt(DebtDeletionDTO dto) {
        logger.info("开始删除债权: 类型={}, 债权人={}, 债务人={}", 
                   dto.getDeletionType(), dto.getCreditor(), dto.getDebtor());
        
        if (debtDeletionService == null) {
            logger.error("债权删除服务未配置");
            return DebtDeletionResult.failure("债权删除服务未配置");
        }
        
        try {
            return debtDeletionService.deleteDebt(dto);
        } catch (Exception e) {
            logger.error("删除债权失败", e);
            throw new RuntimeException("删除债权失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除新增债权
     */
    @Transactional
    public DebtDeletionResult deleteAddition(DebtDeletionDTO dto) {
        dto.setDeletionType("DELETE_ADDITION");
        return deleteDebt(dto);
    }
    
    /**
     * 删除处置债权
     */
    @Transactional
    public DebtDeletionResult deleteDisposal(DebtDeletionDTO dto) {
        dto.setDeletionType("DELETE_DISPOSAL");
        return deleteDebt(dto);
    }
    
    // ==================== DTO转换方法 ====================

    /**
     * 将api-gateway模块的DTO转换为common模块的DTO
     * 用于重构后的Service类
     */
    private com.laoshu198838.dto.debt.entity.OverdueDebtAddDTO convertToRefactoredDto(OverdueDebtAddDTO originalDto) {
        com.laoshu198838.dto.debt.entity.OverdueDebtAddDTO refactoredDto =
            new com.laoshu198838.dto.debt.entity.OverdueDebtAddDTO();

        // 复制所有字段
        refactoredDto.setCreditor(originalDto.getCreditor());
        refactoredDto.setDebtor(originalDto.getDebtor());
        refactoredDto.setManagementCompany(originalDto.getManagementCompany());
        refactoredDto.setSubjectName(originalDto.getSubjectName());
        refactoredDto.setIsLitigation(originalDto.getIsLitigation());
        refactoredDto.setResponsiblePerson(originalDto.getResponsiblePerson());
        refactoredDto.setOverdueAmount(originalDto.getOverdueAmount());
        refactoredDto.setProvisionAmount(originalDto.getProvisionAmount());
        refactoredDto.setDebtCategory(originalDto.getDebtCategory());
        refactoredDto.setDebtNature(originalDto.getDebtNature());
        refactoredDto.setOverdueDate(originalDto.getOverdueDate());
        refactoredDto.setAddDate(originalDto.getAddDate());
        refactoredDto.setPeriod(originalDto.getPeriod());
        refactoredDto.setMeasures(originalDto.getMeasures());

        return refactoredDto;
    }

    // ==================== BusinessService接口实现 ====================
    
    @Override
    public OverdueDebtAdd save(OverdueDebtAdd entity) {
        // 这里可以实现通用的保存逻辑
        throw new UnsupportedOperationException("请使用具体的业务方法");
    }
    
    @Override
    public OverdueDebtAdd update(OverdueDebtAdd entity) {
        // 这里可以实现通用的更新逻辑
        throw new UnsupportedOperationException("请使用具体的业务方法");
    }
    
    @Override
    public void delete(String id) {
        // 这里可以实现通用的删除逻辑
        throw new UnsupportedOperationException("请使用具体的业务方法");
    }
    
    @Override
    public OverdueDebtAdd findById(String id) {
        // 这里可以实现通用的查询逻辑
        throw new UnsupportedOperationException("请使用具体的业务方法");
    }
    
    @Override
    public List<OverdueDebtAdd> findAll() {
        // 这里可以实现通用的查询逻辑
        throw new UnsupportedOperationException("请使用具体的业务方法");
    }
}
