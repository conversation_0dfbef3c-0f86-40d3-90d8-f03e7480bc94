package com.laoshu198838.service;

import com.laoshu198838.dto.debt.DebtDeletionDTO;
import com.laoshu198838.dto.debt.DebtDeletionResult;
import com.laoshu198838.entity.overdue_debt.*;
import com.laoshu198838.repository.overdue_debt.*;
import com.laoshu198838.util.debt.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 债权删除服务
 * 通过负数处置实现债权删除功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class DebtDeletionService {

    @Autowired
    private OverdueDebtAddRepository overdueDebtAddRepository;

    @Autowired
    private OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;

    @Autowired
    private ImpairmentReserveRepository impairmentReserveRepository;

    @Autowired
    private LitigationClaimRepository litigationClaimRepository;

    @Autowired
    private NonLitigationClaimRepository nonLitigationClaimRepository;

    @Autowired
    private DataConsistencyCheckService dataConsistencyCheckService;

    @Autowired
    private AuditLogService auditLogService;
    
    @Autowired
    private OverdueDebtUpdateService overdueDebtUpdateService;

    /**
     * 删除债权（统一入口）
     */
    public DebtDeletionResult deleteDebt(DebtDeletionDTO dto) {
        long startTime = System.currentTimeMillis();

        log.info("开始删除债权操作: 类型={}, 债权人={}, 债务人={}, 金额={}",
                dto.getDeletionType(), dto.getCreditor(), dto.getDebtor(), dto.getAmount());

        try {
            // 1. 验证请求
            validateDeletionRequest(dto);
            
            // 2. 根据删除类型处理
            DebtDeletionResult result;
            if (dto.isDeleteAddition()) {
                result = deleteAddition(dto);
            } else {
                result = deleteDisposal(dto);
            }

            // 3. 设置执行时间
            result.withExecutionTime(startTime);

            log.info("债权删除成功: {}", result.getMessage());
            return result;

        } catch (Exception e) {
            log.error("债权删除失败", e);
            return DebtDeletionResult.failure(
                "删除操作失败: " + e.getMessage(),
                e.toString(),
                "DELETE_ERROR"
            ).withExecutionTime(startTime);
        }
    }

    /**
     * 删除新增债权
     */
    private DebtDeletionResult deleteAddition(DebtDeletionDTO dto) {
        log.info("执行新增债权删除: 债权人={}, 债务人={}, 年={}, 月={}", 
                dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth());

        Map<String, Object> affectedRecords = new HashMap<>();
        
        try {
            // 1. 记录审计日志
            Long auditLogId = auditLogService.logDeletion(
                "DELETE_ADDITION", 
                dto, 
                "删除新增债权"
            );

            // 2. 更新新增表（设置对应月份为负数）
            updateAddTableForDeletion(dto, affectedRecords);

            // 3. 重新计算删除月份的余额（这会更新所有相关表）
            recalculateBalancesForMonth(dto);

            // 4. 处理后续月份更新 - 使用改进的余额传递方法
            if (FiveTableUpdateHelper.needsSubsequentMonthsUpdate(createUpdateContext(dto))) {
                updateSubsequentMonthsWithBalanceTransfer(dto, affectedRecords);
            }

            // 5. 验证数据一致性
            validateDataConsistency(dto);

            return DebtDeletionResult.success("新增债权删除成功", affectedRecords)
                    .withAffectedTables("新增表", "减值准备表", 
                                       "是".equals(dto.getIsLitigation()) ? "诉讼表" : "非诉讼表")
                    .withAuditLogId(auditLogId);

        } catch (Exception e) {
            log.error("删除新增债权失败", e);
            throw new RuntimeException("删除新增债权失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除处置债权
     */
    private DebtDeletionResult deleteDisposal(DebtDeletionDTO dto) {
        log.info("执行处置债权删除: 债权人={}, 债务人={}, 年={}, 月={}", 
                dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth());

        Map<String, Object> affectedRecords = new HashMap<>();
        
        try {
            // 1. 记录审计日志
            Long auditLogId = auditLogService.logDeletion(
                "DELETE_DISPOSAL", 
                dto, 
                "删除处置债权"
            );

            BigDecimal beforeDisposal = calculateTotalDisposalForMonth(dto);

            // 2. 查找要删除的处置记录
            OverdueDebtDecrease.OverdueDebtDecreaseKey key = new OverdueDebtDecrease.OverdueDebtDecreaseKey();
            key.setCreditor(dto.getCreditor());
            key.setDebtor(dto.getDebtor());
            key.setPeriod(dto.getPeriod());
            key.setIsLitigation(dto.getIsLitigation());
            key.setYear(dto.getYear());
            key.setMonth(new BigDecimal(dto.getMonth()));
            
            Optional<OverdueDebtDecrease> existingRecord = overdueDebtDecreaseRepository.findById(key);
            
            if (!existingRecord.isPresent()) {
                log.warn("未找到要删除的处置记录");
                return DebtDeletionResult.failure("未找到要删除的处置记录");
            }
            
            OverdueDebtDecrease recordToDelete = existingRecord.get();
            BigDecimal deletedAmount = recordToDelete.getMonthlyReduceAmount() != null ? 
                                     recordToDelete.getMonthlyReduceAmount() : BigDecimal.ZERO;
            
            log.info("准备物理删除处置记录: 债权人={}, 债务人={}, 期间={}, 金额={}", 
                    dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), deletedAmount);
            
            // 物理删除处置记录
            overdueDebtDecreaseRepository.delete(recordToDelete);
            affectedRecords.put("处置表", "已删除记录: " + recordToDelete.getId());
            log.info("处置记录物理删除完成");

            BigDecimal afterDisposal = calculateTotalDisposalForMonth(dto);

            // 3. 更新新增表的处置金额和债权余额（使用实际删除的金额）
            dto.setAmount(deletedAmount);  // 设置为实际删除的金额
            updateAddTableDisposalAmount(dto, affectedRecords);

            // 4. 重新计算删除月份的余额（这会更新所有相关表）
            log.info("开始调用 recalculateBalancesForMonth");
            recalculateBalancesForMonth(dto);
            log.info("完成调用 recalculateBalancesForMonth");

            // 5. 处理后续月份更新 - 使用改进的余额传递方法
            if (FiveTableUpdateHelper.needsSubsequentMonthsUpdate(createUpdateContext(dto))) {
                updateSubsequentMonthsWithBalanceTransfer(dto, affectedRecords);
            }

            // 6. 验证数据一致性
            validateDataConsistency(dto);

            return DebtDeletionResult.success("处置债权删除成功", affectedRecords)
                    .withAffectedTables("处置表", "新增表", "减值准备表",
                                       "是".equals(dto.getIsLitigation()) ? "诉讼表" : "非诉讼表")
                    .withAuditLogId(auditLogId);

        } catch (Exception e) {
            log.error("删除处置债权失败", e);
            throw new RuntimeException("删除处置债权失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新新增表（删除新增债权）
     */
    private void updateAddTableForDeletion(DebtDeletionDTO dto, Map<String, Object> affectedRecords) {
        // 查找新增表记录
        OverdueDebtAdd.OverdueDebtAddKey key = new OverdueDebtAdd.OverdueDebtAddKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setPeriod(dto.getPeriod());
        key.setIsLitigation(dto.getIsLitigation());
        key.setYear(dto.getYear().toString());

        Optional<OverdueDebtAdd> optionalRecord = overdueDebtAddRepository.findById(key);
        if (!optionalRecord.isPresent()) {
            throw new RuntimeException("未找到要删除的新增债权记录");
        }

        OverdueDebtAdd record = optionalRecord.get();
        
        // 获取对应月份的字段名
        String monthField = FiveTableUpdateHelper.getMonthChineseName(dto.getMonth());
        BigDecimal currentAmount = getMonthAmount(record, dto.getMonth());
        
        // 设置负数金额
        BigDecimal negativeAmount = dto.getNegativeAmount();
        setMonthAmount(record, dto.getMonth(), 
                      DebtCalculationUtils.safeAdd(currentAmount, negativeAmount));
        
        // 更新备注
        String originalRemark = record.getRemark();
        record.setRemark((originalRemark != null ? originalRemark + "; " : "") + 
                        "删除操作: " + dto.getDeleteReason());
        
        // 保存更新
        overdueDebtAddRepository.save(record);
        affectedRecords.put("新增表", record);
        
        log.info("更新新增表成功: {}月金额从{}更新为{}", 
                monthField, currentAmount, 
                DebtCalculationUtils.safeAdd(currentAmount, negativeAmount));
    }


    /**
     * 更新减值准备表（删除操作）
     */
    private void updateImpairmentReserveForDeletion(DebtDeletionDTO dto, String operationType,
                                                    Map<String, Object> affectedRecords) {
        // 查找减值准备表记录
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setYear(dto.getYear());
        key.setMonth(dto.getMonth());
        key.setIsLitigation(dto.getIsLitigation());
        key.setPeriod(dto.getPeriod());

        Optional<ImpairmentReserve> optionalRecord = impairmentReserveRepository.findById(key);
        if (!optionalRecord.isPresent()) {
            log.warn("未找到减值准备表记录，创建新记录");
            // 可以选择创建新记录或抛出异常
            return;
        }

        ImpairmentReserve record = optionalRecord.get();
        
        // 创建更新上下文
        FiveTableUpdateHelper.UpdateContext context = createUpdateContext(dto);
        context.setOperationType(operationType.equals("ADDITION") ? "DELETE_ADD" : "DELETE_DISPOSE");
        
        // 更新减值准备表
        FiveTableUpdateHelper.updateImpairmentReserve(record, context);
        
        // 保存更新
        impairmentReserveRepository.save(record);
        affectedRecords.put("减值准备表", record);
        
        log.info("更新减值准备表成功");
    }

    /**
     * 更新诉讼表（删除操作）
     */
    private void updateLitigationClaimForDeletion(DebtDeletionDTO dto, String operationType,
                                                  Map<String, Object> affectedRecords) {
        // 查找诉讼表记录
        Optional<LitigationClaim> claimOpt = litigationClaimRepository.findByCreditorAndDebtorAndYearAndMonthAndPeriod(
            dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth(), dto.getPeriod()
        );

        if (!claimOpt.isPresent()) {
            log.warn("未找到诉讼表记录");
            return;
        }

        LitigationClaim claim = claimOpt.get();
        
        // 创建更新上下文
        FiveTableUpdateHelper.UpdateContext context = createUpdateContext(dto);
        context.setOperationType(operationType.equals("ADDITION") ? "DELETE_ADD" : "DELETE_DISPOSE");
        
        // 更新诉讼表
        FiveTableUpdateHelper.updateLitigationClaim(claim, context);
        
        // 设置案件名称
        if (dto.getCaseName() != null && !dto.getCaseName().isEmpty()) {
            claim.setLitigationCase(dto.getCaseName());
        } else if (claim.getLitigationCase() == null || claim.getLitigationCase().isEmpty()) {
            claim.setLitigationCase(dto.getCreditor() + "诉" + dto.getDebtor());
        }
        
        // 保存更新
        litigationClaimRepository.save(claim);
        affectedRecords.put("诉讼表", claim);
        
        log.info("更新诉讼表成功");
    }

    /**
     * 更新非诉讼表（删除操作）
     */
    private void updateNonLitigationClaimForDeletion(DebtDeletionDTO dto, String operationType,
                                                     Map<String, Object> affectedRecords) {
        // 查找非诉讼表记录
        Optional<NonLitigationClaim> claimOpt = nonLitigationClaimRepository.findByCreditorAndDebtorAndYearAndMonthAndPeriod(
            dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth(), dto.getPeriod()
        );

        if (!claimOpt.isPresent()) {
            log.warn("未找到非诉讼表记录");
            return;
        }

        NonLitigationClaim claim = claimOpt.get();
        
        // 创建更新上下文
        FiveTableUpdateHelper.UpdateContext context = createUpdateContext(dto);
        context.setOperationType(operationType.equals("ADDITION") ? "DELETE_ADD" : "DELETE_DISPOSE");
        
        // 更新非诉讼表
        FiveTableUpdateHelper.updateNonLitigationClaim(claim, context);
        
        // 保存更新
        nonLitigationClaimRepository.save(claim);
        affectedRecords.put("非诉讼表", claim);
        
        log.info("更新非诉讼表成功");
    }

    /**
     * 更新新增表的处置金额（删除处置时）
     */
    private void updateAddTableDisposalAmount(DebtDeletionDTO dto, Map<String, Object> affectedRecords) {
        // 查找新增表记录
        OverdueDebtAdd.OverdueDebtAddKey key = new OverdueDebtAdd.OverdueDebtAddKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setPeriod(dto.getPeriod());
        key.setIsLitigation(dto.getIsLitigation());
        key.setYear(dto.getYear().toString());

        Optional<OverdueDebtAdd> optionalRecord = overdueDebtAddRepository.findById(key);
        if (!optionalRecord.isPresent()) {
            log.warn("未找到新增表记录，跳过处置金额更新");
            return;
        }

        OverdueDebtAdd record = optionalRecord.get();
        
        // 更新处置金额（减少）
        BigDecimal negativeAmount = dto.getNegativeAmount();
        record.setCashDisposal(DebtCalculationUtils.safeAdd(record.getCashDisposal(), negativeAmount));
        
        // 重新计算债权余额
        BigDecimal newAmount = record.getNewOverdueDebtAmount() != null ? 
                              record.getNewOverdueDebtAmount() : BigDecimal.ZERO;
        BigDecimal totalDisposal = DebtCalculationUtils.safeAdd(
            record.getCashDisposal(),
            DebtCalculationUtils.safeAdd(
                record.getInstallmentRepayment(),
                DebtCalculationUtils.safeAdd(record.getAssetDebt(), record.getOtherMethods())
            )
        );
        
        record.setDebtBalance(DebtCalculationUtils.safeSubtract(newAmount, totalDisposal));
        
        // 保存更新
        overdueDebtAddRepository.save(record);
        
        log.info("更新新增表处置金额成功");
    }

    /**
     * 重新计算指定月份的余额
     */
    private void recalculateBalancesForMonth(DebtDeletionDTO dto) {
        log.info("重新计算{}年{}月的余额", dto.getYear(), dto.getMonth());
        
        // 从处置表获取当月的实际处置总额
        BigDecimal totalDisposal = calculateTotalDisposalForMonth(dto);
        
        // 从新增表获取当月的新增总额
        BigDecimal totalAddition = calculateTotalAdditionForMonth(dto);
        
        // 获取上月余额
        BigDecimal lastMonthBalance = getLastMonthBalance(dto);
        
        // 计算本月末余额
        BigDecimal currentMonthBalance = DebtCalculationUtils.calculateMonthEndBalance(
            lastMonthBalance, totalAddition, totalDisposal);
        
        log.info("余额计算: 上月余额={}, 本月新增={}, 本月处置={}, 本月末余额={}", 
                lastMonthBalance, totalAddition, totalDisposal, currentMonthBalance);
        
        // 更新各表的余额
        updateTableBalances(dto, currentMonthBalance, totalAddition, totalDisposal);
    }
    
    /**
     * 计算指定月份的处置总额
     */
    private BigDecimal calculateTotalDisposalForMonth(DebtDeletionDTO dto) {
        // Temporarily commented out due to compilation error
        // List<OverdueDebtDecrease> disposals = overdueDebtDecreaseRepository
        //     .findByCreditorAndDebtorAndPeriodAndYearAndMonth(
        //         dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), 
        //         dto.getYear(), new BigDecimal(dto.getMonth()));
        List<OverdueDebtDecrease> disposals = new ArrayList<>();
        
        BigDecimal total = BigDecimal.ZERO;
        for (OverdueDebtDecrease disposal : disposals) {
            if (disposal.getMonthlyReduceAmount() != null) {
                total = DebtCalculationUtils.safeAdd(total, disposal.getMonthlyReduceAmount());
            }
        }
        return total;
    }
    
    /**
     * 计算指定月份的新增总额
     */
    private BigDecimal calculateTotalAdditionForMonth(DebtDeletionDTO dto) {
        OverdueDebtAdd.OverdueDebtAddKey key = new OverdueDebtAdd.OverdueDebtAddKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setPeriod(dto.getPeriod());
        key.setIsLitigation(dto.getIsLitigation());
        key.setYear(dto.getYear().toString());
        
        Optional<OverdueDebtAdd> optionalRecord = overdueDebtAddRepository.findById(key);
        if (optionalRecord.isPresent()) {
            OverdueDebtAdd record = optionalRecord.get();
            return getMonthAmount(record, dto.getMonth());
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * 获取上月余额
     */
    private BigDecimal getLastMonthBalance(DebtDeletionDTO dto) {
        if (dto.getMonth() == 1) {
            // 1月份的上月余额需要从去年12月获取
            return getBalanceFromPreviousYear(dto);
        } else {
            // 获取上个月的余额
            DebtDeletionDTO lastMonthDto = new DebtDeletionDTO();
            lastMonthDto.setCreditor(dto.getCreditor());
            lastMonthDto.setDebtor(dto.getDebtor());
            lastMonthDto.setPeriod(dto.getPeriod());
            lastMonthDto.setIsLitigation(dto.getIsLitigation());
            lastMonthDto.setYear(dto.getYear());
            lastMonthDto.setMonth(dto.getMonth() - 1);
            
            return getCurrentMonthBalance(lastMonthDto);
        }
    }
    
    /**
     * 获取当前月份的余额
     */
    private BigDecimal getCurrentMonthBalance(DebtDeletionDTO dto) {
        // 从减值准备表获取余额（汇总表）
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setYear(dto.getYear());
        key.setMonth(dto.getMonth());
        key.setIsLitigation(dto.getIsLitigation());
        key.setPeriod(dto.getPeriod());
        
        Optional<ImpairmentReserve> optionalRecord = impairmentReserveRepository.findById(key);
        if (optionalRecord.isPresent()) {
            ImpairmentReserve record = optionalRecord.get();
            return record.getCurrentMonthBalance() != null ? 
                   record.getCurrentMonthBalance() : BigDecimal.ZERO;
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * 从去年12月获取余额
     */
    private BigDecimal getBalanceFromPreviousYear(DebtDeletionDTO dto) {
        DebtDeletionDTO lastYearDto = new DebtDeletionDTO();
        lastYearDto.setCreditor(dto.getCreditor());
        lastYearDto.setDebtor(dto.getDebtor());
        lastYearDto.setPeriod(dto.getPeriod());
        lastYearDto.setIsLitigation(dto.getIsLitigation());
        lastYearDto.setYear(dto.getYear() - 1);
        lastYearDto.setMonth(12);
        
        return getCurrentMonthBalance(lastYearDto);
    }
    
    /**
     * 更新各表的余额
     */
    private void updateTableBalances(DebtDeletionDTO dto, BigDecimal currentMonthBalance,
                                   BigDecimal totalAddition, BigDecimal totalDisposal) {
        // 更新减值准备表
        updateImpairmentReserveBalance(dto, currentMonthBalance, totalAddition, totalDisposal);
        
        // 根据是否涉诉更新相应表
        if ("是".equals(dto.getIsLitigation())) {
            updateLitigationClaimBalance(dto, currentMonthBalance, totalAddition, totalDisposal);
        } else {
            updateNonLitigationClaimBalance(dto, currentMonthBalance, totalAddition, totalDisposal);
        }
    }
    
    /**
     * 处理后续月份更新 - 改进的余额传递方法
     */
    private void updateSubsequentMonthsWithBalanceTransfer(DebtDeletionDTO dto, 
                                                          Map<String, Object> affectedRecords) {
        log.info("开始更新后续月份数据（改进的余额传递）");
        
        Calendar[] subsequentMonths = FiveTableUpdateHelper.getSubsequentMonths(createUpdateContext(dto));
        
        for (Calendar month : subsequentMonths) {
            int year = month.get(Calendar.YEAR);
            int monthValue = month.get(Calendar.MONTH) + 1;
            
            log.info("更新{}年{}月数据", year, monthValue);
            
            // 创建新的DTO用于后续月份更新
            DebtDeletionDTO monthDto = new DebtDeletionDTO();
            monthDto.setCreditor(dto.getCreditor());
            monthDto.setDebtor(dto.getDebtor());
            monthDto.setManagementCompany(dto.getManagementCompany());
            monthDto.setIsLitigation(dto.getIsLitigation());
            monthDto.setPeriod(dto.getPeriod());
            monthDto.setYear(year);
            monthDto.setMonth(monthValue);
            
            // 重新计算该月份的余额
            recalculateBalancesForMonth(monthDto);
        }
        
        log.info("后续月份更新完成");
    }


    /**
     * 验证删除请求
     */
    private void validateDeletionRequest(DebtDeletionDTO dto) {
        // 使用验证工具类进行验证
        DebtValidationUtils.ValidationResult result = DebtValidationUtils.validateDeletionRequest(
            dto.getCreditor(),
            dto.getDebtor(),
            dto.getManagementCompany(),
            dto.getIsLitigation(),
            dto.getPeriod(),
            dto.getYear(),
            dto.getMonth(),
            dto.getAmount(),
            dto.getDeleteReason(),
            dto.getDeletionType()
        );

        if (!result.isValid()) {
            throw new IllegalArgumentException("验证失败: " + result.getErrorMessage());
        }

        // 额外的业务规则验证
        dto.validateBusinessRules();
    }

    /**
     * 验证数据一致性
     */
    private void validateDataConsistency(DebtDeletionDTO dto) {
        try {
            // 使用具体的数据一致性检查方法
            boolean isConsistent = true;
            try {
                // 检查新增表数据一致性
                var addResult = dataConsistencyCheckService.checkAddTableConsistency(
                    dto.getYear().toString(), dto.getMonth().toString());
                isConsistent = addResult != null;
            } catch (Exception ex) {
                log.warn("数据一致性检查异常: {}", ex.getMessage());
                isConsistent = false;
            }

            if (!isConsistent) {
                log.error("数据一致性验证失败");
                // 可以选择抛出异常或只记录警告
                // throw new RuntimeException("数据一致性验证失败");
            }
        } catch (Exception e) {
            log.error("数据一致性验证异常", e);
        }
    }

    /**
     * 创建更新上下文
     */
    private FiveTableUpdateHelper.UpdateContext createUpdateContext(DebtDeletionDTO dto) {
        FiveTableUpdateHelper.UpdateContext context = new FiveTableUpdateHelper.UpdateContext();
        context.setCreditor(dto.getCreditor());
        context.setDebtor(dto.getDebtor());
        context.setManagementCompany(dto.getManagementCompany());
        context.setIsLitigation(dto.getIsLitigation());
        context.setPeriod(dto.getPeriod());
        context.setYear(dto.getYear());
        context.setMonth(dto.getMonth());
        context.setAmount(dto.getNegativeAmount());
        context.setRemark(dto.getRemark());
        context.setOperationTime(LocalDateTime.now());
        return context;
    }

    /**
     * 获取月份金额
     */
    private BigDecimal getMonthAmount(OverdueDebtAdd record, int month) {
        switch (month) {
            case 1: return record.getAmountJan();
            case 2: return record.getAmountFeb();
            case 3: return record.getAmountMar();
            case 4: return record.getAmountApr();
            case 5: return record.getAmountMay();
            case 6: return record.getAmountJun();
            case 7: return record.getAmountJul();
            case 8: return record.getAmountAug();
            case 9: return record.getAmountSep();
            case 10: return record.getAmountOct();
            case 11: return record.getAmountNov();
            case 12: return record.getAmountDec();
            default: return BigDecimal.ZERO;
        }
    }

    /**
     * 设置月份金额
     */
    private void setMonthAmount(OverdueDebtAdd record, int month, BigDecimal amount) {
        switch (month) {
            case 1: record.setAmountJan(amount); break;
            case 2: record.setAmountFeb(amount); break;
            case 3: record.setAmountMar(amount); break;
            case 4: record.setAmountApr(amount); break;
            case 5: record.setAmountMay(amount); break;
            case 6: record.setAmountJun(amount); break;
            case 7: record.setAmountJul(amount); break;
            case 8: record.setAmountAug(amount); break;
            case 9: record.setAmountSep(amount); break;
            case 10: record.setAmountOct(amount); break;
            case 11: record.setAmountNov(amount); break;
            case 12: record.setAmountDec(amount); break;
        }
    }

    /**
     * 更新减值准备表余额
     */
    private void updateImpairmentReserveBalance(DebtDeletionDTO dto, BigDecimal currentMonthBalance,
                                              BigDecimal totalAddition, BigDecimal totalDisposal) {
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setYear(dto.getYear());
        key.setMonth(dto.getMonth());
        key.setIsLitigation(dto.getIsLitigation());
        key.setPeriod(dto.getPeriod());
        
        Optional<ImpairmentReserve> optionalRecord = impairmentReserveRepository.findById(key);
        if (optionalRecord.isPresent()) {
            ImpairmentReserve record = optionalRecord.get();
            
            // 获取上月债权余额
            BigDecimal lastMonthBalance = getLastMonthBalance(dto);
            
            // 获取上月的减值准备余额
            BigDecimal lastMonthProvisionBalance = getLastMonthProvisionBalance(dto);
            
            // 更新上月末余额字段（债权余额）
            record.setLastMonthBalance(lastMonthBalance);
            
            // 更新上月末减值准备余额
            record.setPreviousMonthBalance(lastMonthProvisionBalance);
            
            // 更新本月新增债权和本月处置债权
            record.setCurrentMonthNewDebt(totalAddition);
            record.setCurrentMonthDisposeDebt(totalDisposal);
            
            // 更新本月末债权余额
            record.setCurrentMonthBalance(currentMonthBalance);
            
            // 计算本月增减
            BigDecimal monthlyChange = DebtCalculationUtils.safeSubtract(totalAddition, totalDisposal);
            record.setCurrentMonthIncreaseDecrease(monthlyChange);
            
            // 更新本月末减值准备余额（基于上月的减值准备余额）
            BigDecimal currentProvisionBalance = DebtCalculationUtils.safeAdd(lastMonthProvisionBalance, monthlyChange);
            record.setCurrentMonthAmount(currentProvisionBalance);
            
            // 计提减值金额 = 本月末减值准备余额
            record.setImpairmentAmount(currentProvisionBalance);
            
            // 判断是否全额计提坏账
            boolean isFullyImpaired = DebtCalculationUtils.isFullyImpaired(currentMonthBalance, currentProvisionBalance);
            record.setIsAllImpaired(isFullyImpaired ? "是" : "否");
            
            // 计算本年度累计回收
            BigDecimal annualRecovery = calculateAnnualRecovery(dto);
            record.setAnnualCumulativeRecovery(annualRecovery);
            
            record.setUpdateTime(LocalDateTime.now());
            impairmentReserveRepository.save(record);
            
            log.info("更新减值准备表余额完成: 上月债权余额={}, 本月末债权余额={}, 上月减值准备余额={}, 本月末减值准备余额={}", 
                    lastMonthBalance, currentMonthBalance, lastMonthProvisionBalance, currentProvisionBalance);
        } else {
            log.warn("未找到减值准备表记录，跳过更新: 债权人={}, 债务人={}, 年={}, 月={}", 
                    dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth());
        }
    }
    
    /**
     * 更新诉讼表余额
     */
    private void updateLitigationClaimBalance(DebtDeletionDTO dto, BigDecimal currentMonthBalance,
                                            BigDecimal totalAddition, BigDecimal totalDisposal) {
        Optional<LitigationClaim> claimOpt = litigationClaimRepository.findByCreditorAndDebtorAndYearAndMonthAndPeriod(
            dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth(), dto.getPeriod()
        );
        
        if (claimOpt.isPresent()) {
            LitigationClaim claim = claimOpt.get();
            
            // 获取上月余额
            BigDecimal lastMonthBalance = getLastMonthBalance(dto);
            
            // 更新上月末债权余额字段
            claim.setLastMonthDebtBalance(lastMonthBalance);
            
            // 更新本月新增债权和本月处置债权
            claim.setCurrentMonthNewDebt(totalAddition);
            claim.setCurrentMonthDisposalDebt(totalDisposal);
            
            // 更新本月末债权余额
            claim.setCurrentMonthDebtBalance(currentMonthBalance);
            
            // 更新涉诉债权本金（根据余额调整）
            if (!DebtCalculationUtils.validateLitigationBalance(
                    lastMonthBalance, claim.getLitigationPrincipal(), claim.getLitigationInterest())) {
                BigDecimal[] adjusted = DebtCalculationUtils.adjustPrincipalAndInterest(
                    lastMonthBalance, claim.getLitigationPrincipal(), claim.getLitigationInterest()
                );
                claim.setLitigationPrincipal(adjusted[0]);
                claim.setLitigationInterest(adjusted[1]);
            }
            
            // 计算本年度累计回收
            BigDecimal annualRecovery = calculateAnnualRecovery(dto);
            claim.setAnnualCumulativeRecovery(annualRecovery);
            
            litigationClaimRepository.save(claim);
            
            log.info("更新诉讼表余额完成: 上月余额={}, 本月末债权余额={}, 本年度累计回收={}", 
                    lastMonthBalance, currentMonthBalance, annualRecovery);
        } else {
            log.warn("未找到诉讼表记录，跳过更新: 债权人={}, 债务人={}, 年={}, 月={}", 
                    dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth());
        }
    }
    
    /**
     * 更新非诉讼表余额
     */
    private void updateNonLitigationClaimBalance(DebtDeletionDTO dto, BigDecimal currentMonthBalance,
                                               BigDecimal totalAddition, BigDecimal totalDisposal) {
        Optional<NonLitigationClaim> claimOpt = nonLitigationClaimRepository.findByCreditorAndDebtorAndYearAndMonthAndPeriod(
            dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth(), dto.getPeriod()
        );
        
        if (claimOpt.isPresent()) {
            NonLitigationClaim claim = claimOpt.get();
            
            // 获取上月余额（本金）
            BigDecimal lastMonthPrincipal = BigDecimal.ZERO;
            BigDecimal lastMonthInterest = BigDecimal.ZERO;
            BigDecimal lastMonthPenalty = BigDecimal.ZERO;
            
            if (dto.getMonth() == 1) {
                // 1月份需要从去年12月获取
                DebtDeletionDTO lastYearDto = new DebtDeletionDTO();
                lastYearDto.setCreditor(dto.getCreditor());
                lastYearDto.setDebtor(dto.getDebtor());
                lastYearDto.setPeriod(dto.getPeriod());
                lastYearDto.setIsLitigation(dto.getIsLitigation());
                lastYearDto.setYear(dto.getYear() - 1);
                lastYearDto.setMonth(12);
                
                Optional<NonLitigationClaim> lastYearClaim = nonLitigationClaimRepository
                    .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                        lastYearDto.getCreditor(), lastYearDto.getDebtor(), 
                        lastYearDto.getYear(), 12, lastYearDto.getPeriod());
                
                if (lastYearClaim.isPresent()) {
                    lastMonthPrincipal = lastYearClaim.get().getCurrentMonthPrincipal() != null ? 
                                        lastYearClaim.get().getCurrentMonthPrincipal() : BigDecimal.ZERO;
                    lastMonthInterest = lastYearClaim.get().getCurrentMonthInterest() != null ? 
                                       lastYearClaim.get().getCurrentMonthInterest() : BigDecimal.ZERO;
                    lastMonthPenalty = lastYearClaim.get().getCurrentMonthPenalty() != null ? 
                                      lastYearClaim.get().getCurrentMonthPenalty() : BigDecimal.ZERO;
                }
            } else {
                // 从上个月获取
                Optional<NonLitigationClaim> lastMonthClaim = nonLitigationClaimRepository
                    .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                        dto.getCreditor(), dto.getDebtor(), dto.getYear(), 
                        dto.getMonth() - 1, dto.getPeriod());
                
                if (lastMonthClaim.isPresent()) {
                    lastMonthPrincipal = lastMonthClaim.get().getCurrentMonthPrincipal() != null ? 
                                        lastMonthClaim.get().getCurrentMonthPrincipal() : BigDecimal.ZERO;
                    lastMonthInterest = lastMonthClaim.get().getCurrentMonthInterest() != null ? 
                                       lastMonthClaim.get().getCurrentMonthInterest() : BigDecimal.ZERO;
                    lastMonthPenalty = lastMonthClaim.get().getCurrentMonthPenalty() != null ? 
                                      lastMonthClaim.get().getCurrentMonthPenalty() : BigDecimal.ZERO;
                }
            }
            
            // 更新上月末字段
            claim.setLastMonthPrincipal(lastMonthPrincipal);
            claim.setLastMonthInterest(lastMonthInterest);
            claim.setLastMonthPenalty(lastMonthPenalty);
            
            // 更新本月新增债权和本月处置债权
            claim.setCurrentMonthNewDebt(totalAddition);
            claim.setCurrentMonthDisposedDebt(totalDisposal);
            
            // 计算本月本金增减
            BigDecimal principalChange = DebtCalculationUtils.safeSubtract(totalAddition, totalDisposal);
            claim.setCurrentMonthPrincipalIncreaseDecrease(principalChange);
            
            // 更新本月末本金
            BigDecimal currentMonthPrincipal = DebtCalculationUtils.safeAdd(lastMonthPrincipal, principalChange);
            claim.setCurrentMonthPrincipal(currentMonthPrincipal);
            
            // 利息和违约金保持不变（如果没有特殊变化）
            claim.setCurrentMonthInterest(lastMonthInterest);
            claim.setCurrentMonthPenalty(lastMonthPenalty);
            
            // 更新本月末债权余额（本金+利息+违约金）
            claim.setCurrentMonthBalance(currentMonthBalance);
            
            // 计算本年度累计回收
            BigDecimal annualRecovery = calculateAnnualRecovery(dto);
            claim.setAnnualCumulativeRecovery(annualRecovery);
            
            nonLitigationClaimRepository.save(claim);
            
            log.info("更新非诉讼表余额完成: 上月本金={}, 本月末本金={}, 本月末债权余额={}, 本年度累计回收={}", 
                    lastMonthPrincipal, currentMonthPrincipal, currentMonthBalance, annualRecovery);
        } else {
            log.warn("未找到非诉讼表记录，跳过更新: 债权人={}, 债务人={}, 年={}, 月={}", 
                    dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth());
        }
    }
    
    /**
     * 计算本年度累计回收
     */
    private BigDecimal calculateAnnualRecovery(DebtDeletionDTO dto) {
        BigDecimal totalRecovery = BigDecimal.ZERO;
        
        // 从1月到当前月份累计
        for (int month = 1; month <= dto.getMonth(); month++) {
            // Temporarily commented out due to compilation error
            // List<OverdueDebtDecrease> monthDisposals = overdueDebtDecreaseRepository
            //     .findByCreditorAndDebtorAndPeriodAndYearAndMonth(
            //         dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), 
            //         dto.getYear(), new BigDecimal(month));
            List<OverdueDebtDecrease> monthDisposals = new ArrayList<>();
            
            for (OverdueDebtDecrease disposal : monthDisposals) {
                if (disposal.getMonthlyReduceAmount() != null && disposal.getMonthlyReduceAmount().compareTo(BigDecimal.ZERO) > 0) {
                    totalRecovery = DebtCalculationUtils.safeAdd(totalRecovery, disposal.getMonthlyReduceAmount());
                }
            }
        }
        
        return totalRecovery;
    }
    
    /**
     * 获取上月的减值准备余额
     */
    private BigDecimal getLastMonthProvisionBalance(DebtDeletionDTO dto) {
        if (dto.getMonth() == 1) {
            // 1月份的上月减值准备余额需要从去年12月获取
            return getProvisionBalanceFromPreviousYear(dto);
        } else {
            // 获取上个月的减值准备余额
            ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
            key.setCreditor(dto.getCreditor());
            key.setDebtor(dto.getDebtor());
            key.setYear(dto.getYear());
            key.setMonth(dto.getMonth() - 1);
            key.setIsLitigation(dto.getIsLitigation());
            key.setPeriod(dto.getPeriod());
            
            Optional<ImpairmentReserve> optionalRecord = impairmentReserveRepository.findById(key);
            if (optionalRecord.isPresent()) {
                ImpairmentReserve record = optionalRecord.get();
                // 返回上月的减值准备余额（本月末余额）
                return record.getCurrentMonthAmount() != null ? 
                       record.getCurrentMonthAmount() : BigDecimal.ZERO;
            }
            
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 从去年12月获取减值准备余额
     */
    private BigDecimal getProvisionBalanceFromPreviousYear(DebtDeletionDTO dto) {
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setYear(dto.getYear() - 1);
        key.setMonth(12);
        key.setIsLitigation(dto.getIsLitigation());
        key.setPeriod(dto.getPeriod());
        
        Optional<ImpairmentReserve> optionalRecord = impairmentReserveRepository.findById(key);
        if (optionalRecord.isPresent()) {
            ImpairmentReserve record = optionalRecord.get();
            return record.getCurrentMonthAmount() != null ? 
                   record.getCurrentMonthAmount() : BigDecimal.ZERO;
        }
        
        return BigDecimal.ZERO;
    }
}