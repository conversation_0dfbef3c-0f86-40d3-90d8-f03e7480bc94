package com.laoshu198838.service;

import com.laoshu198838.repository.overdue_debt.ConsistencyCheckRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据一致性检查服务
 * 提供跨表数据一致性检查功能
 * 
 * <AUTHOR>
 */
@Service
@Transactional(readOnly = true)
public class ConsistencyCheckService {

    private static final Logger logger = LoggerFactory.getLogger(ConsistencyCheckService.class);
    
    @Autowired
    private ConsistencyCheckRepository consistencyCheckRepository;

    /**
     * 执行完整的数据一致性检查
     * 
     * @param year 年份
     * @param month 月份
     * @return 一致性检查结果
     */
    public Map<String, Object> performFullConsistencyCheck(int year, int month) {
        logger.info("开始执行数据一致性检查，年份: {}, 月份: {}", year, month);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 新增金额一致性检查
            Map<String, Object> newAmountCheck = checkNewAmountConsistency(year, month);
            result.put("newAmountCheck", newAmountCheck);
            
            // 2. 处置金额一致性检查
            Map<String, Object> disposedAmountCheck = checkDisposedAmountConsistency(year, month);
            result.put("disposedAmountCheck", disposedAmountCheck);
            
            // 3. 期末余额一致性检查
            Map<String, Object> endingBalanceCheck = checkEndingBalanceConsistency(year, month);
            result.put("endingBalanceCheck", endingBalanceCheck);
            
            // 4. 年初至今累计数据检查
            Map<String, Object> yearToDateCheck = checkYearToDateConsistency(year, month);
            result.put("yearToDateCheck", yearToDateCheck);
            
            // 5. 计算总体一致性状态
            boolean isConsistent = calculateOverallConsistency(result);
            result.put("overallConsistent", isConsistent);
            result.put("checkTime", new Date());
            result.put("year", year);
            result.put("month", month);
            
            logger.info("数据一致性检查完成，总体一致性: {}", isConsistent);
            
        } catch (Exception e) {
            logger.error("数据一致性检查失败", e);
            result.put("error", "检查过程中发生错误: " + e.getMessage());
            result.put("overallConsistent", false);
        }
        
        return result;
    }

    /**
     * 检查新增金额一致性
     */
    public Map<String, Object> checkNewAmountConsistency(int year, int month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取汇总数据
            Map<String, Object> summary = consistencyCheckRepository.getNewAmountSummary(year, month);
            result.put("summary", summary);
            
            // 获取明细数据（不一致的记录）
            List<Map<String, Object>> details = consistencyCheckRepository.getNewAmountDetail(year);
            result.put("inconsistentDetails", details);
            
            // 计算一致性状态
            boolean isConsistent = calculateConsistency(summary) && details.isEmpty();
            result.put("isConsistent", isConsistent);
            result.put("inconsistentCount", details.size());
            
        } catch (Exception e) {
            logger.error("新增金额一致性检查失败", e);
            result.put("error", e.getMessage());
            result.put("isConsistent", false);
        }
        
        return result;
    }

    /**
     * 检查处置金额一致性
     */
    public Map<String, Object> checkDisposedAmountConsistency(int year, int month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取汇总数据
            Map<String, Object> summary = consistencyCheckRepository.getDisposedAmountSummary(year, month);
            result.put("summary", summary);
            
            // 获取明细数据（不一致的记录）
            List<Map<String, Object>> details = consistencyCheckRepository.getDisposedAmountDetail(year);
            result.put("inconsistentDetails", details);
            
            // 计算一致性状态
            boolean isConsistent = calculateConsistency(summary) && details.isEmpty();
            result.put("isConsistent", isConsistent);
            result.put("inconsistentCount", details.size());
            
        } catch (Exception e) {
            logger.error("处置金额一致性检查失败", e);
            result.put("error", e.getMessage());
            result.put("isConsistent", false);
        }
        
        return result;
    }

    /**
     * 检查期末余额一致性
     */
    public Map<String, Object> checkEndingBalanceConsistency(int year, int month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取汇总数据
            Map<String, Object> summary = consistencyCheckRepository.getEndingBalanceSummary(year, month);
            result.put("summary", summary);
            
            // 获取明细数据（不一致的记录）
            List<Map<String, Object>> details = consistencyCheckRepository.getEndingBalanceDetail(year, month);
            result.put("inconsistentDetails", details);
            
            // 计算一致性状态
            boolean isConsistent = calculateConsistency(summary) && details.isEmpty();
            result.put("isConsistent", isConsistent);
            result.put("inconsistentCount", details.size());
            
        } catch (Exception e) {
            logger.error("期末余额一致性检查失败", e);
            result.put("error", e.getMessage());
            result.put("isConsistent", false);
        }
        
        return result;
    }

    /**
     * 检查年初至今累计数据一致性
     */
    public Map<String, Object> checkYearToDateConsistency(int year, int month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取年初至今新增金额汇总
            Map<String, Object> newAmountSummary = consistencyCheckRepository.getYearToDateNewAmountSummary(year, month);
            result.put("newAmountSummary", newAmountSummary);
            
            // 获取年初至今处置金额汇总
            Map<String, Object> disposedAmountSummary = consistencyCheckRepository.getYearToDateDisposedAmountSummary(year, month);
            result.put("disposedAmountSummary", disposedAmountSummary);
            
            // 获取年初余额
            Map<String, Object> initialBalance = consistencyCheckRepository.getInitialBalanceSummary(year);
            result.put("initialBalance", initialBalance);
            
            // 计算一致性状态
            boolean newAmountConsistent = calculateConsistency(newAmountSummary);
            boolean disposedAmountConsistent = calculateConsistency(disposedAmountSummary);
            boolean initialBalanceConsistent = calculateConsistency(initialBalance);
            
            boolean isConsistent = newAmountConsistent && disposedAmountConsistent && initialBalanceConsistent;
            result.put("isConsistent", isConsistent);
            result.put("newAmountConsistent", newAmountConsistent);
            result.put("disposedAmountConsistent", disposedAmountConsistent);
            result.put("initialBalanceConsistent", initialBalanceConsistent);
            
        } catch (Exception e) {
            logger.error("年初至今数据一致性检查失败", e);
            result.put("error", e.getMessage());
            result.put("isConsistent", false);
        }
        
        return result;
    }

    /**
     * 计算汇总数据的一致性
     * 检查各表之间的数据是否一致（差异小于0.01）
     */
    private boolean calculateConsistency(Map<String, Object> summary) {
        if (summary == null || summary.isEmpty()) {
            return false;
        }
        
        try {
            BigDecimal addTableAmount = getBigDecimalValue(summary.get("addTableAmount"));
            BigDecimal litigationAmount = getBigDecimalValue(summary.get("litigationAmount"));
            BigDecimal nonLitigationAmount = getBigDecimalValue(summary.get("nonLitigationAmount"));
            BigDecimal impairmentAmount = getBigDecimalValue(summary.get("impairmentAmount"));
            
            // 计算各表之间的最大差异
            BigDecimal threshold = new BigDecimal("0.01");
            
            return Math.abs(addTableAmount.subtract(litigationAmount).doubleValue()) <= threshold.doubleValue() &&
                   Math.abs(addTableAmount.subtract(nonLitigationAmount).doubleValue()) <= threshold.doubleValue() &&
                   Math.abs(addTableAmount.subtract(impairmentAmount).doubleValue()) <= threshold.doubleValue() &&
                   Math.abs(litigationAmount.subtract(nonLitigationAmount).doubleValue()) <= threshold.doubleValue() &&
                   Math.abs(litigationAmount.subtract(impairmentAmount).doubleValue()) <= threshold.doubleValue() &&
                   Math.abs(nonLitigationAmount.subtract(impairmentAmount).doubleValue()) <= threshold.doubleValue();
                   
        } catch (Exception e) {
            logger.error("计算一致性时发生错误", e);
            return false;
        }
    }

    /**
     * 计算总体一致性状态
     */
    private boolean calculateOverallConsistency(Map<String, Object> result) {
        try {
            Map<String, Object> newAmountCheck = (Map<String, Object>) result.get("newAmountCheck");
            Map<String, Object> disposedAmountCheck = (Map<String, Object>) result.get("disposedAmountCheck");
            Map<String, Object> endingBalanceCheck = (Map<String, Object>) result.get("endingBalanceCheck");
            Map<String, Object> yearToDateCheck = (Map<String, Object>) result.get("yearToDateCheck");
            
            boolean newAmountConsistent = (Boolean) newAmountCheck.getOrDefault("isConsistent", false);
            boolean disposedAmountConsistent = (Boolean) disposedAmountCheck.getOrDefault("isConsistent", false);
            boolean endingBalanceConsistent = (Boolean) endingBalanceCheck.getOrDefault("isConsistent", false);
            boolean yearToDateConsistent = (Boolean) yearToDateCheck.getOrDefault("isConsistent", false);
            
            return newAmountConsistent && disposedAmountConsistent && endingBalanceConsistent && yearToDateConsistent;
            
        } catch (Exception e) {
            logger.error("计算总体一致性时发生错误", e);
            return false;
        }
    }

    /**
     * 安全地将Object转换为BigDecimal
     */
    private BigDecimal getBigDecimalValue(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("无法转换为BigDecimal: {}", value);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取一致性检查报告
     * 
     * @param year 年份
     * @param month 月份
     * @return 格式化的检查报告
     */
    public Map<String, Object> getConsistencyReport(int year, int month) {
        Map<String, Object> fullCheck = performFullConsistencyCheck(year, month);
        
        Map<String, Object> report = new HashMap<>();
        report.put("title", String.format("%d年%d月数据一致性检查报告", year, month));
        report.put("checkTime", fullCheck.get("checkTime"));
        report.put("overallStatus", (Boolean) fullCheck.get("overallConsistent") ? "通过" : "不通过");
        
        // 汇总各项检查结果
        List<Map<String, Object>> checkItems = new ArrayList<>();
        
        addCheckItem(checkItems, "新增金额一致性", (Map<String, Object>) fullCheck.get("newAmountCheck"));
        addCheckItem(checkItems, "处置金额一致性", (Map<String, Object>) fullCheck.get("disposedAmountCheck"));
        addCheckItem(checkItems, "期末余额一致性", (Map<String, Object>) fullCheck.get("endingBalanceCheck"));
        addCheckItem(checkItems, "年初至今数据一致性", (Map<String, Object>) fullCheck.get("yearToDateCheck"));
        
        report.put("checkItems", checkItems);
        report.put("rawData", fullCheck);
        
        return report;
    }

    /**
     * 添加检查项到报告中
     */
    private void addCheckItem(List<Map<String, Object>> checkItems, String itemName, Map<String, Object> checkResult) {
        Map<String, Object> item = new HashMap<>();
        item.put("name", itemName);
        item.put("status", (Boolean) checkResult.getOrDefault("isConsistent", false) ? "通过" : "不通过");
        item.put("inconsistentCount", checkResult.getOrDefault("inconsistentCount", 0));
        item.put("details", checkResult);
        checkItems.add(item);
    }
}
