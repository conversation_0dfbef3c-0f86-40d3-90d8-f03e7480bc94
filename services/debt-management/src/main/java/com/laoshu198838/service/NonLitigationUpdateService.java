package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 非诉讼表更新服务
 * 负责记录和查询非诉讼表状态
 *
 * <AUTHOR>
 */
@Service
public class NonLitigationUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(NonLitigationUpdateService.class);

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public NonLitigationUpdateService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        logger.info("NonLitigationUpdateService初始化完成");
    }


    /**
     * 记录非诉讼表状态
     */
    public void logNonLitigationTableStatus() {
        try {
            // 获取非诉讼表总记录数
            int totalCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM 非诉讼表", Integer.class);
            logger.info("非诉讼表当前记录数: {}", totalCount);

            // 获取最近5条记录
            List<Map<String, Object>> recentRecords = jdbcTemplate.queryForList(
                    "SELECT 序号, 债务人, 年份, 月份, 债权到期时间 FROM 非诉讼表 ORDER BY 序号 DESC LIMIT 5");

            logger.info("最近5条记录:");
            for (Map<String, Object> record : recentRecords) {
                logger.info("序号: {}, 债务人: {}, 年份: {}, 债权到期时间: {}",
                            record.get("序号"), record.get("债务人"), record.get("年份"), record.get("债权到期时间"));
            }
        } catch (Exception e) {
            logger.error("查询非诉讼表状态时发生错误", e);
        }
    }

} 