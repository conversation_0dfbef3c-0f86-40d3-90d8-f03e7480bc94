package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 非诉讼表更新服务
 * 负责记录和查询非诉讼表状态
 *
 * <AUTHOR>
 */
@Service
public class NonLitigationUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(NonLitigationUpdateService.class);

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public NonLitigationUpdateService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }


    /**
     * 记录非诉讼表状态
     */
    public void logNonLitigationTableStatus() {
        try {
            // 获取非诉讼表总记录数
            int totalCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM 非诉讼表", Integer.class);
            logger.info("非诉讼表当前记录数: {}", totalCount);
        } catch (Exception e) {
            logger.error("查询非诉讼表状态时发生错误", e);
        }
    }

} 