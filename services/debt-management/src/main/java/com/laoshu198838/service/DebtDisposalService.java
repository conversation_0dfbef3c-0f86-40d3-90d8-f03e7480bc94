package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 债权处置服务
 * 专门负责债权处置相关的业务操作
 * 
 * <AUTHOR>
 */
@Service
public class DebtDisposalService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtDisposalService.class);
    
    @Autowired
    private OverdueDebtDecreaseService overdueDebtDecreaseService;
    
    @Autowired
    private RefactoredOverdueDebtDecreaseService refactoredOverdueDebtDecreaseService;
    
    /**
     * 更新债权处置数据
     */
    @Transactional
    public ResponseEntity<?> updateDebtReductionData(Map<String, Object> inputData) {
        logger.info("更新债权处置数据: {}", inputData);
        return overdueDebtDecreaseService.updateDebtReductionData(inputData);
    }
    
    /**
     * 处置债权 - 重构版本
     */
    @Transactional
    public OverdueDebtDecrease disposeDebtRefactored(Map<String, Object> frontendData) {
        logger.info("处置债权(重构版本): {}", frontendData);
        return refactoredOverdueDebtDecreaseService.disposeDebt(frontendData);
    }
    
    /**
     * 删除处置记录
     */
    @Transactional
    public ResponseEntity<?> deleteDisposalRecord(Map<String, Object> deleteData) {
        logger.info("删除处置记录: {}", deleteData);
        return overdueDebtDecreaseService.deleteDisposalRecord(deleteData);
    }
}