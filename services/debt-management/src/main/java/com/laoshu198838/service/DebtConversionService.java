package com.laoshu198838.service;

import com.laoshu198838.dto.debt.DebtConversionRequestDTO;
import com.laoshu198838.dto.debt.DebtConversionResponseDTO;
import com.laoshu198838.dto.debt.DebtSearchResultDTO;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim.LitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim.NonLitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve.ImpairmentReserveKey;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.NonLitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.util.debt.DebtFieldMappingUtil;
import com.laoshu198838.util.debt.DebtConversionValidator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 债权转换服务类
 * 实现诉讼与非诉讼债权相互转换的核心业务逻辑
 * 
 * <AUTHOR>
 */
@Service
public class DebtConversionService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtConversionService.class);
    
    private final LitigationClaimRepository litigationClaimRepository;
    private final NonLitigationClaimRepository nonLitigationClaimRepository;
    private final ImpairmentReserveRepository impairmentReserveRepository;
    
    @Autowired
    public DebtConversionService(
            LitigationClaimRepository litigationClaimRepository,
            NonLitigationClaimRepository nonLitigationClaimRepository,
            ImpairmentReserveRepository impairmentReserveRepository) {
        this.litigationClaimRepository = litigationClaimRepository;
        this.nonLitigationClaimRepository = nonLitigationClaimRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
    }
    
    /**
     * 搜索可转换的债权记录
     * 
     * @param creditor 债权人（可选）
     * @param debtor 债务人（可选）
     * @return 匹配的债权记录列表
     */
    public List<DebtSearchResultDTO> searchConvertibleDebts(String creditor, String debtor) {
        logger.info("开始搜索可转换债权记录");
        
        List<DebtSearchResultDTO> results = new ArrayList<>();
        
        try {
            // 搜索诉讼债权
            List<LitigationClaim> litigationClaims = findLitigationClaims(creditor, debtor);
            for (LitigationClaim claim : litigationClaims) {
                DebtSearchResultDTO dto = convertLitigationToSearchResult(claim);
                results.add(dto);
            }
            
            // 搜索非诉讼债权
            List<NonLitigationClaim> nonLitigationClaims = findNonLitigationClaims(creditor, debtor);
            for (NonLitigationClaim claim : nonLitigationClaims) {
                DebtSearchResultDTO dto = convertNonLitigationToSearchResult(claim);
                results.add(dto);
            }
            
            logger.info("搜索完成，找到 {} 条可转换记录", results.size());
            
        } catch (Exception e) {
            logger.error("搜索可转换债权记录时发生错误", e);
            throw new RuntimeException("搜索债权记录失败：" + e.getMessage(), e);
        }
        
        return results;
    }
    
    /**
     * 诉讼转非诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @Transactional
    public DebtConversionResponseDTO convertLitigationToNonLitigation(DebtConversionRequestDTO request) {
        logger.info("开始执行诉讼转非诉讼，债权人：{}，债务人：{}", request.getCreditor(), request.getDebtor());
        
        // 验证转换请求
        DebtConversionValidator.ValidationResult validationResult = 
            DebtConversionValidator.validateConversionRequest(
                request.getCreditor(), request.getDebtor(), request.getPeriod(), 
                request.getConversionYear(), request.getConversionMonth());
        
        if (!validationResult.isValid()) {
            throw new RuntimeException("转换请求验证失败：" + validationResult.getSummary());
        }
        
        try {
            // 1. 查找诉讼表记录（查找转换年月的记录）
            LitigationCompositeKey litigationKey = new LitigationCompositeKey();
            litigationKey.setCreditor(request.getCreditor());
            litigationKey.setDebtor(request.getDebtor());
            litigationKey.setPeriod(request.getPeriod());
            litigationKey.setYear(request.getConversionYear());
            litigationKey.setMonth(request.getConversionMonth());
            
            LitigationClaim litigationClaim = litigationClaimRepository.findById(litigationKey)
                .orElseThrow(() -> new RuntimeException("未找到对应的诉讼债权记录"));
            
            // 2. 保存原始数据用于转换
            BigDecimal originalBalance = litigationClaim.getCurrentMonthDebtBalance();
            BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance();
            
            // 3. 更新诉讼表（清零相关字段，记录互转变动金额）
            litigationClaim.setLitigationPrincipal(BigDecimal.ZERO);
            litigationClaim.setLitigationInterest(BigDecimal.ZERO);
            litigationClaim.setCurrentMonthDebtBalance(BigDecimal.ZERO);
            // 记录互转减少金额（转出记录在减少字段）
            litigationClaim.setTransferDecreaseAmount(originalBalance != null ? originalBalance : BigDecimal.ZERO);
            // 互转不算新增和处置，不填写本月新增债权和本月处置债权字段
            
            String originalRemark = litigationClaim.getRemark() != null ? litigationClaim.getRemark() : "";
            String conversionRemark = String.format("【转换】%d年%d月转为非诉讼。%s", 
                request.getConversionYear(), request.getConversionMonth(), 
                request.getRemark() != null ? request.getRemark() : "");
            litigationClaim.setRemark(originalRemark.isEmpty() ? conversionRemark : originalRemark + "; " + conversionRemark);
            
            litigationClaimRepository.save(litigationClaim);
            
            // 4. 创建非诉讼表记录
            NonLitigationClaim newNonLitigationClaim = createNonLitigationRecord(request, litigationClaim, originalBalance, lastMonthBalance);
            
            // 验证转换结果（使用原始金额进行验证）
            DebtConversionValidator.ValidationResult conversionValidation = 
                DebtConversionValidator.validateLitigationToNonLitigationConversionWithOriginalAmount(
                    originalBalance, newNonLitigationClaim);
            
            if (!conversionValidation.isValid()) {
                logger.error("转换结果验证失败：{}", conversionValidation.getSummary());
                throw new RuntimeException("转换结果验证失败：" + conversionValidation.getSummary());
            }
            
            if (!conversionValidation.getWarnings().isEmpty()) {
                logger.warn("转换完成但存在警告：{}", conversionValidation.getSummary());
            }
            
            // 5. 更新减值准备表（删除旧记录并创建新记录）
            updateImpairmentReserveForConversion(request.getCreditor(), request.getDebtor(), 
                request.getConversionYear(), request.getConversionMonth(), request.getPeriod(), 
                "是", "否", litigationClaim, request.getRemark());
            
            logger.info("诉讼转非诉讼执行成功");
            
            return DebtConversionResponseDTO.success(
                String.format("诉讼债权已成功转换为非诉讼债权 - 债权人：%s，债务人：%s", request.getCreditor(), request.getDebtor()), 
                "litigation_to_non_litigation", 
                1);
                
        } catch (Exception e) {
            logger.error("诉讼转非诉讼执行失败", e);
            throw new RuntimeException("诉讼转非诉讼失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 非诉讼转诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @Transactional
    public DebtConversionResponseDTO convertNonLitigationToLitigation(DebtConversionRequestDTO request) {
        logger.info("开始执行非诉讼转诉讼，债权人：{}，债务人：{}", request.getCreditor(), request.getDebtor());
        
        // 验证转换请求
        DebtConversionValidator.ValidationResult validationResult = 
            DebtConversionValidator.validateConversionRequest(
                request.getCreditor(), request.getDebtor(), request.getPeriod(), 
                request.getConversionYear(), request.getConversionMonth());
        
        if (!validationResult.isValid()) {
            throw new RuntimeException("转换请求验证失败：" + validationResult.getSummary());
        }
        
        try {
            // 1. 查找非诉讼表记录（查找转换年月的记录）
            NonLitigationCompositeKey nonLitigationKey = new NonLitigationCompositeKey();
            nonLitigationKey.setCreditor(request.getCreditor());
            nonLitigationKey.setDebtor(request.getDebtor());
            nonLitigationKey.setPeriod(request.getPeriod());
            nonLitigationKey.setYear(request.getConversionYear());
            nonLitigationKey.setMonth(request.getConversionMonth());
            
            NonLitigationClaim nonLitigationClaim = nonLitigationClaimRepository.findById(nonLitigationKey)
                .orElseThrow(() -> new RuntimeException("未找到对应的非诉讼债权记录"));
            
            // 2. 保存原始数据用于转换
            BigDecimal originalPrincipal = nonLitigationClaim.getCurrentMonthPrincipal();
            BigDecimal originalInterest = nonLitigationClaim.getCurrentMonthInterest();
            BigDecimal originalPenalty = nonLitigationClaim.getCurrentMonthPenalty();
            BigDecimal totalBalance = (originalPrincipal != null ? originalPrincipal : BigDecimal.ZERO)
                .add(originalInterest != null ? originalInterest : BigDecimal.ZERO)
                .add(originalPenalty != null ? originalPenalty : BigDecimal.ZERO);
            
            
            BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal();
            BigDecimal lastMonthInterest = nonLitigationClaim.getLastMonthInterest();
            BigDecimal lastMonthPenalty = nonLitigationClaim.getLastMonthPenalty();
            
            // 3. 更新非诉讼表（本月增减填写负数保持数学逻辑一致性）
            // 为保持 上月末本金 + 本月本金增减 = 本月末本金 的逻辑，需要填写负数
            nonLitigationClaim.setCurrentMonthPrincipalIncreaseDecrease(
                originalPrincipal != null ? originalPrincipal.negate() : BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthInterestIncreaseDecrease(
                originalInterest != null ? originalInterest.negate() : BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPenaltyIncreaseDecrease(
                originalPenalty != null ? originalPenalty.negate() : BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPrincipal(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthInterest(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPenalty(BigDecimal.ZERO);
            // 记录互转减少金额（转出记录在减少字段）
            nonLitigationClaim.setTransferDecreaseAmount(totalBalance != null ? totalBalance : BigDecimal.ZERO);
            // 互转不算新增和处置，不填写本月新增债权和本月处置债权字段
            
            String originalRemark = nonLitigationClaim.getRemark() != null ? nonLitigationClaim.getRemark() : "";
            String conversionRemark = String.format("【转换】%d年%d月转为诉讼。%s", 
                request.getConversionYear(), request.getConversionMonth(), 
                request.getRemark() != null ? request.getRemark() : "");
            nonLitigationClaim.setRemark(originalRemark.isEmpty() ? conversionRemark : originalRemark + "; " + conversionRemark);
            
            nonLitigationClaimRepository.save(nonLitigationClaim);
            
            // 4. 创建诉讼表记录
            LitigationClaim newLitigationClaim = createLitigationRecord(request, nonLitigationClaim, originalPrincipal, originalInterest, originalPenalty, totalBalance, lastMonthPrincipal, lastMonthInterest, lastMonthPenalty);
            
            // 验证转换结果（使用原始金额进行验证）
            DebtConversionValidator.ValidationResult conversionValidation = 
                DebtConversionValidator.validateNonLitigationToLitigationConversionWithOriginalAmount(
                    totalBalance, newLitigationClaim);
            
            if (!conversionValidation.isValid()) {
                logger.error("转换结果验证失败：{}", conversionValidation.getSummary());
                throw new RuntimeException("转换结果验证失败：" + conversionValidation.getSummary());
            }
            
            if (!conversionValidation.getWarnings().isEmpty()) {
                logger.warn("转换完成但存在警告：{}", conversionValidation.getSummary());
            }
            
            // 5. 更新减值准备表（删除旧记录并创建新记录）
            updateImpairmentReserveForConversion(request.getCreditor(), request.getDebtor(), 
                request.getConversionYear(), request.getConversionMonth(), request.getPeriod(), 
                "否", "是", nonLitigationClaim, request.getLitigationCase());
            
            logger.info("非诉讼转诉讼执行成功");
            
            return DebtConversionResponseDTO.success(
                String.format("非诉讼债权已成功转换为诉讼债权 - 债权人：%s，债务人：%s", request.getCreditor(), request.getDebtor()), 
                "non_litigation_to_litigation", 
                1);
                
        } catch (Exception e) {
            logger.error("非诉讼转诉讼执行失败", e);
            throw new RuntimeException("非诉讼转诉讼失败：" + e.getMessage(), e);
        }
    }
    
    // === 私有辅助方法 ===
    
    private List<LitigationClaim> findLitigationClaims(String creditor, String debtor) {
        if (creditor != null && debtor != null) {
            return litigationClaimRepository.findByCreditorAndDebtor(creditor, debtor);
        } else if (creditor != null) {
            return litigationClaimRepository.findByCreditor(creditor);
        } else if (debtor != null) {
            return litigationClaimRepository.findByDebtor(debtor);
        } else {
            return litigationClaimRepository.findAll();
        }
    }
    
    private List<NonLitigationClaim> findNonLitigationClaims(String creditor, String debtor) {
        if (creditor != null && debtor != null) {
            return nonLitigationClaimRepository.findByCreditorAndDebtor(creditor, debtor);
        } else if (creditor != null) {
            return nonLitigationClaimRepository.findByCreditor(creditor);
        } else if (debtor != null) {
            return nonLitigationClaimRepository.findByDebtor(debtor);
        } else {
            return nonLitigationClaimRepository.findAll();
        }
    }
    
    private DebtSearchResultDTO convertLitigationToSearchResult(LitigationClaim claim) {
        return DebtSearchResultDTO.builder()
            .creditor(claim.getId().getCreditor())
            .debtor(claim.getId().getDebtor())
            .period(claim.getId().getPeriod())
            .debtBalance(claim.getCurrentMonthDebtBalance())
            .currentStatus("诉讼")
            .isLitigation("是")
            .managementCompany(claim.getManagementCompany())
            .subjectName(claim.getSubjectName())
            .year(claim.getId().getYear())
            .month(claim.getId().getMonth())
            .sourceTable("litigation")
            .litigationCase(claim.getLitigationCase())
            .build();
    }
    
    private DebtSearchResultDTO convertNonLitigationToSearchResult(NonLitigationClaim claim) {
        BigDecimal totalBalance = (claim.getCurrentMonthPrincipal() != null ? claim.getCurrentMonthPrincipal() : BigDecimal.ZERO)
            .add(claim.getCurrentMonthInterest() != null ? claim.getCurrentMonthInterest() : BigDecimal.ZERO)
            .add(claim.getCurrentMonthPenalty() != null ? claim.getCurrentMonthPenalty() : BigDecimal.ZERO);
            
        return DebtSearchResultDTO.builder()
            .creditor(claim.getId().getCreditor())
            .debtor(claim.getId().getDebtor())
            .period(claim.getId().getPeriod())
            .debtBalance(totalBalance)
            .currentStatus("非诉讼")
            .isLitigation("否")
            .managementCompany(claim.getManagementCompany())
            .subjectName(claim.getSubjectName())
            .year(claim.getId().getYear())
            .month(claim.getId().getMonth())
            .sourceTable("non_litigation")
            .principal(claim.getCurrentMonthPrincipal())
            .interest(claim.getCurrentMonthInterest())
            .penalty(claim.getCurrentMonthPenalty())
            .build();
    }
    
    private NonLitigationClaim createNonLitigationRecord(DebtConversionRequestDTO request, LitigationClaim sourceClaim, BigDecimal originalBalance, BigDecimal lastMonthBalance) {
        NonLitigationClaim newClaim = new NonLitigationClaim();
        
        // 设置复合主键
        NonLitigationCompositeKey key = new NonLitigationCompositeKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(request.getConversionYear());
        key.setMonth(request.getConversionMonth());
        newClaim.setId(key);
        
        // 设置基本信息
        // 序号字段由数据库自动生成，无需手动设置
        newClaim.setSubjectName(sourceClaim.getSubjectName());
        newClaim.setManagementCompany(sourceClaim.getManagementCompany());
        newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
        
        // 设置金额信息（转换当月上月末余额为0）
        newClaim.setLastMonthPrincipal(BigDecimal.ZERO);
        newClaim.setLastMonthInterest(BigDecimal.ZERO);
        newClaim.setLastMonthPenalty(BigDecimal.ZERO);
        newClaim.setCurrentMonthPrincipalIncreaseDecrease(originalBalance != null ? originalBalance : BigDecimal.ZERO);
        newClaim.setCurrentMonthInterestIncreaseDecrease(BigDecimal.ZERO);
        newClaim.setCurrentMonthPenaltyIncreaseDecrease(BigDecimal.ZERO);
        newClaim.setCurrentMonthPrincipal(originalBalance != null ? originalBalance : BigDecimal.ZERO);
        newClaim.setCurrentMonthInterest(BigDecimal.ZERO);
        newClaim.setCurrentMonthPenalty(BigDecimal.ZERO);
        // 记录互转增加金额（转入记录在增加字段）
        newClaim.setTransferIncreaseAmount(originalBalance != null ? originalBalance : BigDecimal.ZERO);
        
        // 使用字段映射工具复制必要字段（包括逾期年限、债权类别等）
        DebtFieldMappingUtil.copyLitigationToNonLitigation(sourceClaim, newClaim);
        
        String conversionRemark = String.format("【转换】从诉讼转换而来（%d年%d月）。%s", 
            request.getConversionYear(), request.getConversionMonth(), 
            request.getRemark() != null ? request.getRemark() : "");
        newClaim.setRemark(conversionRemark);
        
        nonLitigationClaimRepository.save(newClaim);
        return newClaim;
    }
    
    private LitigationClaim createLitigationRecord(DebtConversionRequestDTO request, NonLitigationClaim sourceClaim, 
            BigDecimal originalPrincipal, BigDecimal originalInterest, BigDecimal originalPenalty, BigDecimal totalBalance,
            BigDecimal lastMonthPrincipal, BigDecimal lastMonthInterest, BigDecimal lastMonthPenalty) {
        LitigationClaim newClaim = new LitigationClaim();
        
        // 设置复合主键
        LitigationCompositeKey key = new LitigationCompositeKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(request.getConversionYear());
        key.setMonth(request.getConversionMonth());
        newClaim.setId(key);
        
        // 设置基本信息
        // 序号字段由数据库自动生成，无需手动设置
        newClaim.setLitigationCase(request.getLitigationCase());
        newClaim.setSubjectName(sourceClaim.getSubjectName());
        newClaim.setManagementCompany(sourceClaim.getManagementCompany());
        newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
        
        // 设置债权金额（转换当月上月末余额为0）
        newClaim.setLastMonthDebtBalance(BigDecimal.ZERO);
        // 互转不算新增债权，不填写本月新增债权字段
        newClaim.setLitigationPrincipal(originalPrincipal != null ? originalPrincipal : BigDecimal.ZERO);
        newClaim.setLitigationInterest((originalInterest != null ? originalInterest : BigDecimal.ZERO)
            .add(originalPenalty != null ? originalPenalty : BigDecimal.ZERO));
        newClaim.setCurrentMonthDebtBalance(totalBalance != null ? totalBalance : BigDecimal.ZERO);
        // 记录互转增加金额（转入记录在增加字段）
        newClaim.setTransferIncreaseAmount(totalBalance != null ? totalBalance : BigDecimal.ZERO);
        
        // 设置诉讼特有字段
        newClaim.setLitigationOccurredPrincipal(request.getLitigationOccurredPrincipal());
        newClaim.setLitigationInterestFee(request.getLitigationInterestFee());
        newClaim.setLitigationFee(request.getLitigationFee());
        newClaim.setIntermediaryFee(request.getIntermediaryFee());
        
        // 使用字段映射工具复制必要字段（包括逾期年限、债权类别等）
        DebtFieldMappingUtil.copyNonLitigationToLitigation(sourceClaim, newClaim);
        
        String conversionRemark = String.format("【转换】从非诉讼转换而来（%d年%d月）。%s", 
            request.getConversionYear(), request.getConversionMonth(), 
            request.getRemark() != null ? request.getRemark() : "");
        newClaim.setRemark(conversionRemark);
        
        litigationClaimRepository.save(newClaim);
        return newClaim;
    }
    
    private void updateImpairmentReserveForConversion(String creditor, String debtor, Integer year, Integer month, 
            String period, String fromLitigation, String toLitigation, Object sourceRecord, String frontendCaseName) {
        try {
            // 查找原减值准备记录
            ImpairmentReserveKey oldKey = new ImpairmentReserveKey();
            oldKey.setCreditor(creditor);
            oldKey.setDebtor(debtor);
            oldKey.setYear(year);
            oldKey.setMonth(month);
            oldKey.setPeriod(period);
            oldKey.setIsLitigation(fromLitigation);
            
            Optional<ImpairmentReserve> reserveOpt = impairmentReserveRepository.findById(oldKey);
            if (reserveOpt.isPresent()) {
                ImpairmentReserve oldReserve = reserveOpt.get();
                
                // 创建新的减值准备记录（使用工具类进行字段映射）
                ImpairmentReserve newReserve = DebtFieldMappingUtil.createImpairmentReserveFromDebt(
                    creditor, debtor, year, month, period, toLitigation, sourceRecord, frontendCaseName);
                
                // 复制原有数据
                newReserve.setLastMonthBalance(oldReserve.getLastMonthBalance());
                newReserve.setCurrentMonthBalance(oldReserve.getCurrentMonthBalance());
                newReserve.setImpairmentAmount(oldReserve.getImpairmentAmount());
                newReserve.setInitialImpairmentDate(oldReserve.getInitialImpairmentDate());
                newReserve.setPreviousMonthBalance(oldReserve.getPreviousMonthBalance());
                newReserve.setCurrentMonthIncreaseDecrease(oldReserve.getCurrentMonthIncreaseDecrease());
                newReserve.setCurrentMonthAmount(oldReserve.getCurrentMonthAmount());
                newReserve.setAnnualRecoveryTarget(oldReserve.getAnnualRecoveryTarget());
                newReserve.setAnnualCumulativeRecovery(oldReserve.getAnnualCumulativeRecovery());
                newReserve.setRemark(oldReserve.getRemark());
                newReserve.setIsAllImpaired(oldReserve.getIsAllImpaired());
                newReserve.setCurrentMonthNewDebt(oldReserve.getCurrentMonthNewDebt());
                newReserve.setCurrentMonthDisposeDebt(oldReserve.getCurrentMonthDisposeDebt());
                
                // 先保存新记录，再删除旧记录（避免主键冲突）
                impairmentReserveRepository.save(newReserve);
                impairmentReserveRepository.delete(oldReserve);
                
                logger.info("减值准备表更新成功，是否涉诉从 {} 更新为 {}", fromLitigation, toLitigation);
            } else {
                logger.warn("未找到对应的减值准备记录，无法更新是否涉诉状态");
            }
        } catch (Exception e) {
            logger.error("更新减值准备表时发生错误", e);
            // 不抛出异常，避免影响主要转换流程
        }
    }
}