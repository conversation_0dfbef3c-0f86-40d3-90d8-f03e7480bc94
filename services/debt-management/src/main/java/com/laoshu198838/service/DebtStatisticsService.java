package com.laoshu198838.service;

import com.laoshu198838.model.overduedebt.dto.query.DebtStatisticsDTO;
import com.laoshu198838.model.overduedebt.dto.entity.DebtStatisticsDetailDTO;
import com.laoshu198838.model.datamonitor.dto.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 债权统计服务
 * 专门负责债权数据统计和一致性检查
 * 
 * <AUTHOR>
 */
@Service
public class DebtStatisticsService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtStatisticsService.class);
    
    @Autowired
    private OverdueDebtService overdueDebtService;
    
    @Autowired
    private DataConsistencyCheckService dataConsistencyCheckService;
    
    /**
     * 获取债权统计数据
     */
    public DebtStatisticsDTO getDebtStatistics(String year, String month, String company) {
        logger.info("获取债权统计数据: year={}, month={}, company={}", year, month, company);
        return overdueDebtService.getDebtStatistics(year, month, company);
    }
    
    /**
     * 获取债权统计详细数据
     */
    public DebtStatisticsDetailDTO getDebtStatisticsDetail(String year, String month, String company) {
        logger.info("获取债权统计详细数据: year={}, month={}, company={}", year, month, company);
        return overdueDebtService.getDebtStatisticsDetail(year, month, company);
    }
    
    /**
     * 检查新增表数据一致性
     */
    public AddTableConsistencyResult checkAddTableConsistency(String year, String month) {
        logger.info("检查新增表数据一致性: year={}, month={}", year, month);
        return dataConsistencyCheckService.checkAddTableConsistency(year, month);
    }
    
    /**
     * 检查处置表数据一致性
     */
    public DisposalTableConsistencyResult checkDisposalTableConsistency(String year, String month) {
        logger.info("检查处置表数据一致性: year={}, month={}", year, month);
        return dataConsistencyCheckService.checkDisposalTableConsistency(year, month);
    }
    
    /**
     * 检查债权变动数据一致性
     */
    public DebtChangeConsistencyResult checkDebtChangeConsistency(String year, String month) {
        logger.info("检查债权变动数据一致性: year={}, month={}", year, month);
        return dataConsistencyCheckService.checkDebtChangeConsistency(year, month);
    }
    
    /**
     * 获取数据总览
     */
    public DataOverviewResult getDataOverview(String year, String month) {
        logger.info("获取数据总览: year={}, month={}", year, month);
        return dataConsistencyCheckService.getDataOverview(year, month);
    }
    
    /**
     * 获取一致性检查明细
     */
    public List<ConsistencyDetailItem> getConsistencyDetail(String checkKey, String yearMonth) {
        logger.info("获取一致性检查明细: checkKey={}, yearMonth={}", checkKey, yearMonth);
        return dataConsistencyCheckService.getConsistencyDetail(checkKey, yearMonth);
    }
}