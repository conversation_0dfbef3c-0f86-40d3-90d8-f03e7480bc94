package com.laoshu198838.service;

import com.laoshu198838.model.overduedebt.dto.entity.DebtStatisticsDetailDTO;
import com.laoshu198838.model.overduedebt.dto.query.DebtStatisticsDTO;
import java.util.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.laoshu198838.repository.overdue_debt.OverdueDebtSummaryRepository;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDecreaseRepository;
import com.laoshu198838.repository.overdue_debt.CollectionTargetRepository;
import com.laoshu198838.entity.overdue_debt.CollectionTarget;
import com.laoshu198838.dto.CompanyCollectionProgressDTO;
import java.util.stream.Collectors;
import java.util.Set;
import java.util.HashSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class OverdueDebtService {

    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtService.class);
    
    private final OverdueDebtSummaryRepository overdueDebtSummaryRepository;
    private final OverdueDebtSummaryRepository overdueSummaryRepository; // 别名，用于新方法
    private final DebtDetailsExportRepository debtDetailsExportRepository;
    private final OverdueDebtDetailRepository overdueDebtDetailRepository;
    private final ImpairmentReserveRepository impairmentReserveRepository;
    private final OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;
    private final CollectionTargetRepository collectionTargetRepository;

    public OverdueDebtService(OverdueDebtSummaryRepository overdueDebtSummaryRepository,
                             DebtDetailsExportRepository debtDetailsExportRepository,
                             OverdueDebtDetailRepository overdueDebtDetailRepository,
                             ImpairmentReserveRepository impairmentReserveRepository,
                             OverdueDebtDecreaseRepository overdueDebtDecreaseRepository,
                             CollectionTargetRepository collectionTargetRepository) {
        this.overdueDebtSummaryRepository = overdueDebtSummaryRepository;
        this.overdueSummaryRepository = overdueDebtSummaryRepository; // 设置别名
        this.debtDetailsExportRepository = debtDetailsExportRepository;
        this.overdueDebtDetailRepository = overdueDebtDetailRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
        this.overdueDebtDecreaseRepository = overdueDebtDecreaseRepository;
        this.collectionTargetRepository = collectionTargetRepository;
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDTO getDebtStatistics(String year, String month, String company) {
        DebtStatisticsDTO dto = new DebtStatisticsDTO();
//        逾期债权总览数据
        dto.setTotalReductionAmount(overdueDebtSummaryRepository.findTotalReductionAmount());
        dto.setTotalDebtBalance(overdueDebtSummaryRepository.findTotalDebtBalance());
//        存量债权相关数据
        dto.setInitialDebtBalance(overdueDebtSummaryRepository.findInitialDebtBalance());
        dto.setInitialDebtReductionAmount(overdueDebtSummaryRepository.findInitialDebtReductionAmount());
        dto.setInitialDebtEndingBalance(overdueDebtSummaryRepository.findInitialDebtEndingBalance());
//      新增债权相关数据
        dto.setNewDebtAmount(overdueDebtSummaryRepository.findNewDebtAmount());
        dto.setNewDebtReductionAmount(overdueDebtSummaryRepository.findNewDebtReductionAmount());
        dto.setNewDebtBalance(overdueDebtSummaryRepository.findNewDebtBalance());
//        逾期债权按管理公司汇总数据
        dto.setNewDebtSummaryByCompany(new ArrayList<>());
        dto.setExistingDebtSummaryByCompany(new ArrayList<>());
//        逾期债权月度数据
        dto.setMonthNewReductionDebtByCompany(overdueDebtSummaryRepository.findMonthNewReductionDebtByCompany(year, month, company));

        return dto;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDetailDTO getDebtStatisticsDetail(String year, String month, String company) {

        DebtStatisticsDetailDTO detailDto = new DebtStatisticsDetailDTO();

        // 获取债务数据的详细信息
        detailDto.setNewDebtDetailList(debtDetailsExportRepository.findNewDebtDetailList(year, month, company));
        // 使用OverdueDebtDetailRepository来计算累计处置金额，而不是简单的每月处置金额
        detailDto.setReductionDebtDetailList(overdueDebtDetailRepository.findReductionDebtDetailList(year, month, company));

        return detailDto;
    }

    /**
     * 获取存量债权清收情况统计
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 清收情况数据
     */
    public Map<String, Object> getDebtCollectionStatus(String year, String month, String company) {
        Map<String, Object> result = new HashMap<>();
        
        // 解析年份和月份
        int yearInt = Integer.parseInt(year);
        int monthInt = Integer.parseInt(month.replace("月", ""));
        int previousYear = yearInt - 1;

        // 计算上个月的年份和月份（用于本月清收计算）
        int previousMonthYear = yearInt;
        int previousMonth = monthInt - 1;
        if (previousMonth <= 0) {
            previousMonth = 12;
            previousMonthYear = yearInt - 1;
        }

        logger.info("获取存量债权清收情况统计");

        // 查询本月数据（保持原有逻辑）
        List<Object[]> unifiedResults = null;
        try {
            unifiedResults = overdueDebtDecreaseRepository.findStockDebtCollectionUnifiedData(yearInt, monthInt, company);
        } catch (Exception e) {
            logger.error("查询失败: {}", e.getMessage(), e);
            unifiedResults = new ArrayList<>();
        }

        // 查询上个月数据（仅用于本月清收计算）
        List<Object[]> previousMonthResults = null;
        try {
            previousMonthResults = overdueDebtDecreaseRepository.findStockDebtCollectionUnifiedData(previousMonthYear, previousMonth, company);
        } catch (Exception e) {
            logger.error("上月查询失败: {}", e.getMessage(), e);
            previousMonthResults = new ArrayList<>();
        }

        // 从SQL查询结果中提取汇总数据和明细数据，保持前端兼容性
        BigDecimal totalYearBeginAmount = BigDecimal.ZERO;
        BigDecimal totalYearCumulativeAmount = BigDecimal.ZERO;
        BigDecimal totalPeriodEndAmount = BigDecimal.ZERO;

        // 明细数据列表
        List<Map<String, Object>> yearBeginDetails = new ArrayList<>();
        List<Map<String, Object>> monthCollectionDetails = new ArrayList<>();
        List<Map<String, Object>> yearCumulativeDetails = new ArrayList<>();
        List<Map<String, Object>> periodEndDetails = new ArrayList<>();

        if (unifiedResults != null && !unifiedResults.isEmpty()) {
            for (Object[] row : unifiedResults) {
                try {
                    // SQL返回字段：数据类型, 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间, 期初金额, 累计处置债权, 债权余额, 现金处置, 资产抵债, 分期还款, 其他方式, 备注
                    String managementCompany = row[1] != null ? row[1].toString() : "";
                    String creditor = row[2] != null ? row[2].toString() : "";
                    String debtor = row[3] != null ? row[3].toString() : "";
                    String isLitigation = row[4] != null ? row[4].toString() : "";
                    String period = row[6] != null ? row[6].toString() : "";

                    BigDecimal yearBeginAmount = row[7] != null ? new BigDecimal(row[7].toString()) : BigDecimal.ZERO;
                    BigDecimal cumulativeDisposalAmount = row[8] != null ? new BigDecimal(row[8].toString()) : BigDecimal.ZERO;
                    BigDecimal debtBalance = row[9] != null ? new BigDecimal(row[9].toString()) : BigDecimal.ZERO;

                    // 累加汇总数据
                    totalYearBeginAmount = totalYearBeginAmount.add(yearBeginAmount);
                    totalYearCumulativeAmount = totalYearCumulativeAmount.add(cumulativeDisposalAmount);
                    totalPeriodEndAmount = totalPeriodEndAmount.add(debtBalance);

                    // 构建明细数据 - 期初金额明细
                    if (yearBeginAmount.compareTo(BigDecimal.ZERO) > 0) {
                        Map<String, Object> yearBeginDetail = new HashMap<>();
                        yearBeginDetail.put("creditor", creditor);
                        yearBeginDetail.put("debtor", debtor);
                        yearBeginDetail.put("managementCompany", managementCompany);
                        yearBeginDetail.put("isLitigation", isLitigation);
                        yearBeginDetail.put("period", period);
                        yearBeginDetail.put("amount", yearBeginAmount);
                        yearBeginDetails.add(yearBeginDetail);
                    }

                    // 构建明细数据 - 本年累计处置明细
                    if (cumulativeDisposalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        Map<String, Object> yearCumulativeDetail = new HashMap<>();
                        yearCumulativeDetail.put("creditor", creditor);
                        yearCumulativeDetail.put("debtor", debtor);
                        yearCumulativeDetail.put("managementCompany", managementCompany);
                        yearCumulativeDetail.put("isLitigation", isLitigation);
                        yearCumulativeDetail.put("period", period);
                        yearCumulativeDetail.put("amount", cumulativeDisposalAmount);
                        yearCumulativeDetails.add(yearCumulativeDetail);
                    }

                    // 构建明细数据 - 期末余额明细
                    if (debtBalance.compareTo(BigDecimal.ZERO) > 0) {
                        Map<String, Object> periodEndDetail = new HashMap<>();
                        periodEndDetail.put("creditor", creditor);
                        periodEndDetail.put("debtor", debtor);
                        periodEndDetail.put("managementCompany", managementCompany);
                        periodEndDetail.put("isLitigation", isLitigation);
                        periodEndDetail.put("period", period);
                        periodEndDetail.put("amount", debtBalance);
                        periodEndDetails.add(periodEndDetail);
                    }

                } catch (Exception e) {
                    logger.error("处理数据行时出错: {}, 数据: {}", e.getMessage(), java.util.Arrays.toString(row));
                }
            }
        }

        // 计算本月清收明细：通过对比上月和本月数据
        monthCollectionDetails = calculateMonthCollectionDetails(unifiedResults, previousMonthResults);
        BigDecimal totalMonthCollectionAmount = monthCollectionDetails.stream()
            .map(detail -> (BigDecimal) detail.get("amount"))
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 返回前端期望的数据格式
        result.put("yearBeginAmount", totalYearBeginAmount);
        result.put("monthCollectionAmount", totalMonthCollectionAmount);
        result.put("yearCumulativeCollectionAmount", totalYearCumulativeAmount);
        result.put("periodEndAmount", totalPeriodEndAmount);

        // 返回明细数据
        result.put("yearBeginDetails", yearBeginDetails);
        result.put("monthCollectionDetails", monthCollectionDetails);
        result.put("yearCumulativeDetails", yearCumulativeDetails);
        result.put("periodEndDetails", periodEndDetails);


        logger.info("存量债权清收情况汇总完成");
        return result;
    }

    /**
     * 将查询结果转换为明细列表
     * @param queryResults 查询结果
     * @param type 数据类型
     * @return 明细列表
     */
    private List<Map<String, Object>> convertToDetailsList(List<Object[]> queryResults, String type) {
        List<Map<String, Object>> details = new ArrayList<>();
        
        for (Object[] row : queryResults) {
            Map<String, Object> detail = new HashMap<>();
            detail.put("creditor", row[0]); // 债权人
            detail.put("debtor", row[1]); // 债务人
            detail.put("managementCompany", row[2]); // 管理公司
            detail.put("isLitigation", row[3]); // 是否涉诉
            detail.put("period", row[4]); // 期间
            detail.put("amount", row[5]); // 金额
            detail.put("type", type); // 数据类型
            details.add(detail);
        }
        
        return details;
    }

    /**
     * 获取存量债权清收处置方式统计
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 处置方式统计数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getDebtDisposalMethods(String year, String month, String company) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));


            // 直接使用存量债权清收情况的查询方法
            List<Object[]> unifiedResults = overdueDebtDecreaseRepository.findStockDebtCollectionUnifiedData(yearInt, monthInt, company);

            if (unifiedResults != null && !unifiedResults.isEmpty()) {
                // 直接使用存量债权清收情况的汇总数据，确保数据一致性
                Map<String, Object> summaryData = calculateSummaryFromUnifiedData(unifiedResults);

                // 从汇总数据中提取处置方式金额
                BigDecimal totalCashDisposal = (BigDecimal) summaryData.getOrDefault("stockCashDisposal", BigDecimal.ZERO);
                BigDecimal totalAssetDebt = (BigDecimal) summaryData.getOrDefault("stockAssetDisposal", BigDecimal.ZERO);
                BigDecimal totalInstallment = (BigDecimal) summaryData.getOrDefault("stockInstallmentPayment", BigDecimal.ZERO);
                BigDecimal totalOtherWays = (BigDecimal) summaryData.getOrDefault("stockOtherDisposal", BigDecimal.ZERO);

                // 计算总金额
                BigDecimal totalAmount = totalCashDisposal.add(totalAssetDebt).add(totalInstallment).add(totalOtherWays);

                // 构建处置方式数据
                if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 现金处置
                    if (totalCashDisposal.compareTo(BigDecimal.ZERO) > 0) {
                        result.add(createMethodData("现金处置", totalCashDisposal, totalAmount));
                    }

                    // 资产抵债
                    if (totalAssetDebt.compareTo(BigDecimal.ZERO) > 0) {
                        result.add(createMethodData("资产抵债", totalAssetDebt, totalAmount));
                    }

                    // 分期还款
                    if (totalInstallment.compareTo(BigDecimal.ZERO) > 0) {
                        result.add(createMethodData("分期还款", totalInstallment, totalAmount));
                    }

                    // 其他方式
                    if (totalOtherWays.compareTo(BigDecimal.ZERO) > 0) {
                        result.add(createMethodData("其他方式", totalOtherWays, totalAmount));
                    }
                }
            }


        } catch (Exception e) {
            logger.error("获取处置方式统计失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 创建处置方式数据
     */
    private Map<String, Object> createMethodData(String method, BigDecimal amount, BigDecimal totalAmount) {
        Map<String, Object> methodData = new HashMap<>();
        methodData.put("method", method);
        // 数据已经是万元单位，直接使用，不需要再除以10000
        methodData.put("amount", amount);
        methodData.put("percentage", amount.multiply(new BigDecimal("100"))
                                          .divide(totalAmount, 2, RoundingMode.HALF_UP));
        return methodData;
    }

    /**
     * 获取各子公司存量债权回收完成情况
     * @param year 年份
     * @param month 月份
     * @return 公司回收进度数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getCompanyRecoveryProgress(String year, String month) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        
        return result;
    }

    // ==================== 新增债权统计相关方法 ====================

    /**
     * 获取新增债权情况统计（月度、年度）
     * 基于期间字段筛选2022年4月30日之后的债权数据
     * @param year 年份
     * @param month 月份（可选，为空表示年度统计）
     * @param company 公司
     * @return 新增债权趋势统计数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Map<String, Object> getNewDebtTrendStatistics(String year, String month, String company) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            Integer monthInt = null;
            if (month != null && !month.isEmpty() && !month.equals("全部")) {
                monthInt = Integer.parseInt(month.replace("月", ""));
            }
            
            // 新增债权基准日期：2022年4月30日
            String baselinePeriod = "2022-04-30";
            
            int lastYear = yearInt - 1;
            
            logger.info("获取新增债权情况统计: year={}, month={}, company={}", yearInt, monthInt, company);
            
            if (monthInt != null) {
                // 月度统计 - 使用新的参数化SQL查询
                List<Object[]> statisticsData = overdueDebtSummaryRepository.findNewDebtStatistics(
                    yearInt, monthInt, lastYear, company);
                
                List<Map<String, Object>> monthlyData = convertNewDebtStatisticsToMonthlyData(
                    statisticsData, yearInt, monthInt);
                
                result.put("type", "monthly");
                result.put("data", monthlyData);
            } else {
                // 年度统计 - 获取全年12个月的数据
                List<Map<String, Object>> yearlyData = new ArrayList<>();
                
                for (int m = 1; m <= 12; m++) {
                    List<Object[]> monthData = overdueDebtSummaryRepository.findNewDebtStatistics(
                        yearInt, m, lastYear, company);
                    
                    Map<String, Object> monthSummary = calculateMonthSummaryFromStatisticsData(
                        monthData, yearInt, m);
                    monthSummary.put("period", m + "月");
                    yearlyData.add(monthSummary);
                }
                
                result.put("type", "yearly");
                result.put("data", yearlyData);
            }
            
            // 计算汇总数据 - 使用实际SQL查询结果
            Map<String, Object> summaryData = calculateYearSummaryData(yearInt, monthInt, company);
            result.putAll(summaryData);
            
        } catch (Exception e) {
            logger.error("获取新增债权情况统计失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取各单位新增债权余额统计
     * @param year 年份
     * @param month 月份
     * @param company 公司（可选，用于筛选特定公司）
     * @return 各单位新增债权余额列表
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtBalanceByCompany(String year, String month, String company) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));
            
            logger.info("获取各单位新增债权余额统计: year={}, month={}, company={}", yearInt, monthInt, company);
            
            // 使用新的参数化SQL查询
            List<Object[]> companyStatistics = overdueDebtSummaryRepository.findNewDebtStatisticsByCompany(
                yearInt, monthInt, company);
            
            // 转换查询结果为前端需要的格式
            for (Object[] row : companyStatistics) {
                if (row.length >= 6) {
                    Map<String, Object> companyData = new HashMap<>();
                    
                    String companyName = row[0] != null ? row[0].toString() : "";
                    BigDecimal cumulativeNewAmount = parseBigDecimal(row[1], "累计新增金额");
                    BigDecimal cumulativeReductionAmount = parseBigDecimal(row[2], "累计处置金额");
                    BigDecimal currentBalance = parseBigDecimal(row[3], "当前债权余额");
                    BigDecimal monthNewAmount = parseBigDecimal(row[4], "本月新增金额");
                    Integer recordCount = row[5] != null ? Integer.parseInt(row[5].toString()) : 0;
                    
                    // 计算处置率
                    BigDecimal reductionRate = BigDecimal.ZERO;
                    if (cumulativeNewAmount.compareTo(BigDecimal.ZERO) > 0) {
                        reductionRate = cumulativeReductionAmount.divide(cumulativeNewAmount, 4, RoundingMode.HALF_UP)
                                      .multiply(new BigDecimal(100));
                    }
                    
                    companyData.put("company", companyName);
                    companyData.put("newDebtAmount", cumulativeNewAmount);
                    companyData.put("reductionAmount", cumulativeReductionAmount);
                    companyData.put("balance", currentBalance);
                    companyData.put("reductionRate", reductionRate);
                    companyData.put("monthNewAmount", monthNewAmount);
                    companyData.put("recordCount", recordCount);
                    result.add(companyData);
                }
            }
            
            // 按余额降序排序
            result.sort((a, b) -> {
                BigDecimal balanceA = (BigDecimal) a.get("balance");
                BigDecimal balanceB = (BigDecimal) b.get("balance");
                return balanceB.compareTo(balanceA);
            });
            
        } catch (Exception e) {
            logger.error("获取各单位新增债权余额统计失败", e);
        }
        
        return result;
    }

    /**
     * 获取各子公司新增债权回收完成情况对比
     * @param year 年份
     * @param month 月份
     * @return 各子公司新增债权回收完成情况
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtRecoveryComparison(String year, String month) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));
            
            logger.info("获取各子公司新增债权回收完成情况: year={}, month={}", yearInt, monthInt);
            
            // 使用新的参数化SQL查询获取各公司新增债权统计数据
            List<Object[]> companyStatistics = overdueDebtSummaryRepository.findNewDebtStatisticsByCompany(
                yearInt, monthInt, "全部");
            
            // 转换查询结果为前端需要的格式
            for (Object[] row : companyStatistics) {
                if (row.length >= 6) {
                    Map<String, Object> companyData = new HashMap<>();
                    
                    String companyName = row[0] != null ? row[0].toString() : "";
                    BigDecimal cumulativeNewAmount = parseBigDecimal(row[1], "累计新增金额");
                    BigDecimal cumulativeReductionAmount = parseBigDecimal(row[2], "累计处置金额");
                    BigDecimal currentBalance = parseBigDecimal(row[3], "当前债权余额");
                    BigDecimal monthNewAmount = parseBigDecimal(row[4], "本月新增金额");
                    
                    // 计算回收完成率
                    BigDecimal completionRate = BigDecimal.ZERO;
                    if (cumulativeNewAmount.compareTo(BigDecimal.ZERO) > 0) {
                        completionRate = cumulativeReductionAmount.divide(cumulativeNewAmount, 4, RoundingMode.HALF_UP)
                                       .multiply(new BigDecimal(100));
                    }
                    
                    // 假设目标为期初新增金额的60%
                    BigDecimal targetAmount = cumulativeNewAmount.multiply(new BigDecimal("0.6"));
                    
                    companyData.put("company", companyName);
                    companyData.put("targetAmount", targetAmount);
                    companyData.put("actualAmount", cumulativeReductionAmount);
                    companyData.put("completionRate", completionRate);
                    companyData.put("monthlyAmount", monthNewAmount);
                    companyData.put("yearlyAmount", cumulativeReductionAmount);
                    companyData.put("remainingAmount", currentBalance);
                    result.add(companyData);
                }
            }
            
            // 按完成率降序排序
            result.sort((a, b) -> {
                BigDecimal rateA = (BigDecimal) a.get("completionRate");
                BigDecimal rateB = (BigDecimal) b.get("completionRate");
                return rateB.compareTo(rateA);
            });
            
        } catch (Exception e) {
            logger.error("获取各子公司新增债权回收完成情况失败", e);
        }
        
        return result;
    }

    // ==================== 新增债权统计辅助方法 ====================
    
    /**
     * 将新增债权统计数据转换为月度数据格式
     * @param statisticsData SQL查询结果
     * @param year 年份
     * @param month 月份
     * @return 月度数据列表
     */
    private List<Map<String, Object>> convertNewDebtStatisticsToMonthlyData(
            List<Object[]> statisticsData, int year, int month) {
        List<Map<String, Object>> monthlyData = new ArrayList<>();
        
        BigDecimal totalNewAmount = BigDecimal.ZERO;
        BigDecimal totalReductionAmount = BigDecimal.ZERO;
        BigDecimal totalBalance = BigDecimal.ZERO;
        
        for (Object[] row : statisticsData) {
            if (row.length >= 15) {
                String dataType = row[0] != null ? row[0].toString() : "";
                
                // 只统计当月新增债权数据
                if ("当月新增债权".equals(dataType)) {
                    BigDecimal newAmount = parseBigDecimal(row[7], "新增债权");
                    BigDecimal reductionAmount = parseBigDecimal(row[8], "累计处置债权");
                    BigDecimal balance = parseBigDecimal(row[9], "债权余额");
                    
                    totalNewAmount = totalNewAmount.add(newAmount);
                    totalReductionAmount = totalReductionAmount.add(reductionAmount);
                    totalBalance = totalBalance.add(balance);
                }
            }
        }
        
        Map<String, Object> monthData = new HashMap<>();
        monthData.put("period", year + "年" + month + "月");
        monthData.put("newAmount", totalNewAmount);
        monthData.put("reductionAmount", totalReductionAmount);
        monthData.put("balance", totalBalance);
        monthlyData.add(monthData);
        
        return monthlyData;
    }
    
    /**
     * 从统计数据中计算月度汇总
     * @param statisticsData SQL查询结果
     * @param year 年份
     * @param month 月份
     * @return 月度汇总数据
     */
    private Map<String, Object> calculateMonthSummaryFromStatisticsData(
            List<Object[]> statisticsData, int year, int month) {
        Map<String, Object> summary = new HashMap<>();
        
        BigDecimal newAmount = BigDecimal.ZERO;
        BigDecimal reductionAmount = BigDecimal.ZERO;
        BigDecimal balance = BigDecimal.ZERO;
        
        for (Object[] row : statisticsData) {
            if (row.length >= 15) {
                String dataType = row[0] != null ? row[0].toString() : "";
                
                if ("当月新增债权".equals(dataType)) {
                    newAmount = newAmount.add(parseBigDecimal(row[7], "新增债权"));
                    reductionAmount = reductionAmount.add(parseBigDecimal(row[8], "累计处置债权"));
                    balance = balance.add(parseBigDecimal(row[9], "债权余额"));
                }
            }
        }
        
        summary.put("newAmount", newAmount);
        summary.put("reductionAmount", reductionAmount);
        summary.put("balance", balance);
        
        return summary;
    }
    
    /**
     * 计算年度汇总数据
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 年度汇总数据
     */
    private Map<String, Object> calculateYearSummaryData(int year, Integer month, String company) {
        Map<String, Object> summaryData = new HashMap<>();
        
        try {
            // 获取年度汇总统计数据
            int targetMonth = month != null ? month : 12; // 如果没有指定月份，使用12月
            List<Object[]> yearData = overdueDebtSummaryRepository.findNewDebtStatisticsByCompany(
                year, targetMonth, company);
            
            BigDecimal totalNewAmount = BigDecimal.ZERO;
            BigDecimal totalReductionAmount = BigDecimal.ZERO;
            BigDecimal currentBalance = BigDecimal.ZERO;
            
            for (Object[] row : yearData) {
                if (row.length >= 4) {
                    totalNewAmount = totalNewAmount.add(parseBigDecimal(row[1], "累计新增金额"));
                    totalReductionAmount = totalReductionAmount.add(parseBigDecimal(row[2], "累计处置金额"));
                    currentBalance = currentBalance.add(parseBigDecimal(row[3], "当前债权余额"));
                }
            }
            
            summaryData.put("totalNewAmount", totalNewAmount);
            summaryData.put("totalReductionAmount", totalReductionAmount);
            summaryData.put("currentBalance", currentBalance);
            
        } catch (Exception e) {
            logger.error("计算年度汇总数据失败: {}", e.getMessage(), e);
            summaryData.put("totalNewAmount", BigDecimal.ZERO);
            summaryData.put("totalReductionAmount", BigDecimal.ZERO);
            summaryData.put("currentBalance", BigDecimal.ZERO);
        }
        
        return summaryData;
    }

    /**
     * 获取新增债权处置方式统计
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 处置方式统计数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getNewDebtDisposalMethodStatistics(String year, String month, String company) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));
            
            logger.info("获取新增债权处置方式统计: year={}, month={}, company={}", yearInt, monthInt, company);
            
            // 使用Repository查询新增债权处置方式统计
            List<Object[]> disposalMethodsData = overdueDebtSummaryRepository.findNewDebtDisposalMethodStats(
                yearInt, monthInt, company);
            
            // 计算总金额
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (Object[] row : disposalMethodsData) {
                if (row.length >= 3) {
                    BigDecimal amount = parseBigDecimal(row[1], "处置金额");
                    totalAmount = totalAmount.add(amount);
                }
            }
            
            // 构建处置方式数据
            for (Object[] row : disposalMethodsData) {
                if (row.length >= 3) {
                    Map<String, Object> methodData = new HashMap<>();
                    
                    String method = row[0] != null ? row[0].toString() : "";
                    BigDecimal amount = parseBigDecimal(row[1], "处置金额");
                    Integer recordCount = row[2] != null ? Integer.parseInt(row[2].toString()) : 0;
                    
                    // 计算占比
                    BigDecimal percentage = BigDecimal.ZERO;
                    if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        percentage = amount.multiply(new BigDecimal("100"))
                                         .divide(totalAmount, 2, RoundingMode.HALF_UP);
                    }
                    
                    methodData.put("method", method);
                    methodData.put("amount", amount);
                    methodData.put("percentage", percentage);
                    methodData.put("recordCount", recordCount);
                    result.add(methodData);
                }
            }
            
            // 按金额降序排序
            result.sort((a, b) -> {
                BigDecimal amountA = (BigDecimal) a.get("amount");
                BigDecimal amountB = (BigDecimal) b.get("amount");
                return amountB.compareTo(amountA);
            });
            
            logger.info("新增债权处置方式统计获取成功: year={}, month={}, company={}, count={}", 
                       yearInt, monthInt, company, result.size());
            
        } catch (Exception e) {
            logger.error("获取新增债权处置方式统计失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 安全解析BigDecimal，处理各种数据类型和格式
     */
    private BigDecimal parseBigDecimal(Object value, String fieldName) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        try {
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            } else if (value instanceof Number) {
                return new BigDecimal(value.toString());
            } else {
                String strValue = value.toString().trim();
                if (strValue.isEmpty() || "null".equalsIgnoreCase(strValue)) {
                    return BigDecimal.ZERO;
                }

                // 处理科学计数法格式
                if (strValue.toLowerCase().contains("e")) {
                    return new BigDecimal(strValue);
                }

                // 处理普通数字格式
                return new BigDecimal(strValue);
            }
        } catch (NumberFormatException e) {
            logger.error("解析字段 {} 失败，值: {} (类型: {}), 错误: {}",
                        fieldName, value, value.getClass().getSimpleName(), e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 从统一查询数据计算汇总结果
     * @param unifiedResults 统一查询结果
     * @return 计算后的汇总数据
     */
    private Map<String, Object> calculateSummaryFromUnifiedData(List<Object[]> unifiedResults) {
        Map<String, Object> summary = new HashMap<>();
        
        BigDecimal yearBeginAmount = BigDecimal.ZERO;
        BigDecimal monthCollectionAmount = BigDecimal.ZERO;
        BigDecimal yearCumulativeCollectionAmount = BigDecimal.ZERO;
        BigDecimal periodEndAmount = BigDecimal.ZERO;
        
        BigDecimal stockCashDisposal = BigDecimal.ZERO;
        BigDecimal stockInstallmentPayment = BigDecimal.ZERO;
        BigDecimal stockAssetDisposal = BigDecimal.ZERO;
        BigDecimal stockOtherDisposal = BigDecimal.ZERO;
        
        
        for (Object[] row : unifiedResults) {
            // 新的SQL返回字段顺序：数据类型,管理公司,债权人,债务人,是否涉诉,科目名称,期间,期初金额,累计处置债权,债权余额,现金处置,资产抵债,分期还款,其他方式,备注
            if (row.length >= 15) {
                // 解析字段
                BigDecimal 期初金额 = parseBigDecimal(row[7], "期初金额");
                BigDecimal 累计处置债权 = parseBigDecimal(row[8], "累计处置债权"); 
                BigDecimal 债权余额 = parseBigDecimal(row[9], "债权余额");
                BigDecimal 现金处置 = parseBigDecimal(row[10], "现金处置");
                BigDecimal 资产抵债 = parseBigDecimal(row[11], "资产抵债");
                BigDecimal 分期还款 = parseBigDecimal(row[12], "分期还款");
                BigDecimal 其他方式 = parseBigDecimal(row[13], "其他方式");
                
                
                // 累加期初金额（所有记录）
                yearBeginAmount = yearBeginAmount.add(期初金额);
                
                // 累加本年累计清收金额（所有记录）
                yearCumulativeCollectionAmount = yearCumulativeCollectionAmount.add(累计处置债权);
                
                // 累加期末余额（债权余额，所有记录）
                periodEndAmount = periodEndAmount.add(债权余额);
                
                // 累加处置方式金额（按比例分配存量债权的处置）
                if (累计处置债权.compareTo(BigDecimal.ZERO) > 0) {
                    // 计算存量债权处置的比例
                    BigDecimal totalDisposal = 现金处置.add(资产抵债).add(分期还款).add(其他方式);
                    if (totalDisposal.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal ratio = 累计处置债权.divide(totalDisposal, 10, RoundingMode.HALF_UP);
                        stockCashDisposal = stockCashDisposal.add(现金处置.multiply(ratio));
                        stockAssetDisposal = stockAssetDisposal.add(资产抵债.multiply(ratio));
                        stockInstallmentPayment = stockInstallmentPayment.add(分期还款.multiply(ratio));
                        stockOtherDisposal = stockOtherDisposal.add(其他方式.multiply(ratio));
                    }
                }
            }
        }
        
        // 计算本月清收金额
        monthCollectionAmount = BigDecimal.ZERO;
        
        
        summary.put("yearBeginAmount", yearBeginAmount);
        summary.put("monthCollectionAmount", monthCollectionAmount);
        summary.put("yearCumulativeCollectionAmount", yearCumulativeCollectionAmount);
        summary.put("periodEndAmount", periodEndAmount);
        summary.put("stockCashDisposal", stockCashDisposal);
        summary.put("stockInstallmentPayment", stockInstallmentPayment);
        summary.put("stockAssetDisposal", stockAssetDisposal);
        summary.put("stockOtherDisposal", stockOtherDisposal);
        
        return summary;
    }

    /**
     * 将统一查询数据转换为明细数据
     * @param unifiedResults 统一查询结果
     * @return 分类明细数据
     */
    private Map<String, List<Map<String, Object>>> convertUnifiedDataToDetails(List<Object[]> unifiedResults) {
        Map<String, List<Map<String, Object>>> detailsMap = new HashMap<>();
        
        List<Map<String, Object>> yearBeginDetails = new ArrayList<>();
        List<Map<String, Object>> monthCollectionDetails = new ArrayList<>();
        List<Map<String, Object>> yearCumulativeDetails = new ArrayList<>();
        List<Map<String, Object>> periodEndDetails = new ArrayList<>();
        
        for (Object[] row : unifiedResults) {
            // 新的SQL返回字段顺序：数据类型,管理公司,债权人,债务人,是否涉诉,科目名称,期间,期初金额,累计处置债权,债权余额,现金处置,资产抵债,分期还款,其他方式,备注
            if (row.length >= 15) {
                String dataType = row[0] != null ? row[0].toString() : "";
                String managementCompany = row[1] != null ? row[1].toString() : "";
                String creditor = row[2] != null ? row[2].toString() : "";
                String debtor = row[3] != null ? row[3].toString() : "";
                String isLitigation = row[4] != null ? row[4].toString() : "";
                String subjectName = row[5] != null ? row[5].toString() : "";
                String period = row[6] != null ? row[6].toString() : "";
                
                BigDecimal 期初金额 = parseBigDecimal(row[7], "期初金额");
                BigDecimal 累计处置债权 = parseBigDecimal(row[8], "累计处置债权");
                BigDecimal 债权余额 = parseBigDecimal(row[9], "债权余额");
                
                // 期初金额明细
                if (期初金额.compareTo(BigDecimal.ZERO) > 0) {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("managementCompany", managementCompany);
                    detail.put("creditor", creditor);
                    detail.put("debtor", debtor);
                    detail.put("isLitigation", isLitigation);
                    detail.put("period", period);
                    detail.put("amount", 期初金额);
                    yearBeginDetails.add(detail);
                }
                
                // 本月清收金额明细（需要单独计算）
                
                // 本年累计清收金额明细
                if (累计处置债权.compareTo(BigDecimal.ZERO) > 0) {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("managementCompany", managementCompany);
                    detail.put("creditor", creditor);
                    detail.put("debtor", debtor);
                    detail.put("isLitigation", isLitigation);
                    detail.put("period", period);
                    detail.put("amount", 累计处置债权);
                    yearCumulativeDetails.add(detail);
                }
                
                // 期末余额明细（直接使用SQL计算的债权余额）
                if (债权余额.compareTo(BigDecimal.ZERO) > 0) {
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("managementCompany", managementCompany);
                    detail.put("creditor", creditor);
                    detail.put("debtor", debtor);
                    detail.put("isLitigation", isLitigation);
                    detail.put("period", period);
                    detail.put("amount", 债权余额);
                    periodEndDetails.add(detail);
                }
            }
        }
        
        detailsMap.put("yearBeginDetails", yearBeginDetails);
        detailsMap.put("monthCollectionDetails", monthCollectionDetails);
        detailsMap.put("yearCumulativeDetails", yearCumulativeDetails);
        detailsMap.put("periodEndDetails", periodEndDetails);
        
        return detailsMap;
    }

    /**
     * 构建数据映射表，用于快速查找和对比
     * @param queryResults SQL查询结果
     * @return 数据映射表，key为匹配键，value为数据详情
     */
    private Map<String, Map<String, Object>> buildDataMap(List<Object[]> queryResults) {
        Map<String, Map<String, Object>> dataMap = new HashMap<>();

        if (queryResults == null || queryResults.isEmpty()) {
            return dataMap;
        }

        for (Object[] row : queryResults) {
            try {
                // SQL返回字段：数据类型, 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间, 期初金额, 累计处置债权, 债权余额, 现金处置, 资产抵债, 分期还款, 其他方式, 备注
                String managementCompany = row[1] != null ? row[1].toString() : "";
                String creditor = row[2] != null ? row[2].toString() : "";
                String debtor = row[3] != null ? row[3].toString() : "";
                String isLitigation = row[4] != null ? row[4].toString() : "";
                String period = row[6] != null ? row[6].toString() : "";
                BigDecimal debtBalance = row[9] != null ? new BigDecimal(row[9].toString()) : BigDecimal.ZERO;

                // 构建匹配键：债权人+债务人+管理公司+是否涉诉+期间
                String matchKey = String.join("|", creditor, debtor, managementCompany, isLitigation, period);

                // 构建数据详情
                Map<String, Object> dataDetail = new HashMap<>();
                dataDetail.put("creditor", creditor);
                dataDetail.put("debtor", debtor);
                dataDetail.put("managementCompany", managementCompany);
                dataDetail.put("isLitigation", isLitigation);
                dataDetail.put("period", period);
                dataDetail.put("amount", debtBalance);

                dataMap.put(matchKey, dataDetail);

            } catch (Exception e) {
                logger.error("构建数据映射时出错: {}, 数据: {}", e.getMessage(), java.util.Arrays.toString(row));
            }
        }

        return dataMap;
    }

    /**
     * 计算本月清收明细：通过对比上月和本月的期末余额数据
     * @param currentMonthResults 本月数据
     * @param previousMonthResults 上月数据
     * @return 本月清收明细列表
     */
    private List<Map<String, Object>> calculateMonthCollectionDetails(List<Object[]> currentMonthResults, List<Object[]> previousMonthResults) {
        List<Map<String, Object>> monthCollectionDetails = new ArrayList<>();

        // 构建数据映射表
        Map<String, Map<String, Object>> currentMonthMap = buildDataMap(currentMonthResults);
        Map<String, Map<String, Object>> previousMonthMap = buildDataMap(previousMonthResults);


        // 遍历上月数据，计算清收金额
        for (Map.Entry<String, Map<String, Object>> entry : previousMonthMap.entrySet()) {
            String matchKey = entry.getKey();
            Map<String, Object> previousData = entry.getValue();
            BigDecimal previousAmount = (BigDecimal) previousData.get("amount");

            // 查找本月对应数据
            Map<String, Object> currentData = currentMonthMap.get(matchKey);
            BigDecimal currentAmount = BigDecimal.ZERO;
            if (currentData != null) {
                currentAmount = (BigDecimal) currentData.get("amount");
            }

            // 计算清收金额：上月余额 - 本月余额
            BigDecimal collectionAmount = previousAmount.subtract(currentAmount);

            // 只有清收金额 > 0 的才加入明细
            if (collectionAmount.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, Object> collectionDetail = new HashMap<>();
                collectionDetail.put("creditor", previousData.get("creditor"));
                collectionDetail.put("debtor", previousData.get("debtor"));
                collectionDetail.put("managementCompany", previousData.get("managementCompany"));
                collectionDetail.put("isLitigation", previousData.get("isLitigation"));
                collectionDetail.put("period", previousData.get("period"));
                collectionDetail.put("amount", collectionAmount);
                collectionDetail.put("type", "本月清收");

                monthCollectionDetails.add(collectionDetail);
            }
        }

        return monthCollectionDetails;
    }

    /**
     * 获取存量债权处置方式详细记录
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 详细处置记录列表
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<Map<String, Object>> getDebtDisposalDetails(String year, String month, String company) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));


            // 直接使用存量债权清收情况的查询方法
            List<Object[]> unifiedResults = overdueDebtDecreaseRepository.findStockDebtCollectionUnifiedData(yearInt, monthInt, company);

            for (Object[] row : unifiedResults) {
                if (row.length >= 15) {
                    // SQL返回字段：数据类型,管理公司,债权人,债务人,是否涉诉,科目名称,期间,期初金额,累计处置债权,债权余额,现金处置,资产抵债,分期还款,其他方式,备注
                    BigDecimal cashDisposal = parseBigDecimal(row[10], "现金处置");
                    BigDecimal assetDebt = parseBigDecimal(row[11], "资产抵债");
                    BigDecimal installment = parseBigDecimal(row[12], "分期还款");
                    BigDecimal otherWays = parseBigDecimal(row[13], "其他方式");
                    BigDecimal total = cashDisposal.add(assetDebt).add(installment).add(otherWays);

                    // 只显示有处置金额的记录
                    if (total.compareTo(BigDecimal.ZERO) > 0) {
                        Map<String, Object> detail = new HashMap<>();
                        detail.put("managementCompany", row[1]);        // 管理公司
                        detail.put("creditor", row[2]);                 // 债权人
                        detail.put("debtor", row[3]);                   // 债务人
                        detail.put("isLitigation", row[4]);             // 是否涉诉
                        detail.put("subjectName", row[5]);              // 科目名称
                        detail.put("period", row[6]);                   // 期间
                        detail.put("cashDisposal", cashDisposal);       // 现金处置
                        detail.put("assetDebt", assetDebt);             // 资产抵债
                        detail.put("installmentRepayment", installment); // 分期还款
                        detail.put("otherWays", otherWays);             // 其他方式
                        detail.put("total", total);                     // 合计
                        detail.put("remark", row[14]);                  // 备注

                        result.add(detail);
                    }
                }
            }


        } catch (Exception e) {
            logger.error("获取处置方式详细记录失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 获取各子公司年度清收目标完成情况
     * 
     * @param year 年份
     * @param month 月份
     * @param displayMode 展示模式（INITIAL-按期初金额，TARGET-按清收目标）
     * @return 各公司清收进度数据
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<CompanyCollectionProgressDTO> getCompanyCollectionProgress(String year, String month, String displayMode) {
        
        List<CompanyCollectionProgressDTO> result = new ArrayList<>();
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month.replace("月", ""));
            
            // 获取所有公司的存量债权清收数据
            List<Object[]> collectionData = overdueDebtDecreaseRepository.findStockDebtCollectionUnifiedData(yearInt, monthInt, "全部");
            
            // 获取所有公司的清收目标数据
            List<CollectionTarget> targets = collectionTargetRepository.findByYear(yearInt);
            
            // 按管理公司汇总存量债权数据
            Map<String, CompanyCollectionSummary> companySummaries = aggregateCollectionDataByCompany(collectionData);
            
            // 创建目标数据映射
            Map<String, CollectionTarget> targetMap = targets.stream()
                .collect(Collectors.toMap(CollectionTarget::getManagementCompany, target -> target));
            
            // 合并数据并构建结果
            CompanyCollectionProgressDTO.DisplayMode mode = 
                "TARGET".equalsIgnoreCase(displayMode) ? CompanyCollectionProgressDTO.DisplayMode.TARGET 
                : CompanyCollectionProgressDTO.DisplayMode.INITIAL;
            
            Set<String> allCompanies = new HashSet<>(companySummaries.keySet());
            allCompanies.addAll(targetMap.keySet());
            
            for (String company : allCompanies) {
                if ("全部".equals(company) || company == null || company.trim().isEmpty()) {
                    continue; // 跳过汇总行和无效公司名
                }
                
                CompanyCollectionSummary summary = companySummaries.get(company);
                CollectionTarget target = targetMap.get(company);
                
                CompanyCollectionProgressDTO dto = CompanyCollectionProgressDTO.builder()
                    .managementCompany(company)
                    .yearBeginAmount(summary != null ? summary.getYearBeginAmount() : BigDecimal.ZERO)
                    .collectionTargetAmount(target != null ? target.getTargetAmount() : null)
                    .cumulativeCollectionAmount(summary != null ? summary.getCumulativeCollectionAmount() : BigDecimal.ZERO)
                    .hasTarget(target != null && target.hasTargetAmount())
                    .build();
                
                // 根据展示模式重新计算相关字段
                dto.recalculateByMode(mode);
                
                // 只添加有意义的数据（至少有期初金额或清收目标）
                if (dto.getYearBeginAmount().compareTo(BigDecimal.ZERO) > 0 
                    || (dto.getCollectionTargetAmount() != null && dto.getCollectionTargetAmount().compareTo(BigDecimal.ZERO) > 0)) {
                    result.add(dto);
                }
            }
            
            // 按完成进度降序排序
            result.sort((a, b) -> b.getCompletionRate().compareTo(a.getCompletionRate()));
            
            
        } catch (Exception e) {
            logger.error("获取各子公司年度清收目标完成情况失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取公司清收进度数据失败: " + e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 按管理公司汇总存量债权清收数据
     */
    private Map<String, CompanyCollectionSummary> aggregateCollectionDataByCompany(List<Object[]> collectionData) {
        Map<String, CompanyCollectionSummary> summaries = new HashMap<>();
        
        if (collectionData == null || collectionData.isEmpty()) {
            return summaries;
        }
        
        for (Object[] row : collectionData) {
            try {
                // SQL返回字段：数据类型, 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间, 期初金额, 累计处置债权, 债权余额, ...
                String managementCompany = row[1] != null ? row[1].toString().trim() : "";
                if (managementCompany.isEmpty() || "全部".equals(managementCompany)) {
                    continue;
                }
                
                BigDecimal yearBeginAmount = row[7] != null ? new BigDecimal(row[7].toString()) : BigDecimal.ZERO;
                BigDecimal cumulativeDisposalAmount = row[8] != null ? new BigDecimal(row[8].toString()) : BigDecimal.ZERO;
                
                CompanyCollectionSummary summary = summaries.computeIfAbsent(managementCompany, 
                    k -> new CompanyCollectionSummary(k));
                
                summary.addYearBeginAmount(yearBeginAmount);
                summary.addCumulativeCollectionAmount(cumulativeDisposalAmount);
                
            } catch (Exception e) {
                logger.warn("处理管理公司汇总数据时出错: {}, 数据行: {}", e.getMessage(), java.util.Arrays.toString(row));
            }
        }
        
        return summaries;
    }
    
    /**
     * 公司清收汇总数据内部类
     */
    private static class CompanyCollectionSummary {
        private final String managementCompany;
        private BigDecimal yearBeginAmount = BigDecimal.ZERO;
        private BigDecimal cumulativeCollectionAmount = BigDecimal.ZERO;
        
        public CompanyCollectionSummary(String managementCompany) {
            this.managementCompany = managementCompany;
        }
        
        public void addYearBeginAmount(BigDecimal amount) {
            if (amount != null) {
                this.yearBeginAmount = this.yearBeginAmount.add(amount);
            }
        }
        
        public void addCumulativeCollectionAmount(BigDecimal amount) {
            if (amount != null) {
                this.cumulativeCollectionAmount = this.cumulativeCollectionAmount.add(amount);
            }
        }
        
        public String getManagementCompany() { return managementCompany; }
        public BigDecimal getYearBeginAmount() { return yearBeginAmount; }
        public BigDecimal getCumulativeCollectionAmount() { return cumulativeCollectionAmount; }
    }

}
