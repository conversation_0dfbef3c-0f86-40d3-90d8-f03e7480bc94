-- 为asset_basic_info表添加面积分配字段
-- 执行时间：2025-08-04

USE financial_system;

-- 添加当前自用面积字段
ALTER TABLE asset_basic_info 
ADD COLUMN current_self_use_area DECIMAL(15,2) DEFAULT 0.00 COMMENT '当前自用面积(平方米)';

-- 添加当前出租面积字段
ALTER TABLE asset_basic_info 
ADD COLUMN current_rental_area DECIMAL(15,2) DEFAULT 0.00 COMMENT '当前出租面积(平方米)';

-- 添加当前闲置面积字段
ALTER TABLE asset_basic_info 
ADD COLUMN current_idle_area DECIMAL(15,2) DEFAULT 0.00 COMMENT '当前闲置面积(平方米)';

-- 更新现有记录的面积分配（示例数据）
UPDATE asset_basic_info 
SET 
    current_self_use_area = total_area * 0.7,
    current_rental_area = total_area * 0.3,
    current_idle_area = 0
WHERE current_self_use_area IS NULL;

-- 验证更新结果
SELECT 
    id,
    asset_name,
    total_area,
    current_self_use_area,
    current_rental_area,
    current_idle_area,
    (current_self_use_area + current_rental_area + current_idle_area) as total_allocated_area
FROM asset_basic_info
WHERE status = 'ACTIVE';

-- 添加约束检查（可选）
-- ALTER TABLE asset_basic_info 
-- ADD CONSTRAINT chk_area_allocation 
-- CHECK (current_self_use_area + current_rental_area + current_idle_area <= total_area);
