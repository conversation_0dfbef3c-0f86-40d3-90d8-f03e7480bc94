-- 为asset_basic_info表添加面积分配字段
-- 执行时间：2025-08-04
-- 修正版本：使用正确的数据库名称

USE asset_management;

-- 检查字段是否已存在
SELECT 
    COLUMN_NAME 
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'asset_management' 
    AND TABLE_NAME = 'asset_basic_info' 
    AND COLUMN_NAME IN ('current_self_use_area', 'current_rental_area', 'current_idle_area');

-- 添加当前自用面积字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'asset_management' 
     AND TABLE_NAME = 'asset_basic_info' 
     AND COLUMN_NAME = 'current_self_use_area') = 0,
    'ALTER TABLE asset_basic_info ADD COLUMN current_self_use_area DECIMAL(15,2) DEFAULT 0.00 COMMENT ''当前自用面积(平方米)''',
    'SELECT ''Column current_self_use_area already exists'' AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加当前出租面积字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'asset_management' 
     AND TABLE_NAME = 'asset_basic_info' 
     AND COLUMN_NAME = 'current_rental_area') = 0,
    'ALTER TABLE asset_basic_info ADD COLUMN current_rental_area DECIMAL(15,2) DEFAULT 0.00 COMMENT ''当前出租面积(平方米)''',
    'SELECT ''Column current_rental_area already exists'' AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加当前闲置面积字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'asset_management' 
     AND TABLE_NAME = 'asset_basic_info' 
     AND COLUMN_NAME = 'current_idle_area') = 0,
    'ALTER TABLE asset_basic_info ADD COLUMN current_idle_area DECIMAL(15,2) DEFAULT 0.00 COMMENT ''当前闲置面积(平方米)''',
    'SELECT ''Column current_idle_area already exists'' AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有记录的面积分配（仅更新尚未设置的记录）
UPDATE asset_basic_info 
SET 
    current_self_use_area = COALESCE(current_self_use_area, total_area * 0.7),
    current_rental_area = COALESCE(current_rental_area, total_area * 0.3),
    current_idle_area = COALESCE(current_idle_area, 0)
WHERE 
    current_self_use_area IS NULL 
    OR current_rental_area IS NULL 
    OR current_idle_area IS NULL;

-- 验证更新结果
SELECT 
    'Migration completed successfully' AS status,
    COUNT(*) AS total_records,
    SUM(CASE WHEN current_self_use_area IS NOT NULL THEN 1 ELSE 0 END) AS records_with_self_use_area,
    SUM(CASE WHEN current_rental_area IS NOT NULL THEN 1 ELSE 0 END) AS records_with_rental_area,
    SUM(CASE WHEN current_idle_area IS NOT NULL THEN 1 ELSE 0 END) AS records_with_idle_area
FROM asset_basic_info;

-- 显示部分记录以验证
SELECT 
    id,
    asset_name,
    total_area,
    current_self_use_area,
    current_rental_area,
    current_idle_area,
    (current_self_use_area + current_rental_area + current_idle_area) as total_allocated_area
FROM asset_basic_info
WHERE status = 'ACTIVE'
LIMIT 10;