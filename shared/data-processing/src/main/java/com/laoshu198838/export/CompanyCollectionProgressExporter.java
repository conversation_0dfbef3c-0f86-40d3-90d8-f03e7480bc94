package com.laoshu198838.export;

import com.laoshu198838.dto.CompanyCollectionProgressDTO;
import com.aspose.cells.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 各子公司年度清收目标完成情况Excel导出器
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-11
 */
@Component
public class CompanyCollectionProgressExporter {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyCollectionProgressExporter.class);
    private Workbook workbook;
    
    /**
     * 导出各子公司年度清收目标完成情况为Excel
     * 
     * @param data 公司清收进度数据列表
     * @param year 年份
     * @param month 月份
     * @param displayMode 展示模式
     * @return Excel文件字节数组
     * @throws IOException 导出异常
     */
    public byte[] exportToExcel(List<CompanyCollectionProgressDTO> data, String year, String month, String displayMode) throws IOException {
        logger.info("开始导出各子公司年度清收目标完成情况Excel，数据条数: {}", data != null ? data.size() : 0);
        
        try {
            // 创建工作簿和工作表
            this.workbook = new Workbook();
            Worksheet worksheet = workbook.getWorksheets().get(0);
            worksheet.setName("各子公司年度清收目标完成情况");
            Cells cells = worksheet.getCells();
            
            int rowIndex = 0;
            
            // 创建标题区域
            rowIndex = createTitleSection(cells, year, month, displayMode, rowIndex);
            
            // 创建表头
            rowIndex = createHeaderSection(cells, rowIndex);
            
            // 创建数据行
            rowIndex = createDataSection(cells, data, rowIndex);
            
            // 创建汇总行
            createSummarySection(cells, data, displayMode, rowIndex);
            
            // 调整列宽和样式
            adjustColumnsAndStyles(worksheet);
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.save(outputStream, SaveFormat.XLSX);
            byte[] result = outputStream.toByteArray();
            outputStream.close();
            
            logger.info("各子公司年度清收目标完成情况Excel导出完成，文件大小: {} bytes", result.length);
            return result;
            
        } catch (Exception e) {
            logger.error("导出各子公司年度清收目标完成情况Excel失败: {}", e.getMessage(), e);
            throw new IOException("导出Excel失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建标题区域
     */
    private int createTitleSection(Cells cells, String year, String month, String displayMode, int startRow) throws Exception {
        // 主标题
        Cell titleCell = cells.get(startRow, 0);
        titleCell.putValue("各子公司年度清收目标完成情况表");
        
        // 合并单元格
        Range titleRange = cells.createRange(startRow, 0, 1, 8);
        titleRange.merge();
        
        // 设置标题样式
        Style titleStyle = workbook.createStyle();
        titleStyle.getFont().setBold(true);
        titleStyle.getFont().setSize(16);
        titleStyle.setHorizontalAlignment(TextAlignmentType.CENTER);
        titleStyle.setVerticalAlignment(TextAlignmentType.CENTER);
        titleCell.setStyle(titleStyle);
        
        startRow++;
        
        // 查询条件
        Cell conditionCell = cells.get(startRow, 0);
        conditionCell.putValue(String.format("查询条件：%s年%s  展示模式：%s", 
            year, month, "TARGET".equals(displayMode) ? "按清收目标" : "按期初金额"));
        
        Range conditionRange = cells.createRange(startRow, 0, 1, 8);
        conditionRange.merge();
        
        startRow++;
        
        // 导出时间
        Cell exportTimeCell = cells.get(startRow, 0);
        exportTimeCell.putValue("导出时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        Range timeRange = cells.createRange(startRow, 0, 1, 8);
        timeRange.merge();
        
        startRow++;
        
        // 空行
        startRow++;
        
        return startRow;
    }
    
    /**
     * 创建表头区域
     */
    private int createHeaderSection(Cells cells, int startRow) throws Exception {
        String[] headers = {
            "排名", "管理公司", "期初金额(万元)", "清收目标(万元)", 
            "累计清收金额(万元)", "未完成金额(万元)", "完成进度(%)", "状态"
        };
        
        // 创建表头样式
        Style headerStyle = workbook.createStyle();
        headerStyle.getFont().setBold(true);
        headerStyle.setHorizontalAlignment(TextAlignmentType.CENTER);
        headerStyle.setVerticalAlignment(TextAlignmentType.CENTER);
        headerStyle.setForegroundColor(Color.getLightGray());
        headerStyle.setPattern(BackgroundType.SOLID);
        headerStyle.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
        headerStyle.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
        headerStyle.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
        headerStyle.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = cells.get(startRow, i);
            cell.putValue(headers[i]);
            cell.setStyle(headerStyle);
        }
        
        return startRow + 1;
    }
    
    /**
     * 创建数据区域
     */
    private int createDataSection(Cells cells, List<CompanyCollectionProgressDTO> data, int startRow) throws Exception {
        if (data == null || data.isEmpty()) {
            Cell noDataCell = cells.get(startRow, 0);
            noDataCell.putValue("暂无数据");
            Range noDataRange = cells.createRange(startRow, 0, 1, 8);
            noDataRange.merge();
            return startRow + 1;
        }
        
        // 按完成率降序排序
        data.sort((a, b) -> b.getCompletionRate().compareTo(a.getCompletionRate()));
        
        // 创建数据样式
        Style dataStyle = createDataStyle();
        Style amountStyle = createAmountStyle();
        Style percentStyle = createPercentStyle();
        
        for (int i = 0; i < data.size(); i++) {
            CompanyCollectionProgressDTO item = data.get(i);
            int currentRow = startRow + i;
            
            // 排名
            cells.get(currentRow, 0).putValue(i + 1);
            cells.get(currentRow, 0).setStyle(dataStyle);
            
            // 管理公司
            cells.get(currentRow, 1).putValue(item.getManagementCompany());
            cells.get(currentRow, 1).setStyle(dataStyle);
            
            // 期初金额
            cells.get(currentRow, 2).putValue(item.getYearBeginAmount().doubleValue());
            cells.get(currentRow, 2).setStyle(amountStyle);
            
            // 清收目标
            if (item.isHasTarget() && item.getCollectionTargetAmount() != null) {
                cells.get(currentRow, 3).putValue(item.getCollectionTargetAmount().doubleValue());
                cells.get(currentRow, 3).setStyle(amountStyle);
            } else {
                cells.get(currentRow, 3).putValue("-");
                cells.get(currentRow, 3).setStyle(dataStyle);
            }
            
            // 累计清收金额
            cells.get(currentRow, 4).putValue(item.getCumulativeCollectionAmount().doubleValue());
            cells.get(currentRow, 4).setStyle(amountStyle);
            
            // 未完成金额
            cells.get(currentRow, 5).putValue(item.getRemainingAmount().doubleValue());
            cells.get(currentRow, 5).setStyle(amountStyle);
            
            // 完成进度
            cells.get(currentRow, 6).putValue(item.getCompletionRate().doubleValue() / 100.0);
            cells.get(currentRow, 6).setStyle(percentStyle);
            
            // 状态
            String status = getStatusText(item.getCompletionRate().doubleValue());
            cells.get(currentRow, 7).putValue(status);
            cells.get(currentRow, 7).setStyle(dataStyle);
        }
        
        return startRow + data.size();
    }
    
    /**
     * 创建汇总区域
     */
    private void createSummarySection(Cells cells, List<CompanyCollectionProgressDTO> data, String displayMode, int startRow) throws Exception {
        if (data == null || data.isEmpty()) {
            return;
        }
        
        // 空行
        startRow++;
        
        // 计算汇总数据
        double totalBenchmark = data.stream()
            .mapToDouble(item -> item.getBenchmarkAmount().doubleValue())
            .sum();
        double totalCollection = data.stream()
            .mapToDouble(item -> item.getCumulativeCollectionAmount().doubleValue())
            .sum();
        double totalRemaining = data.stream()
            .mapToDouble(item -> item.getRemainingAmount().doubleValue())
            .sum();
        double avgRate = data.stream()
            .mapToDouble(item -> item.getCompletionRate().doubleValue())
            .average()
            .orElse(0.0);
        double overallRate = totalBenchmark > 0 ? (totalCollection / totalBenchmark * 100.0) : 0.0;
        
        // 创建样式
        Style summaryStyle = createDataStyle();
        summaryStyle.setBorder(BorderType.TOP_BORDER, CellBorderType.THICK, Color.getBlack());
        Style summaryAmountStyle = createAmountStyle();
        summaryAmountStyle.setBorder(BorderType.TOP_BORDER, CellBorderType.THICK, Color.getBlack());
        Style summaryPercentStyle = createPercentStyle();
        summaryPercentStyle.setBorder(BorderType.TOP_BORDER, CellBorderType.THICK, Color.getBlack());
        
        // 汇总行
        cells.get(startRow, 0).putValue("汇总");
        cells.get(startRow, 0).setStyle(summaryStyle);
        
        cells.get(startRow, 1).putValue(String.format("共%d个公司", data.size()));
        cells.get(startRow, 1).setStyle(summaryStyle);
        
        // 根据展示模式显示不同的汇总信息
        if ("TARGET".equals(displayMode)) {
            cells.get(startRow, 2).putValue("-");
            cells.get(startRow, 2).setStyle(summaryStyle);
            cells.get(startRow, 3).putValue(totalBenchmark);
            cells.get(startRow, 3).setStyle(summaryAmountStyle);
        } else {
            cells.get(startRow, 2).putValue(totalBenchmark);
            cells.get(startRow, 2).setStyle(summaryAmountStyle);
            cells.get(startRow, 3).putValue("-");
            cells.get(startRow, 3).setStyle(summaryStyle);
        }
        
        cells.get(startRow, 4).putValue(totalCollection);
        cells.get(startRow, 4).setStyle(summaryAmountStyle);
        
        cells.get(startRow, 5).putValue(totalRemaining);
        cells.get(startRow, 5).setStyle(summaryAmountStyle);
        
        cells.get(startRow, 6).putValue(overallRate / 100.0);
        cells.get(startRow, 6).setStyle(summaryPercentStyle);
        
        cells.get(startRow, 7).putValue(String.format("平均%.1f%%", avgRate));
        cells.get(startRow, 7).setStyle(summaryStyle);
    }
    
    /**
     * 根据完成率获取状态文本
     */
    private String getStatusText(double completionRate) {
        if (completionRate >= 100.0) {
            return "已完成";
        } else if (completionRate >= 80.0) {
            return "良好";
        } else if (completionRate >= 50.0) {
            return "一般";
        } else {
            return "较差";
        }
    }
    
    /**
     * 调整列宽和样式
     */
    private void adjustColumnsAndStyles(Worksheet worksheet) {
        // 设置列宽
        worksheet.getCells().setColumnWidthPixel(0, 60);  // 排名
        worksheet.getCells().setColumnWidthPixel(1, 120); // 管理公司
        worksheet.getCells().setColumnWidthPixel(2, 120); // 期初金额
        worksheet.getCells().setColumnWidthPixel(3, 120); // 清收目标
        worksheet.getCells().setColumnWidthPixel(4, 140); // 累计清收金额
        worksheet.getCells().setColumnWidthPixel(5, 120); // 未完成金额
        worksheet.getCells().setColumnWidthPixel(6, 100); // 完成进度
        worksheet.getCells().setColumnWidthPixel(7, 80);  // 状态
    }
    
    // 样式创建方法
    private Style createDataStyle() throws Exception {
        Style style = workbook.createStyle();
        style.setHorizontalAlignment(TextAlignmentType.CENTER);
        style.setVerticalAlignment(TextAlignmentType.CENTER);
        style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
        return style;
    }
    
    private Style createAmountStyle() throws Exception {
        Style style = workbook.createStyle();
        style.setHorizontalAlignment(TextAlignmentType.RIGHT);
        style.setVerticalAlignment(TextAlignmentType.CENTER);
        style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setNumber(3); // 数字格式：#,##0.00
        return style;
    }
    
    private Style createPercentStyle() throws Exception {
        Style style = workbook.createStyle();
        style.setHorizontalAlignment(TextAlignmentType.RIGHT);
        style.setVerticalAlignment(TextAlignmentType.CENTER);
        style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
        style.setNumber(10); // 百分比格式：0.00%
        return style;
    }
}
