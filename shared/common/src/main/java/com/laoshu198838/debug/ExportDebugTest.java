package com.laoshu198838.debug;

import java.sql.*;
import java.util.*;

/**
 * 调试逾期债权明细表导出为空的问题
 */
public class ExportDebugTest {
    
    private static final String JDBC_URL = "****************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "Zlb&198838";
    
    public static void main(String[] args) {
        System.out.println("=== 开始调试逾期债权明细表导出问题 ===");
        
        try {
            // 1. 测试数据库连接
            testDatabaseConnection();
            
            // 2. 测试基础表查询
            testBasicTableQueries();
            
            // 3. 测试复杂SQL查询
            testComplexQueries();
            
        } catch (Exception e) {
            System.err.println("❌ 调试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testDatabaseConnection() throws SQLException {
        System.out.println("\n1. 测试数据库连接...");
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
            System.out.println("✅ 数据库连接成功");
            
            // 检查数据库名称
            String dbName = conn.getCatalog();
            System.out.println("📍 当前数据库: " + dbName);
            
            // 检查表是否存在
            DatabaseMetaData metaData = conn.getMetaData();
            try (ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"})) {
                System.out.println("📋 数据库中的表:");
                while (tables.next()) {
                    String tableName = tables.getString("TABLE_NAME");
                    System.out.println("  - " + tableName);
                }
            }
        }
    }
    
    private static void testBasicTableQueries() throws SQLException {
        System.out.println("\n2. 测试基础表查询...");
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
            
            // 测试各个表的数据量
            String[] tables = {"诉讼表", "非诉讼表", "减值准备表", "新增表", "处置表"};
            
            for (String table : tables) {
                testTableData(conn, table, 2025, 2);
            }
        }
    }
    
    private static void testTableData(Connection conn, String tableName, int year, int month) throws SQLException {
        System.out.println("\n📊 测试表: " + tableName);
        
        // 1. 总数据量
        String countSql = "SELECT COUNT(*) as total FROM " + tableName;
        try (PreparedStatement ps = conn.prepareStatement(countSql);
             ResultSet rs = ps.executeQuery()) {
            if (rs.next()) {
                System.out.println("  总数据量: " + rs.getInt("total"));
            }
        }
        
        // 2. 指定年月数据量
        String yearMonthSql = "SELECT COUNT(*) as count FROM " + tableName + " WHERE 年份 = ? AND 月份 = ?";
        try (PreparedStatement ps = conn.prepareStatement(yearMonthSql)) {
            ps.setInt(1, year);
            ps.setInt(2, month);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    System.out.println("  " + year + "年" + month + "月数据量: " + rs.getInt("count"));
                }
            }
        }
        
        // 3. 查看前3条数据
        String sampleSql = "SELECT * FROM " + tableName + " WHERE 年份 = ? AND 月份 = ? LIMIT 3";
        try (PreparedStatement ps = conn.prepareStatement(sampleSql)) {
            ps.setInt(1, year);
            ps.setInt(2, month);
            try (ResultSet rs = ps.executeQuery()) {
                System.out.println("  样本数据:");
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                int rowNum = 0;
                while (rs.next() && rowNum < 3) {
                    rowNum++;
                    System.out.print("    行" + rowNum + ": ");
                    for (int i = 1; i <= Math.min(5, columnCount); i++) { // 只显示前5列
                        String columnName = metaData.getColumnName(i);
                        String value = rs.getString(i);
                        System.out.print(columnName + "=" + value + " ");
                    }
                    System.out.println();
                }
                
                if (rowNum == 0) {
                    System.out.println("    ⚠️ 没有找到数据");
                }
            }
        }
    }
    
    private static void testComplexQueries() throws SQLException {
        System.out.println("\n3. 测试复杂SQL查询...");
        
        try (Connection conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD)) {
            
            // 测试表8的复杂查询
            testTable8Query(conn, 2025, 2);
            
            // 测试表9的复杂查询  
            testTable9Query(conn, 2025, 2);
            
            // 测试表10的复杂查询
            testTable10Query(conn, 2025, 2);
        }
    }
    
    private static void testTable8Query(Connection conn, int year, int month) throws SQLException {
        System.out.println("\n🔍 测试表8查询 (临表-3)...");
        
        String sql = """
            WITH all_keys AS (
                SELECT DISTINCT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
                FROM (
                    SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM 减值准备表 WHERE 年份 = ? - 1 AND 月份 = 12
                    UNION
                    SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM 新增表 WHERE 年份 = ?
                    UNION  
                    SELECT 管理公司, 债权人, 债务人, 是否涉诉, '', 期间 FROM 处置表 WHERE 年份 = ? AND 月份 <= ?
                ) combined
            )
            SELECT COUNT(*) as count FROM all_keys
            """;
            
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setInt(1, year);
            ps.setInt(2, year);
            ps.setInt(3, year);
            ps.setInt(4, month);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    System.out.println("  表8查询结果数量: " + rs.getInt("count"));
                }
            }
        }
    }
    
    private static void testTable9Query(Connection conn, int year, int month) throws SQLException {
        System.out.println("\n🔍 测试表9查询 (新增逾期债权明细表)...");
        
        String sql = """
            SELECT COUNT(*) as count FROM 新增表 
            WHERE 年份 = ? AND (
                `1月` <> 0 OR `2月` <> 0 OR `3月` <> 0 OR `4月` <> 0 OR 
                `5月` <> 0 OR `6月` <> 0 OR `7月` <> 0 OR `8月` <> 0 OR 
                `9月` <> 0 OR `10月` <> 0 OR `11月` <> 0 OR `12月` <> 0
            )
            """;
            
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setInt(1, year);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    System.out.println("  表9查询结果数量: " + rs.getInt("count"));
                }
            }
        }
    }
    
    private static void testTable10Query(Connection conn, int year, int month) throws SQLException {
        System.out.println("\n🔍 测试表10查询 (债权处置明细表)...");
        
        String sql = """
            SELECT COUNT(*) as count FROM 处置表 
            WHERE 年份 = ? AND 月份 <= ?
            """;
            
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setInt(1, year);
            ps.setInt(2, month);
            
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    System.out.println("  表10查询结果数量: " + rs.getInt("count"));
                }
            }
        }
    }
}
