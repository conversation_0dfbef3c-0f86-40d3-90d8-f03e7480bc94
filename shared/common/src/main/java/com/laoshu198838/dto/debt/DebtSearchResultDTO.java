package com.laoshu198838.dto.debt;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

/**
 * 债权搜索结果DTO
 * 用于返回可转换债权的搜索结果数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DebtSearchResultDTO {
    
    /**
     * 债权人
     */
    private String creditor;
    
    /**
     * 债务人
     */
    private String debtor;
    
    /**
     * 期间
     */
    private String period;
    
    /**
     * 债权余额
     */
    private BigDecimal debtBalance;
    
    /**
     * 当前状态（诉讼/非诉讼）
     */
    private String currentStatus;
    
    /**
     * 是否涉诉（是/否）
     */
    private String isLitigation;
    
    /**
     * 管理公司
     */
    private String managementCompany;
    
    /**
     * 科目名称
     */
    private String subjectName;
    
    /**
     * 年份
     */
    private Integer year;
    
    /**
     * 月份
     */
    private Integer month;
    
    /**
     * 来源表标识（litigation/non_litigation）
     */
    private String sourceTable;
    
    /**
     * 诉讼案件名称（仅诉讼债权有值）
     */
    private String litigationCase;
    
    /**
     * 本金
     */
    private BigDecimal principal;
    
    /**
     * 利息
     */
    private BigDecimal interest;
    
    /**
     * 违约金
     */
    private BigDecimal penalty;
    
    /**
     * 责任人
     */
    private String responsiblePerson;
    
    /**
     * 债权类别
     */
    private String debtCategory;
    
    /**
     * 备注
     */
    private String remark;
}