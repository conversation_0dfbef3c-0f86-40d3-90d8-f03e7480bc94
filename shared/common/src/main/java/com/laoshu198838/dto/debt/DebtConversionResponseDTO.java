package com.laoshu198838.dto.debt;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 债权转换响应DTO
 * 用于返回诉讼与非诉讼债权转换的结果数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DebtConversionResponseDTO {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 详细信息
     */
    private String details;
    
    /**
     * 转换类型（litigation_to_non_litigation 或 non_litigation_to_litigation）
     */
    private String conversionType;
    
    /**
     * 受影响的记录数
     */
    private int affectedRecords;
    
    /**
     * 转换前状态
     */
    private String fromStatus;
    
    /**
     * 转换后状态
     */
    private String toStatus;
    
    /**
     * 转换时间戳
     */
    private String timestamp;
    
    /**
     * 创建成功响应
     */
    public static DebtConversionResponseDTO success(String message, String conversionType, int affectedRecords) {
        return DebtConversionResponseDTO.builder()
                .success(true)
                .message(message)
                .conversionType(conversionType)
                .affectedRecords(affectedRecords)
                .timestamp(java.time.LocalDateTime.now().toString())
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static DebtConversionResponseDTO failure(String message, String details) {
        return DebtConversionResponseDTO.builder()
                .success(false)
                .message(message)
                .details(details)
                .timestamp(java.time.LocalDateTime.now().toString())
                .build();
    }
}