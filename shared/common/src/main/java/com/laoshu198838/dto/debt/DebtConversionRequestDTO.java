package com.laoshu198838.dto.debt;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

/**
 * 债权转换请求DTO
 * 用于承载诉讼与非诉讼债权转换的请求数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DebtConversionRequestDTO {
    
    /**
     * 债权人
     */
    @NotBlank(message = "债权人不能为空")
    private String creditor;
    
    /**
     * 债务人
     */
    @NotBlank(message = "债务人不能为空")
    private String debtor;
    
    /**
     * 期间
     */
    @NotBlank(message = "期间不能为空")
    private String period;
    
    /**
     * 原始年份
     */
    @NotNull(message = "年份不能为空")
    private Integer year;
    
    /**
     * 原始月份
     */
    @NotNull(message = "月份不能为空")
    @Min(value = 1, message = "月份必须在1-12之间")
    @Max(value = 12, message = "月份必须在1-12之间")
    private Integer month;
    
    /**
     * 转换年份
     */
    @NotNull(message = "转换年份不能为空")
    private Integer conversionYear;
    
    /**
     * 转换月份
     */
    @NotNull(message = "转换月份不能为空")
    @Min(value = 1, message = "转换月份必须在1-12之间")
    @Max(value = 12, message = "转换月份必须在1-12之间")
    private Integer conversionMonth;
    
    /**
     * 备注说明
     */
    private String remark;
    
    // === 非诉讼转诉讼特有字段 ===
    
    /**
     * 诉讼案件名称
     */
    private String litigationCase;
    
    /**
     * 诉讼主张本金
     */
    private BigDecimal litigationOccurredPrincipal;
    
    /**
     * 诉讼主张应收利息及罚金
     */
    private BigDecimal litigationInterestFee;
    
    /**
     * 诉讼费
     */
    private BigDecimal litigationFee;
    
    /**
     * 中介费
     */
    private BigDecimal intermediaryFee;
}