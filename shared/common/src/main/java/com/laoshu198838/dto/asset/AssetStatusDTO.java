package com.laoshu198838.dto.asset;

import com.laoshu198838.entity.asset.AssetStatus;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资产状态DTO
 * 
 * <AUTHOR>
 */
@Data
public class AssetStatusDTO {

    private Long id;
    private Long assetId;
    private String assetName; // 关联查询获取
    private String managementCompany;
    private Integer statusYear;
    private Integer statusMonth;
    private BigDecimal totalArea;
    private BigDecimal selfUseArea;
    private BigDecimal rentalArea;
    private BigDecimal idleArea;
    private String remark;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String createdBy;
    private String updatedBy;

    // 计算字段
    private BigDecimal activatedArea; // 盘活面积
    private BigDecimal activationRate; // 盘活率

    /**
     * 从实体转换为DTO
     */
    public static AssetStatusDTO fromEntity(AssetStatus entity) {
        if (entity == null) {
            return null;
        }

        AssetStatusDTO dto = new AssetStatusDTO();
        dto.setId(entity.getId());
        dto.setAssetId(entity.getAssetId());
        dto.setManagementCompany(entity.getManagementCompany());
        dto.setStatusYear(entity.getStatusYear());
        dto.setStatusMonth(entity.getStatusMonth());
        dto.setTotalArea(entity.getTotalArea());
        dto.setSelfUseArea(entity.getSelfUseArea());
        dto.setRentalArea(entity.getRentalArea());
        dto.setIdleArea(entity.getIdleArea());
        dto.setRemark(entity.getRemark());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setUpdatedBy(entity.getUpdatedBy());

        // 设置计算字段
        dto.setActivatedArea(entity.getActivatedArea());
        dto.setActivationRate(entity.getActivationRate());

        return dto;
    }

    /**
     * 转换为实体
     */
    public AssetStatus toEntity() {
        AssetStatus entity = new AssetStatus();
        entity.setId(this.id);
        entity.setAssetId(this.assetId);
        entity.setManagementCompany(this.managementCompany);
        entity.setStatusYear(this.statusYear);
        entity.setStatusMonth(this.statusMonth);
        entity.setTotalArea(this.totalArea);
        entity.setSelfUseArea(this.selfUseArea);
        entity.setRentalArea(this.rentalArea);
        entity.setIdleArea(this.idleArea);
        entity.setRemark(this.remark);
        entity.setCreatedBy(this.createdBy);
        entity.setUpdatedBy(this.updatedBy);

        return entity;
    }
}
