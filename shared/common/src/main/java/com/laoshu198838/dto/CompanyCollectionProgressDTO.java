package com.laoshu198838.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 公司清收进度数据传输对象
 * 
 * <p>用于前后端交互，包含各公司的清收目标完成情况数据</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-11
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyCollectionProgressDTO {
    
    /**
     * 管理公司名称
     */
    private String managementCompany;
    
    /**
     * 期初金额（万元）
     */
    private BigDecimal yearBeginAmount;
    
    /**
     * 清收目标金额（万元）
     */
    private BigDecimal collectionTargetAmount;
    
    /**
     * 累计清收金额（万元）
     */
    private BigDecimal cumulativeCollectionAmount;
    
    /**
     * 未完成金额（万元）- 按当前展示模式计算
     */
    private BigDecimal remainingAmount;
    
    /**
     * 完成进度（百分比，保留1位小数）
     */
    private BigDecimal completionRate;
    
    /**
     * 基准金额（万元）- 用于图表展示的总高度（期初金额或清收目标）
     */
    private BigDecimal benchmarkAmount;
    
    /**
     * 是否有清收目标
     */
    private boolean hasTarget;
    
    /**
     * 展示模式枚举
     */
    public enum DisplayMode {
        /**
         * 按期初金额展示
         */
        INITIAL("期初金额"),
        
        /**
         * 按清收目标展示
         */
        TARGET("清收目标");
        
        private final String displayName;
        
        DisplayMode(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * 根据展示模式重新计算相关字段
     * 
     * @param displayMode 展示模式
     */
    public void recalculateByMode(DisplayMode displayMode) {
        if (displayMode == DisplayMode.TARGET && hasTarget && collectionTargetAmount != null) {
            // 按目标模式
            this.benchmarkAmount = this.collectionTargetAmount;
            this.remainingAmount = this.collectionTargetAmount.subtract(
                this.cumulativeCollectionAmount != null ? this.cumulativeCollectionAmount : BigDecimal.ZERO);
            
            // 计算完成率：累计清收金额 / 清收目标 × 100%
            if (this.collectionTargetAmount.compareTo(BigDecimal.ZERO) > 0) {
                this.completionRate = this.cumulativeCollectionAmount != null
                    ? this.cumulativeCollectionAmount.divide(this.collectionTargetAmount, 3, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO;
            } else {
                this.completionRate = BigDecimal.ZERO;
            }
        } else {
            // 按期初模式（或目标不存在时的降级处理）
            this.benchmarkAmount = this.yearBeginAmount != null ? this.yearBeginAmount : BigDecimal.ZERO;
            this.remainingAmount = this.benchmarkAmount.subtract(
                this.cumulativeCollectionAmount != null ? this.cumulativeCollectionAmount : BigDecimal.ZERO);
            
            // 计算完成率：累计清收金额 / 期初金额 × 100%
            if (this.benchmarkAmount.compareTo(BigDecimal.ZERO) > 0) {
                this.completionRate = this.cumulativeCollectionAmount != null
                    ? this.cumulativeCollectionAmount.divide(this.benchmarkAmount, 3, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO;
            } else {
                this.completionRate = BigDecimal.ZERO;
            }
        }
        
        // 确保未完成金额不为负数
        if (this.remainingAmount.compareTo(BigDecimal.ZERO) < 0) {
            this.remainingAmount = BigDecimal.ZERO;
        }
        
        // 确保完成率不超过100%
        if (this.completionRate.compareTo(BigDecimal.valueOf(100)) > 0) {
            this.completionRate = BigDecimal.valueOf(100.0).setScale(1, RoundingMode.HALF_UP);
        }
    }
    
    /**
     * 格式化金额显示（千分位分隔）
     * 
     * @param amount 金额
     * @return 格式化后的金额字符串
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0";
        }
        return String.format("%,.0f", amount);
    }
    
    /**
     * 获取格式化的期初金额
     */
    public String getFormattedYearBeginAmount() {
        return formatAmount(yearBeginAmount);
    }
    
    /**
     * 获取格式化的清收目标金额
     */
    public String getFormattedTargetAmount() {
        return formatAmount(collectionTargetAmount);
    }
    
    /**
     * 获取格式化的累计清收金额
     */
    public String getFormattedCumulativeAmount() {
        return formatAmount(cumulativeCollectionAmount);
    }
    
    /**
     * 获取格式化的未完成金额
     */
    public String getFormattedRemainingAmount() {
        return formatAmount(remainingAmount);
    }
    
    /**
     * 获取格式化的完成率
     */
    public String getFormattedCompletionRate() {
        if (completionRate == null) {
            return "0.0%";
        }
        return completionRate + "%";
    }
    
    /**
     * 验证数据完整性
     */
    public boolean isValid() {
        return managementCompany != null && !managementCompany.trim().isEmpty()
            && yearBeginAmount != null
            && cumulativeCollectionAmount != null;
    }
}