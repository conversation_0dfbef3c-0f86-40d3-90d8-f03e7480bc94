package com.laoshu198838.dto.asset;

import com.laoshu198838.entity.asset.RentalInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 出租信息DTO
 * 
 * <AUTHOR>
 */
@Data
public class RentalInfoDTO {

    private Long id;
    private Long assetId;
    private String assetName; // 关联查询获取
    private String managementCompany;
    private String lessor;
    private String lessee;
    private String contractNo;
    private LocalDate contractSignDate;
    private LocalDate rentalStartDate;
    private LocalDate rentalEndDate;
    private BigDecimal rentalArea;
    private BigDecimal monthlyRent;
    private RentalInfo.ContractStatus contractStatus;
    private String contractStatusDescription;
    private Boolean isExpiringSoon;
    private String remark;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private String createdBy;
    private String updatedBy;

    // 计算字段
    private Long daysUntilExpiry; // 距离到期天数
    private Long totalRentalMonths; // 总租期月数
    private BigDecimal annualRent; // 年租金

    /**
     * 从实体转换为DTO
     */
    public static RentalInfoDTO fromEntity(RentalInfo entity) {
        if (entity == null) {
            return null;
        }

        RentalInfoDTO dto = new RentalInfoDTO();
        dto.setId(entity.getId());
        dto.setAssetId(entity.getAssetId());
        dto.setManagementCompany(entity.getManagementCompany());
        dto.setLessor(entity.getLessor());
        dto.setLessee(entity.getLessee());
        dto.setContractNo(entity.getContractNo());
        dto.setContractSignDate(entity.getContractSignDate());
        dto.setRentalStartDate(entity.getRentalStartDate());
        dto.setRentalEndDate(entity.getRentalEndDate());
        dto.setRentalArea(entity.getRentalArea());
        dto.setMonthlyRent(entity.getMonthlyRent());
        dto.setContractStatus(entity.getContractStatus());
        dto.setContractStatusDescription(entity.getContractStatus() != null ? 
            entity.getContractStatus().getDescription() : null);
        dto.setIsExpiringSoon(entity.getIsExpiringSoon());
        dto.setRemark(entity.getRemark());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedTime(entity.getUpdatedTime());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setUpdatedBy(entity.getUpdatedBy());

        // 设置计算字段
        dto.setDaysUntilExpiry(entity.getDaysUntilExpiry());
        dto.setTotalRentalMonths(entity.getTotalRentalMonths());
        dto.setAnnualRent(entity.getAnnualRent());

        return dto;
    }

    /**
     * 转换为实体
     */
    public RentalInfo toEntity() {
        RentalInfo entity = new RentalInfo();
        entity.setId(this.id);
        entity.setAssetId(this.assetId);
        entity.setManagementCompany(this.managementCompany);
        entity.setLessor(this.lessor);
        entity.setLessee(this.lessee);
        entity.setContractNo(this.contractNo);
        entity.setContractSignDate(this.contractSignDate);
        entity.setRentalStartDate(this.rentalStartDate);
        entity.setRentalEndDate(this.rentalEndDate);
        entity.setRentalArea(this.rentalArea);
        entity.setMonthlyRent(this.monthlyRent);
        entity.setContractStatus(this.contractStatus);
        entity.setIsExpiringSoon(this.isExpiringSoon);
        entity.setRemark(this.remark);
        entity.setCreatedBy(this.createdBy);
        entity.setUpdatedBy(this.updatedBy);

        return entity;
    }
}
