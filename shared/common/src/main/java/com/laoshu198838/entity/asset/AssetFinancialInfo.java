package com.laoshu198838.entity.asset;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 资产财务信息实体类
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "asset_financial_info",
       uniqueConstraints = @UniqueConstraint(columnNames = {"asset_id", "financial_year", "financial_month"}))
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class AssetFinancialInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "asset_id", nullable = false)
    private Long assetId;

    @Column(name = "management_company", nullable = false, length = 100)
    private String managementCompany;

    @Column(name = "accounting_date", nullable = false)
    private LocalDate accountingDate;

    @Column(name = "original_value", nullable = false, precision = 20, scale = 2)
    private BigDecimal originalValue;

    @Column(name = "depreciation_years", nullable = false)
    private Integer depreciationYears;

    @Enumerated(EnumType.STRING)
    @Column(name = "depreciation_method")
    private DepreciationMethod depreciationMethod = DepreciationMethod.STRAIGHT_LINE;

    @Column(name = "residual_rate", precision = 5, scale = 4)
    private BigDecimal residualRate = new BigDecimal("0.05");

    @Column(name = "book_value", nullable = false, precision = 20, scale = 2)
    private BigDecimal bookValue;

    @Column(name = "financial_year", nullable = false)
    private Integer financialYear;

    @Column(name = "financial_month", nullable = false)
    private Integer financialMonth;

    @Column(name = "accumulated_depreciation", precision = 20, scale = 2)
    private BigDecimal accumulatedDepreciation = BigDecimal.ZERO;

    @Column(name = "monthly_depreciation", precision = 20, scale = 2)
    private BigDecimal monthlyDepreciation = BigDecimal.ZERO;

    @Column(name = "is_auto_generated", columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean isAutoGenerated = false;

    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    @PrePersist
    protected void onCreate() {
        createdTime = LocalDateTime.now();
        updatedTime = LocalDateTime.now();
        calculateDepreciation();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
        calculateDepreciation();
    }

    /**
     * 计算折旧相关数据
     */
    private void calculateDepreciation() {
        if (originalValue != null && depreciationYears != null && residualRate != null) {
            // 计算月折旧额
            this.monthlyDepreciation = calculateMonthlyDepreciation();
            
            // 计算账面价值
            this.bookValue = originalValue.subtract(accumulatedDepreciation);
        }
    }

    /**
     * 计算月折旧额
     */
    public BigDecimal calculateMonthlyDepreciation() {
        if (originalValue == null || depreciationYears == null || residualRate == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal residualValue = originalValue.multiply(residualRate);
        BigDecimal depreciableAmount = originalValue.subtract(residualValue);
        
        switch (depreciationMethod) {
            case STRAIGHT_LINE:
                // 直线法：(原值 - 残值) / (折旧年限 * 12)
                return depreciableAmount.divide(
                    new BigDecimal(depreciationYears * 12), 2, RoundingMode.HALF_UP);
                
            case WORK_LOAD:
                // 工作量法：需要额外的工作量数据，暂时按直线法处理
                return depreciableAmount.divide(
                    new BigDecimal(depreciationYears * 12), 2, RoundingMode.HALF_UP);
                
            case DOUBLE_DECLINING_BALANCE:
                // 双倍余额递减法：年折旧率 = 2 / 折旧年限，月折旧率 = 年折旧率 / 12
                BigDecimal rate = new BigDecimal("2").divide(
                    new BigDecimal(depreciationYears), 4, RoundingMode.HALF_UP);
                BigDecimal monthlyRate = rate.divide(new BigDecimal("12"), 4, RoundingMode.HALF_UP);
                return bookValue.multiply(monthlyRate);
                
            case SUM_OF_YEARS:
                // 年数总和法：需要知道已使用年限，暂时按直线法处理
                return depreciableAmount.divide(
                    new BigDecimal(depreciationYears * 12), 2, RoundingMode.HALF_UP);
                
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取净残值
     */
    public BigDecimal getResidualValue() {
        if (originalValue != null && residualRate != null) {
            return originalValue.multiply(residualRate);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取可折旧金额
     */
    public BigDecimal getDepreciableAmount() {
        return originalValue.subtract(getResidualValue());
    }

    /**
     * 折旧方法枚举
     */
    public enum DepreciationMethod {
        STRAIGHT_LINE("直线法"),
        WORK_LOAD("工作量法"),
        DOUBLE_DECLINING_BALANCE("双倍余额递减法"),
        SUM_OF_YEARS("年数总和法");

        private final String description;

        DepreciationMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
