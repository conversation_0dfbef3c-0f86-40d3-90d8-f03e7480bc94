package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 清收目标表实体类
 * 
 * <p>对应数据库表"清收目标表"，用于存储各公司的逾期债权年度清收目标。
 * 该表记录了每个管理公司在特定年份的债权清收目标金额。</p>
 * 
 * <h3>数据库映射：</h3>
 * <ul>
 *   <li>表名：清收目标表</li>
 *   <li>主键：复合主键（管理公司 + 年份）</li>
 *   <li>字符集：utf8mb4</li>
 * </ul>
 * 
 * <h3>业务用途：</h3>
 * <ul>
 *   <li>年度清收目标设定和管理</li>
 *   <li>各公司清收目标对比分析</li>
 *   <li>清收进度跟踪和考核</li>
 *   <li>多年度目标趋势分析</li>
 * </ul>
 * 
 * <h3>关联关系：</h3>
 * <ul>
 *   <li>与其他债权表通过管理公司字段关联</li>
 *   <li>支持按年份进行数据分析</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 * @see CollectionTargetKey
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "清收目标表")
public class CollectionTarget implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 复合主键
     * 包含管理公司和年份
     */
    @EmbeddedId
    private CollectionTargetKey id;

    /**
     * 记录序号
     * 用于排序和标识
     */
    @Column(name = "序号", nullable = false)
    private Integer sequence;

    /**
     * 年度清收目标金额
     * 单位：万元，精度：19位数字，2位小数
     * 默认值：0.00
     */
    @Column(name = "清收目标金额", nullable = false, precision = 19, scale = 2)
    private BigDecimal targetAmount = BigDecimal.ZERO;

    /**
     * 记录创建时间
     * 自动设置为当前时间
     */
    @Column(name = "创建时间", nullable = false)
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     * 每次更新时自动设置为当前时间
     */
    @Column(name = "更新时间", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 创建人员
     * 记录是谁创建了这条目标记录
     */
    @Column(name = "创建人", length = 50)
    private String createdBy;

    /**
     * 备注信息
     * 用于记录额外的说明信息
     */
    @Column(name = "备注", columnDefinition = "TEXT")
    private String remark;

    /**
     * 默认无参构造函数
     * JPA要求实体类必须有无参构造函数
     */
    public CollectionTarget() {
    }

    /**
     * 带参数的构造函数
     * 
     * @param managementCompany 管理公司名称
     * @param year 目标年份
     * @param sequence 序号
     * @param targetAmount 目标金额
     */
    public CollectionTarget(String managementCompany, Integer year, Integer sequence, BigDecimal targetAmount) {
        this.id = new CollectionTargetKey(managementCompany, year);
        this.sequence = sequence;
        this.targetAmount = targetAmount != null ? targetAmount : BigDecimal.ZERO;
    }

    /**
     * JPA生命周期回调：持久化前执行
     * 自动设置创建时间和更新时间
     */
    @PrePersist
    public void prePersist() {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
    }

    /**
     * JPA生命周期回调：更新前执行
     * 自动更新更新时间
     */
    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    // ==================== 委托方法 ====================
    // 将复合主键字段的访问委托给内部的复合主键类

    /**
     * 获取管理公司名称
     * 
     * @return 管理公司名称
     */
    public String getManagementCompany() {
        return id != null ? id.getManagementCompany() : null;
    }

    /**
     * 设置管理公司名称
     * 
     * @param managementCompany 管理公司名称
     */
    public void setManagementCompany(String managementCompany) {
        if (id == null) {
            id = new CollectionTargetKey();
        }
        id.setManagementCompany(managementCompany);
    }

    /**
     * 获取目标年份
     * 
     * @return 目标年份
     */
    public Integer getYear() {
        return id != null ? id.getYear() : null;
    }

    /**
     * 设置目标年份
     * 
     * @param year 目标年份
     */
    public void setYear(Integer year) {
        if (id == null) {
            id = new CollectionTargetKey();
        }
        id.setYear(year);
    }

    // ==================== 业务方法 ====================

    /**
     * 检查目标金额是否已设置（大于0）
     * 
     * @return 如果目标金额大于0则返回true
     */
    public boolean hasTargetAmount() {
        return targetAmount != null && targetAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 格式化目标金额为字符串
     * 
     * @return 格式化的金额字符串（保留2位小数）
     */
    public String getFormattedTargetAmount() {
        return targetAmount != null ? String.format("%.2f", targetAmount) : "0.00";
    }

    /**
     * 验证实体数据是否完整
     * 
     * @return 如果数据完整则返回true
     */
    public boolean isValid() {
        return id != null && id.isValid() 
               && sequence != null && sequence > 0
               && targetAmount != null;
    }

    /**
     * 重写toString方法，便于调试和日志输出
     * 
     * @return 格式化的字符串表示
     */
    @Override
    public String toString() {
        return String.format("CollectionTarget{id=%s, sequence=%d, targetAmount=%s, createdBy='%s'}", 
                           id, sequence, targetAmount, createdBy);
    }
}
