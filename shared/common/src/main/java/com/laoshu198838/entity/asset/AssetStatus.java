package com.laoshu198838.entity.asset;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资产状态实体类
 * 使用历史快照模式记录资产状态变更
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "asset_status", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"asset_id", "status_year", "status_month"}))
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class AssetStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "asset_id", nullable = false)
    private Long assetId;

    @Column(name = "management_company", nullable = false, length = 100)
    private String managementCompany;

    @Column(name = "status_year", nullable = false)
    private Integer statusYear;

    @Column(name = "status_month", nullable = false)
    private Integer statusMonth;

    @Column(name = "total_area", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalArea;

    @Column(name = "self_use_area", precision = 15, scale = 2)
    private BigDecimal selfUseArea = BigDecimal.ZERO;

    @Column(name = "rental_area", precision = 15, scale = 2)
    private BigDecimal rentalArea = BigDecimal.ZERO;

    @Column(name = "idle_area", precision = 15, scale = 2)
    private BigDecimal idleArea = BigDecimal.ZERO;

    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    @PrePersist
    protected void onCreate() {
        createdTime = LocalDateTime.now();
        updatedTime = LocalDateTime.now();
        // 自动计算闲置面积
        calculateIdleArea();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
        // 自动计算闲置面积
        calculateIdleArea();
    }

    /**
     * 计算闲置面积
     * 闲置面积 = 总面积 - 自用面积 - 出租面积
     */
    private void calculateIdleArea() {
        if (totalArea != null) {
            BigDecimal selfUse = selfUseArea != null ? selfUseArea : BigDecimal.ZERO;
            BigDecimal rental = rentalArea != null ? rentalArea : BigDecimal.ZERO;
            this.idleArea = totalArea.subtract(selfUse).subtract(rental);
        }
    }

    /**
     * 获取盘活面积（自用面积 + 出租面积）
     */
    public BigDecimal getActivatedArea() {
        BigDecimal selfUse = selfUseArea != null ? selfUseArea : BigDecimal.ZERO;
        BigDecimal rental = rentalArea != null ? rentalArea : BigDecimal.ZERO;
        return selfUse.add(rental);
    }

    /**
     * 获取盘活率
     */
    public BigDecimal getActivationRate() {
        if (totalArea == null || totalArea.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return getActivatedArea().divide(totalArea, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }
}
