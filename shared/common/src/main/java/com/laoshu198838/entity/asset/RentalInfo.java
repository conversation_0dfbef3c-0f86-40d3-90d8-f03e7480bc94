package com.laoshu198838.entity.asset;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 出租信息实体类
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "rental_info")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class RentalInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "asset_id", nullable = false)
    private Long assetId;

    @Column(name = "management_company", nullable = false, length = 100)
    private String managementCompany;

    @Column(name = "lessor", nullable = false, length = 100)
    private String lessor;

    @Column(name = "lessee", nullable = false, length = 100)
    private String lessee;

    @Column(name = "contract_no", length = 100)
    private String contractNo;

    @Column(name = "contract_sign_date")
    private LocalDate contractSignDate;

    @Column(name = "rental_start_date", nullable = false)
    private LocalDate rentalStartDate;

    @Column(name = "rental_end_date", nullable = false)
    private LocalDate rentalEndDate;

    @Column(name = "rental_area", nullable = false, precision = 15, scale = 2)
    private BigDecimal rentalArea;

    @Column(name = "monthly_rent", nullable = false, precision = 15, scale = 2)
    private BigDecimal monthlyRent;

    @Enumerated(EnumType.STRING)
    @Column(name = "contract_status")
    private ContractStatus contractStatus = ContractStatus.ACTIVE;

    @Column(name = "is_expiring_soon", columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean isExpiringSoon = false;

    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    @PrePersist
    protected void onCreate() {
        createdTime = LocalDateTime.now();
        updatedTime = LocalDateTime.now();
        checkExpiringStatus();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
        checkExpiringStatus();
    }

    /**
     * 检查合同是否即将到期（30天内）
     */
    private void checkExpiringStatus() {
        if (rentalEndDate != null) {
            long daysUntilExpiry = ChronoUnit.DAYS.between(LocalDate.now(), rentalEndDate);
            this.isExpiringSoon = daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
        }
    }

    /**
     * 获取合同剩余天数
     */
    public long getDaysUntilExpiry() {
        if (rentalEndDate != null) {
            return ChronoUnit.DAYS.between(LocalDate.now(), rentalEndDate);
        }
        return 0;
    }

    /**
     * 获取合同总租期（月数）
     */
    public long getTotalRentalMonths() {
        if (rentalStartDate != null && rentalEndDate != null) {
            return ChronoUnit.MONTHS.between(rentalStartDate, rentalEndDate);
        }
        return 0;
    }

    /**
     * 获取年租金
     */
    public BigDecimal getAnnualRent() {
        if (monthlyRent != null) {
            return monthlyRent.multiply(new BigDecimal("12"));
        }
        return BigDecimal.ZERO;
    }

    /**
     * 合同状态枚举
     */
    public enum ContractStatus {
        ACTIVE("生效中"),
        EXPIRED("已到期"),
        TERMINATED("已终止");

        private final String description;

        ContractStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
