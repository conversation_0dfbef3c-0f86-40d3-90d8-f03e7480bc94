package com.laoshu198838.entity.asset;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 资产附件实体类
 * 
 * <AUTHOR>
 */
@Entity
@Table(name = "asset_attachments")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class AssetAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "asset_id", nullable = false)
    private Long assetId;

    @Column(name = "management_company", nullable = false, length = 100)
    private String managementCompany;

    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @Column(name = "original_file_name", nullable = false, length = 255)
    private String originalFileName;

    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    @Column(name = "file_size")
    private Long fileSize;

    @Column(name = "file_type", length = 50)
    private String fileType;

    @Enumerated(EnumType.STRING)
    @Column(name = "attachment_type", nullable = false)
    private AttachmentType attachmentType;

    @Column(name = "contract_no", length = 100)
    private String contractNo;

    @Column(name = "upload_time", nullable = false, updatable = false)
    private LocalDateTime uploadTime;

    @Column(name = "uploaded_by", length = 50)
    private String uploadedBy;

    @Enumerated(EnumType.STRING)
    @Column(name = "sync_status")
    private SyncStatus syncStatus = SyncStatus.PENDING;

    @Column(name = "sync_time")
    private LocalDateTime syncTime;

    @Column(name = "local_path", length = 500)
    private String localPath;

    @PrePersist
    protected void onCreate() {
        uploadTime = LocalDateTime.now();
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (originalFileName != null && originalFileName.contains(".")) {
            return originalFileName.substring(originalFileName.lastIndexOf(".") + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "未知";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查是否为图片文件
     */
    public boolean isImage() {
        String extension = getFileExtension();
        return "jpg".equals(extension) || "jpeg".equals(extension) || 
               "png".equals(extension) || "gif".equals(extension) || 
               "bmp".equals(extension) || "webp".equals(extension);
    }

    /**
     * 检查是否为PDF文件
     */
    public boolean isPdf() {
        return "pdf".equals(getFileExtension());
    }

    /**
     * 附件类型枚举
     */
    public enum AttachmentType {
        PURCHASE_CONTRACT("购买合同"),
        RENTAL_CONTRACT("出租合同"),
        PROPERTY_CERTIFICATE("产权证书"),
        OTHER("其他");

        private final String description;

        AttachmentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING("待同步"),
        SYNCED("已同步"),
        FAILED("同步失败");

        private final String description;

        SyncStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
