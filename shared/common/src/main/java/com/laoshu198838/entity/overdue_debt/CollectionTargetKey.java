package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.io.Serializable;

/**
 * 清收目标表专用的复合主键类
 * 
 * <p>该类封装"清收目标表"的联合主键字段，
 * 包括管理公司和年份等信息。</p>
 * 
 * <h3>主键组成：</h3>
 * <ul>
 *   <li>管理公司 - 管理公司名称</li>
 *   <li>年份 - 目标年份</li>
 * </ul>
 * 
 * <h3>设计说明：</h3>
 * <ul>
 *   <li>遵循项目现有的复合主键设计模式</li>
 *   <li>确保每个公司每年只有一条清收目标记录</li>
 *   <li>支持多年度目标管理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 * @see CollectionTarget
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@EqualsAndHashCode
@Embeddable
public class CollectionTargetKey implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 管理公司名称
     * 对应数据库字段：管理公司
     */
    @Column(name = "管理公司", nullable = false, length = 50)
    private String managementCompany;

    /**
     * 目标年份
     * 对应数据库字段：年份
     */
    @Column(name = "年份", nullable = false)
    private Integer year;

    /**
     * 默认无参构造函数
     * JPA要求实体类必须有无参构造函数
     */
    public CollectionTargetKey() {
    }

    /**
     * 带参数的构造函数，用于创建复合键实例
     * 
     * @param managementCompany 管理公司名称
     * @param year 目标年份
     */
    public CollectionTargetKey(String managementCompany, Integer year) {
        this.managementCompany = managementCompany;
        this.year = year;
    }

    /**
     * 重写toString方法，便于调试和日志输出
     * 
     * @return 格式化的字符串表示
     */
    @Override
    public String toString() {
        return String.format("CollectionTargetKey{managementCompany='%s', year=%d}", 
                           managementCompany, year);
    }

    /**
     * 验证主键字段是否完整
     * 
     * @return 如果所有必需字段都不为空则返回true
     */
    public boolean isValid() {
        return managementCompany != null && !managementCompany.trim().isEmpty() 
               && year != null && year > 0;
    }

    /**
     * 创建主键的字符串标识
     * 用于缓存键或其他需要字符串标识的场景
     * 
     * @return 主键的字符串标识
     */
    public String toKeyString() {
        return String.format("%s_%d", managementCompany, year);
    }
}
