package com.laoshu198838.util.debt;

import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 债权字段映射工具类
 * 解决诉讼表和非诉讼表之间字段名不一致的问题
 * 
 * <AUTHOR>
 */
public class DebtFieldMappingUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtFieldMappingUtil.class);
    
    /**
     * 将诉讼表的业务字段复制到非诉讼表
     * 
     * @param source 源诉讼表记录
     * @param target 目标非诉讼表记录
     */
    public static void copyLitigationToNonLitigation(LitigationClaim source, NonLitigationClaim target) {
        if (source == null || target == null) {
            logger.warn("源记录或目标记录为空，无法进行字段映射");
            return;
        }
        
        // 基本信息复制
        target.setSubjectName(source.getSubjectName());
        target.setManagementCompany(source.getManagementCompany());
        target.setResponsiblePerson(source.getResponsiblePerson());
        target.setDueDate(source.getDueDate());
        
        // 关键字段映射（解决字段名不一致问题）
        target.setOverdueYear(source.getOverdueYear());
        target.setCreditorCategory(source.getClaimType());  // 债权类别映射
        target.setDebtType(source.getDebtType());
        target.setCreditorNature(source.getDebtNature());   // 债权性质映射
        
        // 业务相关字段
        target.setAnnualRecoveryTarget(source.getAnnualRecoveryTarget() != null ? 
            new java.math.BigDecimal(source.getAnnualRecoveryTarget()) : java.math.BigDecimal.ZERO);
        target.setAnnualCumulativeRecovery(source.getAnnualCumulativeRecovery());
        target.setArrangement(source.getArrangement());
        
        logger.debug("诉讼表字段已成功映射到非诉讼表");
    }
    
    /**
     * 将非诉讼表的业务字段复制到诉讼表
     * 
     * @param source 源非诉讼表记录
     * @param target 目标诉讼表记录
     */
    public static void copyNonLitigationToLitigation(NonLitigationClaim source, LitigationClaim target) {
        if (source == null || target == null) {
            logger.warn("源记录或目标记录为空，无法进行字段映射");
            return;
        }
        
        // 基本信息复制
        target.setSubjectName(source.getSubjectName());
        target.setManagementCompany(source.getManagementCompany());
        target.setResponsiblePerson(source.getResponsiblePerson());
        target.setDueDate(source.getDueDate());
        
        // 关键字段映射（解决字段名不一致问题）
        target.setOverdueYear(source.getOverdueYear());
        target.setClaimType(source.getCreditorCategory());  // 债权类别映射
        target.setDebtType(source.getDebtType());
        target.setDebtNature(source.getCreditorNature());   // 债权性质映射
        
        // 业务相关字段
        target.setAnnualRecoveryTarget(source.getAnnualRecoveryTarget() != null ? 
            source.getAnnualRecoveryTarget().toString() : "0");
        target.setAnnualCumulativeRecovery(source.getAnnualCumulativeRecovery());
        target.setArrangement(source.getArrangement());
        
        logger.debug("非诉讼表字段已成功映射到诉讼表");
    }
    
    /**
     * 创建减值准备记录的通用字段复制
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param year 年份
     * @param month 月份
     * @param period 期间
     * @param isLitigation 是否涉诉
     * @param sourceRecord 源记录（可以是诉讼表或非诉讼表）
     * @param frontendCaseName 前端传入的案件名称（可为空）
     * @return 新的减值准备记录
     */
    public static ImpairmentReserve createImpairmentReserveFromDebt(
            String creditor, String debtor, Integer year, Integer month, String period, 
            String isLitigation, Object sourceRecord, String frontendCaseName) {
        
        ImpairmentReserve reserve = new ImpairmentReserve();
        
        // 设置主键
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(creditor);
        key.setDebtor(debtor);
        key.setYear(year);
        key.setMonth(month);
        key.setPeriod(period);
        key.setIsLitigation(isLitigation);
        reserve.setId(key);
        
        // 根据源记录类型复制相关字段
        String caseName = null;
        if (sourceRecord instanceof LitigationClaim) {
            LitigationClaim litigation = (LitigationClaim) sourceRecord;
            reserve.setSubjectName(litigation.getSubjectName());
            reserve.setManagementCompany(litigation.getManagementCompany());
            reserve.setDueDate(litigation.getDueDate());
            reserve.setDebtNature(litigation.getDebtNature());
            reserve.setDebtType(litigation.getDebtType());
            caseName = litigation.getLitigationCase();
        } else if (sourceRecord instanceof NonLitigationClaim) {
            NonLitigationClaim nonLitigation = (NonLitigationClaim) sourceRecord;
            reserve.setSubjectName(nonLitigation.getSubjectName());
            reserve.setManagementCompany(nonLitigation.getManagementCompany());
            reserve.setDueDate(nonLitigation.getDueDate());
            reserve.setDebtNature(nonLitigation.getCreditorNature());
            reserve.setDebtType(nonLitigation.getDebtType());
        }
        
        // 设置案件名称：优先使用前端传入值，其次使用源记录值，最后使用默认值
        if (frontendCaseName != null && !frontendCaseName.trim().isEmpty()) {
            reserve.setCaseName(frontendCaseName);
        } else if (caseName != null && !caseName.trim().isEmpty()) {
            reserve.setCaseName(caseName);
        } else {
            // 默认值：债权人诉债务人
            reserve.setCaseName(creditor + "诉" + debtor);
        }
        
        logger.debug("减值准备记录字段映射完成，是否涉诉：{}，案件名称：{}", isLitigation, reserve.getCaseName());
        return reserve;
    }
    
    /**
     * 重载方法，保持向后兼容性
     */
    public static ImpairmentReserve createImpairmentReserveFromDebt(
            String creditor, String debtor, Integer year, Integer month, String period, 
            String isLitigation, Object sourceRecord) {
        return createImpairmentReserveFromDebt(creditor, debtor, year, month, period, 
            isLitigation, sourceRecord, null);
    }
}