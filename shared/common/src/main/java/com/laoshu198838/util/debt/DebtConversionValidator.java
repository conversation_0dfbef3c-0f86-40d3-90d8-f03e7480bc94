package com.laoshu198838.util.debt;

import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * 债权转换数据一致性验证工具
 * 确保转换前后金额守恒和数据完整性
 * 
 * <AUTHOR>
 */
public class DebtConversionValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtConversionValidator.class);
    
    /**
     * 验证转换前数据的完整性
     * 
     * @param creditor 债权人
     * @param debtor 债务人  
     * @param period 期间
     * @param year 年份
     * @param month 月份
     * @return 验证结果
     */
    public static ValidationResult validateConversionRequest(String creditor, String debtor, 
            String period, Integer year, Integer month) {
        
        ValidationResult result = new ValidationResult();
        
        // 验证必填字段
        if (creditor == null || creditor.trim().isEmpty()) {
            result.addError("债权人不能为空");
        }
        if (debtor == null || debtor.trim().isEmpty()) {
            result.addError("债务人不能为空");
        }
        if (period == null || period.trim().isEmpty()) {
            result.addError("期间不能为空");
        }
        if (year == null || year <= 0) {
            result.addError("年份必须为有效的正整数");
        }
        if (month == null || month < 1 || month > 12) {
            result.addError("月份必须在1-12之间");
        }
        
        logger.debug("转换请求验证完成，错误数量：{}", result.getErrors().size());
        return result;
    }
    
    /**
     * 验证诉讼转非诉讼的金额守恒（使用原始金额）
     * 
     * @param originalAmount 原始诉讼余额
     * @param newClaim 新非诉讼表记录
     * @return 验证结果
     */
    public static ValidationResult validateLitigationToNonLitigationConversionWithOriginalAmount(
            BigDecimal originalAmount, NonLitigationClaim newClaim) {
        
        ValidationResult result = new ValidationResult();
        
        if (originalAmount == null || newClaim == null) {
            result.addError("原始金额或新记录为空，无法验证");
            return result;
        }
        
        // 验证转换当月上月末余额为0（符合需求）
        BigDecimal newLastMonthTotal = safeAdd(
            newClaim.getLastMonthPrincipal(),
            newClaim.getLastMonthInterest(),
            newClaim.getLastMonthPenalty()
        );
        
        if (newLastMonthTotal.compareTo(BigDecimal.ZERO) != 0) {
            result.addWarning("转换当月非诉讼表上月末余额应为0，当前为：" + newLastMonthTotal);
        }
        
        // 验证金额转换正确性  
        BigDecimal newCurrentTotal = safeAdd(
            newClaim.getCurrentMonthPrincipal(),
            newClaim.getCurrentMonthInterest(),
            newClaim.getCurrentMonthPenalty()
        );
        
        if (originalAmount.compareTo(newCurrentTotal) != 0) {
            result.addError(String.format("金额转换不匹配：原诉讼余额 %s，新非诉讼总额 %s", 
                originalAmount, newCurrentTotal));
        }
        
        logger.debug("诉讼转非诉讼验证完成，错误：{}，警告：{}", 
            result.getErrors().size(), result.getWarnings().size());
        return result;
    }
    
    /**
     * 验证诉讼转非诉讼的金额守恒（原方法，保留兼容性）
     * 
     * @param originalClaim 原诉讼表记录
     * @param newClaim 新非诉讼表记录
     * @return 验证结果
     */
    public static ValidationResult validateLitigationToNonLitigationConversion(
            LitigationClaim originalClaim, NonLitigationClaim newClaim) {
        
        ValidationResult result = new ValidationResult();
        
        if (originalClaim == null || newClaim == null) {
            result.addError("原记录或新记录为空，无法验证");
            return result;
        }
        
        // 验证转换当月上月末余额为0（符合需求）
        BigDecimal newLastMonthTotal = safeAdd(
            newClaim.getLastMonthPrincipal(),
            newClaim.getLastMonthInterest(),
            newClaim.getLastMonthPenalty()
        );
        
        if (newLastMonthTotal.compareTo(BigDecimal.ZERO) != 0) {
            result.addWarning("转换当月非诉讼表上月末余额应为0，当前为：" + newLastMonthTotal);
        }
        
        // 验证金额转换正确性  
        BigDecimal originalBalance = originalClaim.getCurrentMonthDebtBalance();
        BigDecimal newCurrentTotal = safeAdd(
            newClaim.getCurrentMonthPrincipal(),
            newClaim.getCurrentMonthInterest(),
            newClaim.getCurrentMonthPenalty()
        );
        
        if (originalBalance != null && newCurrentTotal.compareTo(originalBalance) != 0) {
            result.addError(String.format("金额转换不匹配：原诉讼余额 %s，新非诉讼总额 %s", 
                originalBalance, newCurrentTotal));
        }
        
        // 验证关键字段复制
        validateFieldMapping(originalClaim, newClaim, result);
        
        logger.debug("诉讼转非诉讼验证完成，错误：{}，警告：{}", 
            result.getErrors().size(), result.getWarnings().size());
        return result;
    }
    
    /**
     * 验证非诉讼转诉讼的金额守恒（使用原始金额）
     * 
     * @param originalTotalAmount 原始非诉讼总额
     * @param newClaim 新诉讼表记录
     * @return 验证结果
     */
    public static ValidationResult validateNonLitigationToLitigationConversionWithOriginalAmount(
            BigDecimal originalTotalAmount, LitigationClaim newClaim) {
        
        ValidationResult result = new ValidationResult();
        
        if (originalTotalAmount == null || newClaim == null) {
            result.addError("原始金额或新记录为空，无法验证");
            return result;
        }
        
        // 验证转换当月上月末余额为0（符合需求）
        if (newClaim.getLastMonthDebtBalance() != null && 
            newClaim.getLastMonthDebtBalance().compareTo(BigDecimal.ZERO) != 0) {
            result.addWarning("转换当月诉讼表上月末债权余额应为0，当前为：" + newClaim.getLastMonthDebtBalance());
        }
        
        // 验证金额转换正确性
        BigDecimal newBalance = newClaim.getCurrentMonthDebtBalance();
        
        if (newBalance != null && originalTotalAmount.compareTo(newBalance) != 0) {
            result.addError(String.format("金额转换不匹配：原非诉讼总额 %s，新诉讼余额 %s", 
                originalTotalAmount, newBalance));
        }
        
        logger.debug("非诉讼转诉讼验证完成，错误：{}，警告：{}", 
            result.getErrors().size(), result.getWarnings().size());
        return result;
    }
    
    /**
     * 验证非诉讼转诉讼的金额守恒（原方法，保留兼容性）
     * 
     * @param originalClaim 原非诉讼表记录
     * @param newClaim 新诉讼表记录
     * @return 验证结果
     */
    public static ValidationResult validateNonLitigationToLitigationConversion(
            NonLitigationClaim originalClaim, LitigationClaim newClaim) {
        
        ValidationResult result = new ValidationResult();
        
        if (originalClaim == null || newClaim == null) {
            result.addError("原记录或新记录为空，无法验证");
            return result;
        }
        
        // 验证转换当月上月末余额为0（符合需求）
        if (newClaim.getLastMonthDebtBalance() != null && 
            newClaim.getLastMonthDebtBalance().compareTo(BigDecimal.ZERO) != 0) {
            result.addWarning("转换当月诉讼表上月末债权余额应为0，当前为：" + newClaim.getLastMonthDebtBalance());
        }
        
        // 验证金额转换正确性
        BigDecimal originalTotal = safeAdd(
            originalClaim.getCurrentMonthPrincipal(),
            originalClaim.getCurrentMonthInterest(),
            originalClaim.getCurrentMonthPenalty()
        );
        
        BigDecimal newBalance = newClaim.getCurrentMonthDebtBalance();
        
        if (newBalance != null && originalTotal.compareTo(newBalance) != 0) {
            result.addError(String.format("金额转换不匹配：原非诉讼总额 %s，新诉讼余额 %s", 
                originalTotal, newBalance));
        }
        
        // 验证关键字段复制
        validateFieldMapping(originalClaim, newClaim, result);
        
        logger.debug("非诉讼转诉讼验证完成，错误：{}，警告：{}", 
            result.getErrors().size(), result.getWarnings().size());
        return result;
    }
    
    /**
     * 验证字段映射的正确性
     */
    private static void validateFieldMapping(Object original, Object target, ValidationResult result) {
        if (original instanceof LitigationClaim && target instanceof NonLitigationClaim) {
            LitigationClaim litigation = (LitigationClaim) original;
            NonLitigationClaim nonLitigation = (NonLitigationClaim) target;
            
            if (!safeEquals(litigation.getOverdueYear(), nonLitigation.getOverdueYear())) {
                result.addWarning("逾期年限字段映射可能有误");
            }
            if (!safeEquals(litigation.getClaimType(), nonLitigation.getCreditorCategory())) {
                result.addWarning("债权类别字段映射可能有误");
            }
            if (!safeEquals(litigation.getDebtType(), nonLitigation.getDebtType())) {
                result.addWarning("债权类型字段映射可能有误");
            }
            if (!safeEquals(litigation.getDebtNature(), nonLitigation.getCreditorNature())) {
                result.addWarning("债权性质字段映射可能有误");
            }
        } else if (original instanceof NonLitigationClaim && target instanceof LitigationClaim) {
            NonLitigationClaim nonLitigation = (NonLitigationClaim) original;
            LitigationClaim litigation = (LitigationClaim) target;
            
            if (!safeEquals(nonLitigation.getOverdueYear(), litigation.getOverdueYear())) {
                result.addWarning("逾期年限字段映射可能有误");
            }
            if (!safeEquals(nonLitigation.getCreditorCategory(), litigation.getClaimType())) {
                result.addWarning("债权类别字段映射可能有误");
            }
            if (!safeEquals(nonLitigation.getDebtType(), litigation.getDebtType())) {
                result.addWarning("债权类型字段映射可能有误");
            }
            if (!safeEquals(nonLitigation.getCreditorNature(), litigation.getDebtNature())) {
                result.addWarning("债权性质字段映射可能有误");
            }
        }
    }
    
    /**
     * 安全的BigDecimal加法
     */
    private static BigDecimal safeAdd(BigDecimal... values) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal value : values) {
            if (value != null) {
                result = result.add(value);
            }
        }
        return result;
    }
    
    /**
     * 安全的字符串比较
     */
    private static boolean safeEquals(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equals(str2);
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final java.util.List<String> errors = new java.util.ArrayList<>();
        private final java.util.List<String> warnings = new java.util.ArrayList<>();
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public java.util.List<String> getErrors() {
            return errors;
        }
        
        public java.util.List<String> getWarnings() {
            return warnings;
        }
        
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            if (!errors.isEmpty()) {
                sb.append("错误：").append(String.join("; ", errors));
            }
            if (!warnings.isEmpty()) {
                if (sb.length() > 0) sb.append(" | ");
                sb.append("警告：").append(String.join("; ", warnings));
            }
            return sb.toString();
        }
    }
}