package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease.OverdueDebtDecreaseKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 债权处置数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OverdueDebtDecreaseRepository extends JpaRepository<OverdueDebtDecrease, OverdueDebtDecreaseKey> {

    /**
     * 根据债务人和期间查找处置记录
     * @param debtor 债务人名称
     * @param period 期间（格式：YYYY-MM）
     * @return 处置记录列表
     */
    @Query("SELECT o FROM OverdueDebtDecrease o WHERE o.id.debtor = :debtor AND o.id.period = :period")
    List<OverdueDebtDecrease> findByDebtorAndPeriod(@Param("debtor") String debtor, @Param("period") String period);

    /**
     * 根据债务人查找最新的处置记录
     * @param debtor 债务人名称
     * @return 处置记录（可能为空）
     */
    @Query("SELECT o FROM OverdueDebtDecrease o WHERE o.id.debtor LIKE %:debtor% ORDER BY o.id.period DESC")
    List<OverdueDebtDecrease> findLatestByDebtor(@Param("debtor") String debtor);

    /**
     * 找出年份和月份范围的处置记录
     * @param year 年份
     * @param startMonth 起始月份
     * @param endMonth 终止月份
     * @return 年月范围内的处置记录
     */
    @Query(value = "SELECT " +
                   " 债务人, " +
                   " SUM(每月处置金额) AS 每月处置金额, " +
                   " SUM(现金处置) AS 现金处置, " +
                   " SUM(分期还款) AS 分期还款, " +
                   " SUM(资产抵债) AS 资产抵债, " +
                   " SUM(其他方式) AS 其他方式 " +
                   "FROM 处置表 " +
                   "WHERE 年份 = :yr AND 月份 >= :start AND 月份 <= :end " +
                   "GROUP BY 债务人 " +
                   "HAVING SUM(每月处置金额) <> 0 OR SUM(现金处置) <> 0 OR SUM(分期还款) <> 0 OR SUM(资产抵债) <> 0 OR SUM(其他方式) <> 0",
           nativeQuery = true)
    List<Map<String, Object>> findRecordsByYearAndMonthsBetween(@Param("yr") BigDecimal year, @Param("start") BigDecimal startMonth, @Param("end") BigDecimal endMonth);

    /**
     * 直接计算年份和月份范围内的处置金额总和
     * @param year 年份
     * @param startMonth 起始月份
     * @param endMonth 终止月份
     * @return 处置金额总和
     */
    @Query("SELECT SUM(o.cashDisposal + o.installmentRepayment + o.assetDebt + o.otherWays) " +
           "FROM OverdueDebtDecrease o " +
           "WHERE o.id.year = :yr AND o.id.month >= :start AND o.id.month <= :end")
    BigDecimal calculateTotalDisposalAmount(@Param("yr") BigDecimal year, @Param("start") BigDecimal startMonth, @Param("end") BigDecimal endMonth);

    /**
     * 直接计算年份和月份范围内的处置金额明细总和
     * @param year 年份
     * @param startMonth 起始月份
     * @param endMonth 终止月份
     * @return 包含各类处置金额的汇总数据
     */
    @Query("SELECT new map(" +
           "SUM(o.cashDisposal) as totalCashDisposal, " +
           "SUM(o.installmentRepayment) as totalInstallmentRepayment, " +
           "SUM(o.assetDebt) as totalAssetDebt, " +
           "SUM(o.otherWays) as totalOtherWays) " +
           "FROM OverdueDebtDecrease o " +
           "WHERE o.id.year = :yr AND o.id.month >= :start AND o.id.month <= :end")
    Map<String, BigDecimal> calculateDisposalDetailSummary(@Param("yr") BigDecimal year, @Param("start") BigDecimal startMonth, @Param("end") BigDecimal endMonth);
    
    /**
     * 根据债权人、债务人、期间、年份和月份查找处置记录
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param month 月份
     * @return 处置记录列表
     */
    @Query("SELECT o FROM OverdueDebtDecrease o WHERE o.id.creditor = :creditor AND o.id.debtor = :debtor " +
           "AND o.id.period = :period AND o.id.year = :year AND o.id.month = :month")
    List<OverdueDebtDecrease> findByCreditorAndDebtorAndPeriodAndYearAndMonth(
        @Param("creditor") String creditor,
        @Param("debtor") String debtor,
        @Param("period") String period,
        @Param("year") Integer year,
        @Param("month") BigDecimal month);

    // ==================== 存量债权清收情况统计查询方法（基于表8逻辑） ====================

    /**
     * 基于表8逻辑获取处置数据并进行存量/新增债权拆分
     * 复制表8的SQL逻辑，增加分期还款列，并实现存量债权处置拆分
     *
     * @param year 当前年份
     * @param month 当前月份
     * @param company 管理公司
     * @return 拆分后的处置数据，包含16列：管理公司,债权人,债务人,是否涉诉,科目名称,期间,上年末余额,当年新增债权,当年累计处置金额,现金处置,资产抵债,分期还款,其他方式,备注,存量债权处置金额,新增债权处置金额
     */
    @Query(value = """
        WITH
          year_start_balance AS (
            SELECT
              管理公司,
              债权人,
              债务人,
              是否涉诉,
              科目名称,
              期间,
              SUM(本月末债权余额) AS 上年末余额,
              GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
            FROM 减值准备表
            WHERE 年份 = :year - 1 AND 月份 = 12
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          ),
          new_debt AS (
            SELECT
              管理公司,
              债权人,
              债务人,
              是否涉诉,
              科目名称,
              期间,
              SUM(本月新增债权) AS 当年累计新增债权
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          ),
          disposal AS (
            SELECT
              管理公司,
              债权人,
              债务人,
              是否涉诉,
              期间,
              SUM(每月处置金额) AS 当年累计处置金额,
              SUM(现金处置) AS 现金处置,
              SUM(资产抵债) AS 资产抵债,
              SUM(分期还款) AS 分期还款,
              SUM(其他方式) AS 其他方式
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          all_keys AS (
            SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
            UNION
            SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
          ),
          base_data AS (
            SELECT
              k.管理公司,
              k.债权人,
              k.债务人,
              k.是否涉诉,
              k.科目名称,
              k.期间,
              COALESCE(y.上年末余额, 0) AS 上年末余额,
              COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
              COALESCE(d.当年累计处置金额, 0) AS 当年累计处置金额,
              COALESCE(d.现金处置, 0) AS 现金处置,
              COALESCE(d.资产抵债, 0) AS 资产抵债,
              COALESCE(d.分期还款, 0) AS 分期还款,
              COALESCE(d.其他方式, 0) AS 其他方式,
              COALESCE(y.备注, '') AS 备注
            FROM all_keys k
            LEFT JOIN year_start_balance y
              ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
              AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
            LEFT JOIN new_debt n
              ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
              AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
            LEFT JOIN disposal d
              ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
              AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
            WHERE
              (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
              AND (:company IN ('全部', '所有公司', 'all') OR k.管理公司 = :company)
              AND (COALESCE(d.当年累计处置金额, 0) > 0)
          )
        SELECT
          管理公司,
          债权人,
          债务人,
          是否涉诉,
          科目名称,
          期间,
          上年末余额,
          当年新增债权,
          当年累计处置金额,
          现金处置,
          资产抵债,
          分期还款,
          其他方式,
          备注,
          -- 存量债权处置拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 当年累计处置金额  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0  -- 仅新增债权
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 当年累计处置金额 <= 当年新增债权 THEN 0  -- 全部归新增
                ELSE 当年累计处置金额 - 当年新增债权  -- 超出部分归存量
              END
            ELSE 0
          END AS 存量债权处置金额,
          -- 新增债权处置拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 0  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 当年累计处置金额  -- 仅新增债权
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 当年累计处置金额 <= 当年新增债权 THEN 当年累计处置金额  -- 全部归新增
                ELSE 当年新增债权  -- 新增部分归新增
              END
            ELSE 0
          END AS 新增债权处置金额
        FROM base_data
        ORDER BY 上年末余额 DESC
    """, nativeQuery = true)
    List<Object[]> findDisposalDataWithSplit(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 基于拆分数据计算存量债权清收情况统计 - 简化版本用于测试
     *
     * @param year 当前年份
     * @param month 当前月份
     * @param company 管理公司
     * @return 统计结果：期初金额,本年累计清收处置,现金处置,资产抵债,分期还款,其他方式
     */
    @Query(value = """
        SELECT
          COALESCE(SUM(CASE WHEN y.上年末余额 > 0 THEN y.上年末余额 ELSE 0 END), 0) AS 期初金额,
          COALESCE(SUM(CASE WHEN y.上年末余额 > 0 THEN d.当年累计处置金额 ELSE 0 END), 0) AS 本年累计清收处置,
          COALESCE(SUM(CASE WHEN y.上年末余额 > 0 THEN d.现金处置 ELSE 0 END), 0) AS 现金处置,
          COALESCE(SUM(CASE WHEN y.上年末余额 > 0 THEN d.资产抵债 ELSE 0 END), 0) AS 资产抵债,
          COALESCE(SUM(CASE WHEN y.上年末余额 > 0 THEN d.分期还款 ELSE 0 END), 0) AS 分期还款,
          COALESCE(SUM(CASE WHEN y.上年末余额 > 0 THEN d.其他方式 ELSE 0 END), 0) AS 其他方式
        FROM (
          SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间, SUM(本月末债权余额) AS 上年末余额
          FROM 减值准备表 WHERE 年份 = :year - 1 AND 月份 = 12
          GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          HAVING SUM(本月末债权余额) > 0
        ) y
        LEFT JOIN (
          SELECT 管理公司, 债权人, 债务人, 是否涉诉, 期间,
                 SUM(每月处置金额) AS 当年累计处置金额,
                 SUM(现金处置) AS 现金处置,
                 SUM(资产抵债) AS 资产抵债,
                 SUM(分期还款) AS 分期还款,
                 SUM(其他方式) AS 其他方式
          FROM 处置表 WHERE 年份 = :year AND 月份 <= :month
          GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
        ) d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 AND y.债务人 = d.债务人
            AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
        WHERE (:company IN ('全部', '所有公司', 'all') OR y.管理公司 = :company)
          AND COALESCE(d.当年累计处置金额, 0) > 0
    """, nativeQuery = true)
    Object[] findStockDebtCollectionSummary(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 基于表8逻辑的存量债权清收情况统计汇总 - 新需求实现
     * 复制表8的筛选逻辑，然后进行存量债权拆分
     */
    @Query(value = """
        WITH
          year_start_balance AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 上年末余额,
              GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
            FROM 减值准备表
            WHERE 年份 = :year - 1 AND 月份 = 12
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          ),
          new_debt AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月新增债权) AS 当年累计新增债权
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          ),
          disposal AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 当年累计处置金额,
              SUM(现金处置) AS 现金处置,
              SUM(资产抵债) AS 资产抵债,
              SUM(分期还款) AS 分期还款,
              SUM(其他方式) - SUM(分期还款) AS 其他方式
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          disposal_current_month AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 当月累计处置金额
            FROM 处置表
            WHERE 年份 = :year AND 月份 = :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          disposal_previous_month AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 上月累计处置金额
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month - 1
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          base_data AS (
            SELECT
              k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
              COALESCE(y.上年末余额, 0) AS 上年末余额,
              COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
              COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
              COALESCE(dc.当月累计处置金额, 0) - COALESCE(dp.上月累计处置金额, 0) AS 本月处置金额,
              COALESCE(d.现金处置, 0) AS 现金处置,
              COALESCE(d.资产抵债, 0) AS 资产抵债,
              COALESCE(d.分期还款, 0) AS 分期还款,
              COALESCE(d.其他方式, 0) AS 其他方式,
              COALESCE(y.备注, '') AS 备注
            FROM (
              SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
              UNION
              SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
            ) k
            LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
              AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
            LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
              AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
            LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
              AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
            LEFT JOIN disposal_current_month dc ON k.管理公司 = dc.管理公司 AND k.债权人 = dc.债权人 AND k.债务人 = dc.债务人
              AND k.是否涉诉 = dc.是否涉诉 AND k.期间 = dc.期间
            LEFT JOIN disposal_previous_month dp ON k.管理公司 = dp.管理公司 AND k.债权人 = dp.债权人 AND k.债务人 = dp.债务人
              AND k.是否涉诉 = dp.是否涉诉 AND k.期间 = dp.期间
            WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
              AND (:company IN ('全部', '所有公司', 'all') OR k.管理公司 = :company)
              AND (COALESCE(d.现金处置, 0) > 0 OR COALESCE(d.分期还款, 0) > 0
                   OR COALESCE(d.资产抵债, 0) > 0 OR COALESCE(d.其他方式, 0) > 0)
          ),
          stock_debt_disposal AS (
            SELECT *,
              -- 存量债权累计处置拆分逻辑（基于累计处置金额）
              CASE
                WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 累计处置金额  -- 仅存量债权
                WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0  -- 仅新增债权
                WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
                  CASE
                    WHEN 累计处置金额 <= 当年新增债权 THEN 0  -- 全部归新增
                    ELSE 累计处置金额 - 当年新增债权  -- 超出部分归存量
                  END
                ELSE 0
              END AS 存量债权累计处置金额,
              -- 存量债权本月处置拆分逻辑（基于本月处置金额）
              CASE
                WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 本月处置金额  -- 仅存量债权
                WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0  -- 仅新增债权
                WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
                  CASE
                    WHEN 本月处置金额 <= 当年新增债权 THEN 0  -- 全部归新增
                    ELSE 本月处置金额 - 当年新增债权  -- 超出部分归存量
                  END
                ELSE 0
              END AS 存量债权本月处置金额
            FROM base_data
          )
        SELECT
          -- 期初金额：存量债权的上年末余额之和
          COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 ELSE 0 END), 0) AS 期初金额,

          -- 本月清收金额：当月存量债权处置金额之和
          COALESCE(SUM(存量债权本月处置金额), 0) AS 本月清收金额,

          -- 本年累计清收金额：本年累计存量债权处置金额之和
          COALESCE(SUM(存量债权累计处置金额), 0) AS 本年累计清收金额,

          -- 期末余额：确保数学一致性 = 期初金额 - 本年累计清收金额
          -- 使用与期初金额和累计清收相同的计算逻辑，确保数学一致性
          COALESCE(SUM(CASE WHEN 上年末余额 > 0 THEN 上年末余额 ELSE 0 END), 0) - 
          COALESCE(SUM(存量债权累计处置金额), 0) AS 期末余额,

          -- 存量债权清收处置方式统计（用于柱形图）
          COALESCE(SUM(CASE WHEN 存量债权累计处置金额 > 0 AND 累计处置金额 > 0 THEN 现金处置 * (存量债权累计处置金额 / 累计处置金额) ELSE 0 END), 0) AS 存量现金处置,
          COALESCE(SUM(CASE WHEN 存量债权累计处置金额 > 0 AND 累计处置金额 > 0 THEN 分期还款 * (存量债权累计处置金额 / 累计处置金额) ELSE 0 END), 0) AS 存量分期还款,
          COALESCE(SUM(CASE WHEN 存量债权累计处置金额 > 0 AND 累计处置金额 > 0 THEN 资产抵债 * (存量债权累计处置金额 / 累计处置金额) ELSE 0 END), 0) AS 存量资产抵债,
          COALESCE(SUM(CASE WHEN 存量债权累计处置金额 > 0 AND 累计处置金额 > 0 THEN 其他方式 * (存量债权累计处置金额 / 累计处置金额) ELSE 0 END), 0) AS 存量其他方式
        FROM stock_debt_disposal
    """, nativeQuery = true)
    Object[] findStockDebtCollectionSummaryFromTable8(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 获取存量债权清收情况明细数据 - 基于表8逻辑
     * 返回拆分后的存量债权明细数据
     */
    @Query(value = """
        WITH
          year_start_balance AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 上年末余额,
              GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
            FROM 减值准备表
            WHERE 年份 = :year - 1 AND 月份 = 12
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          ),
          new_debt AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月新增债权) AS 当年累计新增债权
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          ),
          disposal AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 当年累计处置金额,
              SUM(现金处置) AS 现金处置,
              SUM(资产抵债) AS 资产抵债,
              SUM(分期还款) AS 分期还款,
              SUM(其他方式) - SUM(分期还款) AS 其他方式
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          disposal_current_month AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 当月累计处置金额
            FROM 处置表
            WHERE 年份 = :year AND 月份 = :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          disposal_previous_month AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 上月累计处置金额
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month - 1
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          base_data AS (
            SELECT
              k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
              COALESCE(y.上年末余额, 0) AS 上年末余额,
              COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
              COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
              COALESCE(dc.当月累计处置金额, 0) - COALESCE(dp.上月累计处置金额, 0) AS 本月处置金额,
              COALESCE(d.现金处置, 0) AS 现金处置,
              COALESCE(d.资产抵债, 0) AS 资产抵债,
              COALESCE(d.分期还款, 0) AS 分期还款,
              COALESCE(d.其他方式, 0) AS 其他方式,
              COALESCE(y.备注, '') AS 备注
            FROM (
              SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
              UNION
              SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
            ) k
            LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
              AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
            LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
              AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
            LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
              AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
            LEFT JOIN disposal_current_month dc ON k.管理公司 = dc.管理公司 AND k.债权人 = dc.债权人 AND k.债务人 = dc.债务人
              AND k.是否涉诉 = dc.是否涉诉 AND k.期间 = dc.期间
            LEFT JOIN disposal_previous_month dp ON k.管理公司 = dp.管理公司 AND k.债权人 = dp.债权人 AND k.债务人 = dp.债务人
              AND k.是否涉诉 = dp.是否涉诉 AND k.期间 = dp.期间
            WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
              AND (:company IN ('全部', '所有公司', 'all') OR k.管理公司 = :company)
              AND (COALESCE(d.现金处置, 0) > 0 OR COALESCE(d.分期还款, 0) > 0
                   OR COALESCE(d.资产抵债, 0) > 0 OR COALESCE(d.其他方式, 0) > 0)
          )
        SELECT
          管理公司, 债权人, 债务人, 是否涉诉, 期间,
          上年末余额 AS 期初金额,
          -- 本月清收金额：存量债权本月处置拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 本月处置金额  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0  -- 仅新增债权
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 本月处置金额 <= 当年新增债权 THEN 0  -- 全部归新增
                ELSE 本月处置金额 - 当年新增债权  -- 超出部分归存量
              END
            ELSE 0
          END AS 本月清收金额,
          -- 本年累计清收金额：存量债权累计处置拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 累计处置金额  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0  -- 仅新增债权
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 累计处置金额 <= 当年新增债权 THEN 0  -- 全部归新增
                ELSE 累计处置金额 - 当年新增债权  -- 超出部分归存量
              END
            ELSE 0
          END AS 本年累计清收金额,
          -- 期末余额：使用余额拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN
              上年末余额 - 累计处置金额  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN
              0  -- 仅新增债权，无存量余额
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 累计处置金额 <= 当年新增债权 THEN 上年末余额  -- 处置未触及存量
                ELSE 上年末余额 - (累计处置金额 - 当年新增债权)  -- 减去触及存量的部分
              END
            ELSE 上年末余额
          END AS 期末余额
        FROM base_data
        WHERE 上年末余额 > 0  -- 只返回有存量债权的记录
        ORDER BY 管理公司, 债权人, 债务人, 期间
    """, nativeQuery = true)
    List<Object[]> findStockDebtCollectionDetailsFromTable8(@Param("year") int year, @Param("month") int month, @Param("company") String company);





    /**
     * 测试方法 - 简单查询验证Repository是否工作
     */
    @Query(value = "SELECT COUNT(*) FROM 减值准备表 WHERE 年份 = :year", nativeQuery = true)
    Long testQuery(@Param("year") int year);

    /**
     * 验证表8筛选后的原始数据是否存在差异（不进行存量/新增拆分）
     * 
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 验证结果：期初金额总和,累计处置金额总和,理论期末余额,实际期末余额,是否一致
     */
    @Query(value = """
        WITH
          year_start_balance AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 上年末余额
            FROM 减值准备表
            WHERE 年份 = :year - 1 AND 月份 = 12
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          ),
          disposal AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 当年累计处置金额
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          current_balance AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 当前余额
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 = :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          )
        SELECT 
          COALESCE(SUM(y.上年末余额), 0) AS 期初金额总和,
          COALESCE(SUM(d.当年累计处置金额), 0) AS 累计处置金额总和,
          COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(d.当年累计处置金额), 0) AS 理论期末余额,
          COALESCE(SUM(c.当前余额), 0) AS 实际当前余额,
          ABS(COALESCE(SUM(c.当前余额), 0) - (COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(d.当年累计处置金额), 0))) AS 数据差异
        FROM year_start_balance y
        LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
          AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
        LEFT JOIN current_balance c ON y.管理公司 = c.管理公司 AND y.债权人 = c.债权人 
          AND y.债务人 = c.债务人 AND y.是否涉诉 = c.是否涉诉 AND y.期间 = c.期间
          AND y.科目名称 = c.科目名称
        WHERE (:company IN ('全部', '所有公司', 'all') OR y.管理公司 = :company)
    """, nativeQuery = true)
    Object[] verifyTable8RawData(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 数据一致性验证查询
     * 用于诊断数据库中基础数据的一致性问题
     * 
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 验证结果：期初金额,累计处置金额,理论期末余额,实际期末余额,差异
     */
    @Query(value = """
        WITH 
          year_start_balance AS (
            SELECT 
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 上年末余额
            FROM 减值准备表
            WHERE 年份 = :year - 1 AND 月份 = 12
              AND (:company IN ('全部', '所有公司', 'all') OR 管理公司 = :company)
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          ),
          disposal AS (
            SELECT 
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 累计处置金额
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month
              AND (:company IN ('全部', '所有公司', 'all') OR 管理公司 = :company)
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          current_balance AS (
            SELECT 
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 当前余额
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 = :month
              AND (:company IN ('全部', '所有公司', 'all') OR 管理公司 = :company)
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          )
        SELECT 
          COALESCE(SUM(y.上年末余额), 0) AS 期初金额汇总,
          COALESCE(SUM(d.累计处置金额), 0) AS 累计处置金额汇总,
          COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(d.累计处置金额), 0) AS 理论期末余额,
          COALESCE(SUM(c.当前余额), 0) AS 实际期末余额,
          COALESCE(SUM(c.当前余额), 0) - (COALESCE(SUM(y.上年末余额), 0) - COALESCE(SUM(d.累计处置金额), 0)) AS 差异
        FROM year_start_balance y
        LEFT JOIN disposal d ON y.管理公司 = d.管理公司 AND y.债权人 = d.债权人 
          AND y.债务人 = d.债务人 AND y.是否涉诉 = d.是否涉诉 AND y.期间 = d.期间
        LEFT JOIN current_balance c ON y.管理公司 = c.管理公司 AND y.债权人 = c.债权人 
          AND y.债务人 = c.债务人 AND y.是否涉诉 = c.是否涉诉 AND y.期间 = c.期间 
          AND y.科目名称 = c.科目名称
    """, nativeQuery = true)
    Object[] verifyDataConsistency(@Param("year") int year, @Param("month") int month, @Param("company") String company);

    /**
     * 存量债权清收统一查询方法 - 修正版本
     * 🔧 关键修正：移除过度筛选条件，包含所有存量债权（不管是否有新增债权）
     * 基于表8逻辑，返回包含所有必要信息的统一数据集，供应用层进行计算和拆分
     * 
     * @param year 年份
     * @param month 月份
     * @param company 公司
     * @return 统一查询结果，包含明细和汇总计算所需的所有信息
     */
    @Query(value = """
        WITH
          year_start_balance AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 上年末余额,
              GROUP_CONCAT(DISTINCT 备注 SEPARATOR ',') AS 备注
            FROM 减值准备表
            WHERE 年份 = :year - 1 AND 月份 = 12
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING SUM(本月末债权余额) <> 0
          ),
          new_debt AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月新增债权) AS 当年累计新增债权
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          ),
          disposal AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 期间,
              SUM(每月处置金额) AS 当年累计处置金额,
              SUM(CASE WHEN 月份 = :month THEN 每月处置金额 ELSE 0 END) AS 当月处置金额,
              SUM(现金处置) AS 现金处置,
              SUM(资产抵债) AS 资产抵债,
              SUM(分期还款) AS 分期还款,
              SUM(其他方式) - SUM(分期还款) AS 其他方式
            FROM 处置表
            WHERE 年份 = :year AND 月份 <= :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 期间
          ),
          current_balance AS (
            SELECT
              管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
              SUM(本月末债权余额) AS 当前余额
            FROM 减值准备表
            WHERE 年份 = :year AND 月份 = :month
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
          ),
          base_data AS (
            SELECT
              k.管理公司, k.债权人, k.债务人, k.是否涉诉, k.科目名称, k.期间,
              COALESCE(y.上年末余额, 0) AS 上年末余额,
              COALESCE(n.当年累计新增债权, 0) AS 当年新增债权,
              COALESCE(d.当年累计处置金额, 0) AS 累计处置金额,
              COALESCE(cb.当前余额, 0) AS 当前余额,
              COALESCE(d.现金处置, 0) AS 现金处置,
              COALESCE(d.资产抵债, 0) AS 资产抵债,
              COALESCE(d.分期还款, 0) AS 分期还款,
              COALESCE(d.其他方式, 0) AS 其他方式,
              COALESCE(y.备注, '') AS 备注
            FROM (
              SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM year_start_balance
              UNION
              SELECT 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间 FROM new_debt
            ) k
            LEFT JOIN year_start_balance y ON k.管理公司 = y.管理公司 AND k.债权人 = y.债权人 AND k.债务人 = y.债务人
              AND k.是否涉诉 = y.是否涉诉 AND k.科目名称 = y.科目名称 AND k.期间 = y.期间
            LEFT JOIN new_debt n ON k.管理公司 = n.管理公司 AND k.债权人 = n.债权人 AND k.债务人 = n.债务人
              AND k.是否涉诉 = n.是否涉诉 AND k.科目名称 = n.科目名称 AND k.期间 = n.期间
            LEFT JOIN disposal d ON k.管理公司 = d.管理公司 AND k.债权人 = d.债权人 AND k.债务人 = d.债务人
              AND k.是否涉诉 = d.是否涉诉 AND k.期间 = d.期间
            LEFT JOIN current_balance cb ON k.管理公司 = cb.管理公司 AND k.债权人 = cb.债权人 AND k.债务人 = cb.债务人
              AND k.是否涉诉 = cb.是否涉诉 AND k.科目名称 = cb.科目名称 AND k.期间 = cb.期间
            WHERE (COALESCE(y.上年末余额, 0) <> 0 OR COALESCE(n.当年累计新增债权, 0) <> 0)
          )
        SELECT
          '存量债权' AS 数据类型,
          管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间,
          上年末余额 AS 期初金额,
          -- 存量债权累计处置拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN 累计处置金额  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN 0  -- 仅新增债权
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 累计处置金额 <= 当年新增债权 THEN 0  -- 全部归新增
                ELSE 累计处置金额 - 当年新增债权  -- 超出部分归存量
              END
            ELSE 0
          END AS 累计处置债权,
          -- 存量债权余额拆分逻辑
          CASE
            WHEN 上年末余额 > 0 AND 当年新增债权 = 0 THEN
              上年末余额 - 累计处置金额  -- 仅存量债权
            WHEN 上年末余额 = 0 AND 当年新增债权 > 0 THEN
              0  -- 仅新增债权，无存量余额
            WHEN 上年末余额 > 0 AND 当年新增债权 > 0 THEN  -- 混合债权
              CASE
                WHEN 累计处置金额 <= 当年新增债权 THEN 上年末余额  -- 处置未触及存量
                ELSE 上年末余额 - (累计处置金额 - 当年新增债权)  -- 减去触及存量的部分
              END
            ELSE 上年末余额
          END AS 债权余额,
          现金处置, 资产抵债, 分期还款, 其他方式, 备注
        FROM base_data
        WHERE 上年末余额 > 0  -- 只返回有存量债权的记录
          AND (:company IN ('全部', '所有公司', 'all') OR 管理公司 = :company)
        ORDER BY 管理公司, 债权人, 债务人, 期间
    """, nativeQuery = true)
    List<Object[]> findStockDebtCollectionUnifiedData(@Param("year") int year, @Param("month") int month, @Param("company") String company);
}
