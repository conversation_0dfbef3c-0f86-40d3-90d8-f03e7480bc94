package com.laoshu198838.repository.asset;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.asset.AssetFinancialInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 资产财务信息Repository
 * 
 * <AUTHOR>
 */
@Repository
@DataSource("asset-management")
public interface AssetFinancialInfoRepository extends JpaRepository<AssetFinancialInfo, Long> {

    /**
     * 根据资产ID和年月查询财务信息
     */
    Optional<AssetFinancialInfo> findByAssetIdAndFinancialYearAndFinancialMonth(Long assetId, Integer financialYear, Integer financialMonth);

    /**
     * 根据资产ID查询所有财务信息
     */
    List<AssetFinancialInfo> findByAssetIdOrderByFinancialYearDescFinancialMonthDesc(Long assetId);

    /**
     * 根据管理公司和年月查询财务信息
     */
    List<AssetFinancialInfo> findByManagementCompanyAndFinancialYearAndFinancialMonth(String managementCompany, Integer financialYear, Integer financialMonth);

    /**
     * 根据管理公司和年月分页查询财务信息
     */
    Page<AssetFinancialInfo> findByManagementCompanyAndFinancialYearAndFinancialMonth(String managementCompany, Integer financialYear, Integer financialMonth, Pageable pageable);

    /**
     * 根据资产ID和年份查询财务信息
     */
    List<AssetFinancialInfo> findByAssetIdAndFinancialYearOrderByFinancialMonth(Long assetId, Integer financialYear);

    /**
     * 获取指定年月的财务汇总
     */
    @Query("SELECT SUM(f.originalValue), SUM(f.bookValue), SUM(f.accumulatedDepreciation), SUM(f.monthlyDepreciation) " +
           "FROM AssetFinancialInfo f " +
           "WHERE f.managementCompany = :managementCompany AND f.financialYear = :year AND f.financialMonth = :month")
    List<Object[]> getFinancialSummaryByYearMonth(@Param("managementCompany") String managementCompany, 
                                                 @Param("year") Integer year, 
                                                 @Param("month") Integer month);

    /**
     * 获取各管理公司的财务汇总
     */
    @Query("SELECT f.managementCompany, SUM(f.originalValue), SUM(f.bookValue), SUM(f.accumulatedDepreciation) " +
           "FROM AssetFinancialInfo f " +
           "WHERE f.financialYear = :year AND f.financialMonth = :month " +
           "GROUP BY f.managementCompany")
    List<Object[]> getFinancialSummaryByCompany(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 获取资产的最新财务信息
     */
    @Query("SELECT f FROM AssetFinancialInfo f WHERE f.assetId = :assetId " +
           "ORDER BY f.financialYear DESC, f.financialMonth DESC LIMIT 1")
    Optional<AssetFinancialInfo> findLatestFinancialInfoByAssetId(@Param("assetId") Long assetId);

    /**
     * 检查指定资产在指定年月是否已有财务记录
     */
    boolean existsByAssetIdAndFinancialYearAndFinancialMonth(Long assetId, Integer financialYear, Integer financialMonth);

    /**
     * 查询自动生成的财务记录
     */
    List<AssetFinancialInfo> findByIsAutoGeneratedTrueAndFinancialYearAndFinancialMonth(Integer financialYear, Integer financialMonth);

    /**
     * 查询需要生成财务记录的资产（没有当月财务记录的资产）
     */
    @Query("SELECT DISTINCT a.id FROM AssetBasicInfo a " +
           "WHERE a.managementCompany = :managementCompany AND a.status = 'ACTIVE' " +
           "AND NOT EXISTS (SELECT 1 FROM AssetFinancialInfo f " +
           "WHERE f.assetId = a.id AND f.financialYear = :year AND f.financialMonth = :month)")
    List<Long> findAssetsWithoutFinancialRecord(@Param("managementCompany") String managementCompany,
                                               @Param("year") Integer year, 
                                               @Param("month") Integer month);

    /**
     * 获取折旧方法统计
     */
    @Query("SELECT f.depreciationMethod, COUNT(f) FROM AssetFinancialInfo f " +
           "WHERE f.managementCompany = :managementCompany " +
           "GROUP BY f.depreciationMethod")
    List<Object[]> getDepreciationMethodStats(@Param("managementCompany") String managementCompany);

    /**
     * 删除指定资产的所有财务信息
     */
    void deleteByAssetId(Long assetId);

    /**
     * 获取有财务记录的年月列表
     */
    @Query("SELECT DISTINCT f.financialYear, f.financialMonth FROM AssetFinancialInfo f " +
           "WHERE f.managementCompany = :managementCompany " +
           "ORDER BY f.financialYear DESC, f.financialMonth DESC")
    List<Object[]> findDistinctYearMonthByManagementCompany(@Param("managementCompany") String managementCompany);

    /**
     * 获取当前月份的财务汇总
     */
    @Query("SELECT f.managementCompany, SUM(f.originalValue), SUM(f.bookValue), SUM(f.accumulatedDepreciation) " +
           "FROM AssetFinancialInfo f " +
           "WHERE f.financialYear = YEAR(CURRENT_DATE) AND f.financialMonth = MONTH(CURRENT_DATE) " +
           "GROUP BY f.managementCompany")
    List<Object[]> getCurrentMonthFinancialSummary();

    /**
     * 根据多个条件查询财务信息
     */
    @Query("SELECT f FROM AssetFinancialInfo f WHERE " +
           "(:managementCompany IS NULL OR f.managementCompany = :managementCompany) AND " +
           "(:assetId IS NULL OR f.assetId = :assetId) AND " +
           "(:financialYear IS NULL OR f.financialYear = :financialYear) AND " +
           "(:financialMonth IS NULL OR f.financialMonth = :financialMonth) AND " +
           "(:depreciationMethod IS NULL OR f.depreciationMethod = :depreciationMethod) " +
           "ORDER BY f.financialYear DESC, f.financialMonth DESC")
    Page<AssetFinancialInfo> findByConditions(
        @Param("managementCompany") String managementCompany,
        @Param("assetId") Long assetId,
        @Param("financialYear") Integer financialYear,
        @Param("financialMonth") Integer financialMonth,
        @Param("depreciationMethod") AssetFinancialInfo.DepreciationMethod depreciationMethod,
        Pageable pageable);
}
