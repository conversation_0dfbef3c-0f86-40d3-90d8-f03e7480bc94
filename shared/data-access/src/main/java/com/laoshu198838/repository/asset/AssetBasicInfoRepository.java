package com.laoshu198838.repository.asset;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.asset.AssetBasicInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 资产基本信息Repository
 * 
 * <AUTHOR>
 */
@Repository
@DataSource("asset-management")
public interface AssetBasicInfoRepository extends JpaRepository<AssetBasicInfo, Long> {

    /**
     * 根据管理公司查询资产列表
     */
    List<AssetBasicInfo> findByManagementCompanyAndStatus(String managementCompany, AssetBasicInfo.AssetStatus status);

    /**
     * 根据管理公司分页查询资产列表
     */
    Page<AssetBasicInfo> findByManagementCompanyAndStatus(String managementCompany, AssetBasicInfo.AssetStatus status, Pageable pageable);

    /**
     * 根据资产类型查询
     */
    List<AssetBasicInfo> findByAssetTypeAndManagementCompanyAndStatus(
        AssetBasicInfo.AssetType assetType, String managementCompany, AssetBasicInfo.AssetStatus status);

    /**
     * 根据产权人查询
     */
    List<AssetBasicInfo> findByPropertyOwnerIdAndStatus(Long propertyOwnerId, AssetBasicInfo.AssetStatus status);

    /**
     * 查询瑕疵资产（无产权证）
     */
    List<AssetBasicInfo> findByHasPropertyCertificateAndManagementCompanyAndStatus(
        Boolean hasPropertyCertificate, String managementCompany, AssetBasicInfo.AssetStatus status);

    /**
     * 根据资产名称模糊查询
     */
    @Query("SELECT a FROM AssetBasicInfo a WHERE a.assetName LIKE %:assetName% AND a.managementCompany = :managementCompany AND a.status = :status")
    List<AssetBasicInfo> findByAssetNameContainingAndManagementCompanyAndStatus(
        @Param("assetName") String assetName, 
        @Param("managementCompany") String managementCompany, 
        @Param("status") AssetBasicInfo.AssetStatus status);

    /**
     * 统计各管理公司的资产数量
     */
    @Query("SELECT a.managementCompany, COUNT(a) FROM AssetBasicInfo a WHERE a.status = :status GROUP BY a.managementCompany")
    List<Object[]> countByManagementCompanyAndStatus(@Param("status") AssetBasicInfo.AssetStatus status);

    /**
     * 统计各资产类型的数量和面积
     */
    @Query("SELECT a.assetType, COUNT(a), SUM(a.totalArea) FROM AssetBasicInfo a " +
           "WHERE a.managementCompany = :managementCompany AND a.status = :status " +
           "GROUP BY a.assetType")
    List<Object[]> getAssetTypeStatistics(@Param("managementCompany") String managementCompany, 
                                         @Param("status") AssetBasicInfo.AssetStatus status);

    /**
     * 获取瑕疵资产统计
     */
    @Query("SELECT a.managementCompany, a.assetType, COUNT(a), SUM(a.totalArea) FROM AssetBasicInfo a " +
           "WHERE a.hasPropertyCertificate = false AND a.status = :status " +
           "GROUP BY a.managementCompany, a.assetType")
    List<Object[]> getDefectiveAssetStatistics(@Param("status") AssetBasicInfo.AssetStatus status);

    /**
     * 获取资产总面积统计
     */
    @Query("SELECT SUM(a.totalArea) FROM AssetBasicInfo a WHERE a.managementCompany = :managementCompany AND a.status = :status")
    BigDecimal getTotalAreaByManagementCompany(@Param("managementCompany") String managementCompany, 
                                              @Param("status") AssetBasicInfo.AssetStatus status);

    /**
     * 检查资产名称是否已存在
     */
    boolean existsByAssetNameAndManagementCompanyAndIdNot(String assetName, String managementCompany, Long id);

    /**
     * 检查购买合同编号是否已存在
     */
    boolean existsByPurchaseContractNoAndManagementCompanyAndIdNot(String purchaseContractNo, String managementCompany, Long id);

    /**
     * 获取管理公司列表
     */
    @Query("SELECT DISTINCT a.managementCompany FROM AssetBasicInfo a WHERE a.status = :status ORDER BY a.managementCompany")
    List<String> findDistinctManagementCompanies(@Param("status") AssetBasicInfo.AssetStatus status);

    /**
     * 根据多个条件查询资产
     */
    @Query("SELECT a FROM AssetBasicInfo a WHERE " +
           "(:managementCompany IS NULL OR a.managementCompany = :managementCompany) AND " +
           "(:assetType IS NULL OR a.assetType = :assetType) AND " +
           "(:propertyOwnerId IS NULL OR a.propertyOwnerId = :propertyOwnerId) AND " +
           "(:hasPropertyCertificate IS NULL OR a.hasPropertyCertificate = :hasPropertyCertificate) AND " +
           "a.status = :status")
    Page<AssetBasicInfo> findByConditions(
        @Param("managementCompany") String managementCompany,
        @Param("assetType") AssetBasicInfo.AssetType assetType,
        @Param("propertyOwnerId") Long propertyOwnerId,
        @Param("hasPropertyCertificate") Boolean hasPropertyCertificate,
        @Param("status") AssetBasicInfo.AssetStatus status,
        Pageable pageable);

}
