package com.laoshu198838.repository.asset;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.asset.RentalInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 出租信息Repository
 * 
 * <AUTHOR>
 */
@Repository
@DataSource("asset-management")
public interface RentalInfoRepository extends JpaRepository<RentalInfo, Long> {

    /**
     * 根据资产ID查询出租信息
     */
    List<RentalInfo> findByAssetIdOrderByRentalStartDateDesc(Long assetId);

    /**
     * 根据资产ID和合同状态查询出租信息
     */
    List<RentalInfo> findByAssetIdAndContractStatusOrderByRentalStartDateDesc(Long assetId, RentalInfo.ContractStatus contractStatus);

    /**
     * 根据管理公司查询出租信息
     */
    List<RentalInfo> findByManagementCompanyOrderByRentalStartDateDesc(String managementCompany);

    /**
     * 根据管理公司分页查询出租信息
     */
    Page<RentalInfo> findByManagementCompanyOrderByRentalStartDateDesc(String managementCompany, Pageable pageable);

    /**
     * 根据合同编号查询
     */
    List<RentalInfo> findByContractNoContaining(String contractNo);

    /**
     * 根据承租人查询
     */
    List<RentalInfo> findByLesseeContainingAndManagementCompany(String lessee, String managementCompany);

    /**
     * 查询即将到期的合同（30天内）
     */
    List<RentalInfo> findByIsExpiringSoonTrueAndManagementCompanyOrderByRentalEndDate(String managementCompany);

    /**
     * 查询指定日期范围内到期的合同
     */
    @Query("SELECT r FROM RentalInfo r WHERE r.rentalEndDate BETWEEN :startDate AND :endDate " +
           "AND r.managementCompany = :managementCompany AND r.contractStatus = :status " +
           "ORDER BY r.rentalEndDate")
    List<RentalInfo> findByRentalEndDateBetweenAndManagementCompanyAndContractStatus(
        @Param("startDate") LocalDate startDate, 
        @Param("endDate") LocalDate endDate,
        @Param("managementCompany") String managementCompany,
        @Param("status") RentalInfo.ContractStatus status);

    /**
     * 获取指定资产的有效出租面积总和
     */
    @Query("SELECT COALESCE(SUM(r.rentalArea), 0) FROM RentalInfo r " +
           "WHERE r.assetId = :assetId AND r.contractStatus = :status")
    BigDecimal getTotalRentalAreaByAssetId(@Param("assetId") Long assetId, 
                                          @Param("status") RentalInfo.ContractStatus status);

    /**
     * 获取指定资产的有效月租金总和
     */
    @Query("SELECT COALESCE(SUM(r.monthlyRent), 0) FROM RentalInfo r " +
           "WHERE r.assetId = :assetId AND r.contractStatus = :status")
    BigDecimal getTotalMonthlyRentByAssetId(@Param("assetId") Long assetId, 
                                           @Param("status") RentalInfo.ContractStatus status);

    /**
     * 获取管理公司的租金收入统计
     */
    @Query("SELECT r.managementCompany, COUNT(r), SUM(r.rentalArea), SUM(r.monthlyRent) " +
           "FROM RentalInfo r WHERE r.contractStatus = :status " +
           "GROUP BY r.managementCompany")
    List<Object[]> getRentalStatsByManagementCompany(@Param("status") RentalInfo.ContractStatus status);

    /**
     * 检查合同编号是否已存在
     */
    boolean existsByContractNoAndManagementCompanyAndIdNot(String contractNo, String managementCompany, Long id);

    /**
     * 查询指定时间段内生效的合同
     */
    @Query("SELECT r FROM RentalInfo r WHERE r.assetId = :assetId " +
           "AND r.rentalStartDate <= :endDate AND r.rentalEndDate >= :startDate " +
           "AND r.contractStatus = :status")
    List<RentalInfo> findActiveRentalsByAssetIdAndDateRange(
        @Param("assetId") Long assetId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate,
        @Param("status") RentalInfo.ContractStatus status);

    /**
     * 获取当前有效的出租合同
     */
    @Query("SELECT r FROM RentalInfo r WHERE r.assetId = :assetId " +
           "AND r.rentalStartDate <= CURRENT_DATE AND r.rentalEndDate >= CURRENT_DATE " +
           "AND r.contractStatus = :status")
    List<RentalInfo> findCurrentActiveRentalsByAssetId(@Param("assetId") Long assetId, 
                                                      @Param("status") RentalInfo.ContractStatus status);

    /**
     * 删除指定资产的所有出租信息
     */
    void deleteByAssetId(Long assetId);

    /**
     * 获取出租人列表
     */
    @Query("SELECT DISTINCT r.lessor FROM RentalInfo r WHERE r.managementCompany = :managementCompany ORDER BY r.lessor")
    List<String> findDistinctLessorsByManagementCompany(@Param("managementCompany") String managementCompany);

    /**
     * 获取承租人列表
     */
    @Query("SELECT DISTINCT r.lessee FROM RentalInfo r WHERE r.managementCompany = :managementCompany ORDER BY r.lessee")
    List<String> findDistinctLesseesByManagementCompany(@Param("managementCompany") String managementCompany);

    /**
     * 根据多个条件查询出租信息
     */
    @Query("SELECT r FROM RentalInfo r WHERE " +
           "(:managementCompany IS NULL OR r.managementCompany = :managementCompany) AND " +
           "(:assetId IS NULL OR r.assetId = :assetId) AND " +
           "(:contractStatus IS NULL OR r.contractStatus = :contractStatus) AND " +
           "(:lessee IS NULL OR r.lessee LIKE %:lessee%) " +
           "ORDER BY r.rentalStartDate DESC")
    Page<RentalInfo> findByConditions(
        @Param("managementCompany") String managementCompany,
        @Param("assetId") Long assetId,
        @Param("contractStatus") RentalInfo.ContractStatus contractStatus,
        @Param("lessee") String lessee,
        Pageable pageable);
}
