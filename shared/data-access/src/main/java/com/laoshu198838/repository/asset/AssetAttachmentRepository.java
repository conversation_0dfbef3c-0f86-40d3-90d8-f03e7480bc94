package com.laoshu198838.repository.asset;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.asset.AssetAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 资产附件Repository
 * 
 * <AUTHOR>
 */
@Repository
@DataSource("asset-management")
public interface AssetAttachmentRepository extends JpaRepository<AssetAttachment, Long> {

    /**
     * 根据资产ID查询附件列表
     */
    List<AssetAttachment> findByAssetIdOrderByUploadTimeDesc(Long assetId);

    /**
     * 根据资产ID和附件类型查询附件
     */
    List<AssetAttachment> findByAssetIdAndAttachmentTypeOrderByUploadTimeDesc(Long assetId, AssetAttachment.AttachmentType attachmentType);

    /**
     * 根据管理公司查询附件列表
     */
    List<AssetAttachment> findByManagementCompanyOrderByUploadTimeDesc(String managementCompany);

    /**
     * 根据管理公司分页查询附件列表
     */
    Page<AssetAttachment> findByManagementCompanyOrderByUploadTimeDesc(String managementCompany, Pageable pageable);

    /**
     * 根据合同编号查询附件
     */
    List<AssetAttachment> findByContractNoAndManagementCompany(String contractNo, String managementCompany);

    /**
     * 根据文件名查询附件
     */
    List<AssetAttachment> findByOriginalFileNameContainingAndManagementCompany(String fileName, String managementCompany);

    /**
     * 根据附件类型查询附件
     */
    List<AssetAttachment> findByAttachmentTypeAndManagementCompanyOrderByUploadTimeDesc(AssetAttachment.AttachmentType attachmentType, String managementCompany);

    /**
     * 查询待同步的附件
     */
    List<AssetAttachment> findBySyncStatusOrderByUploadTime(AssetAttachment.SyncStatus syncStatus);

    /**
     * 查询同步失败的附件
     */
    List<AssetAttachment> findBySyncStatusAndManagementCompanyOrderByUploadTime(AssetAttachment.SyncStatus syncStatus, String managementCompany);

    /**
     * 获取附件类型统计
     */
    @Query("SELECT a.attachmentType, COUNT(a) FROM AssetAttachment a " +
           "WHERE a.managementCompany = :managementCompany " +
           "GROUP BY a.attachmentType")
    List<Object[]> getAttachmentTypeStats(@Param("managementCompany") String managementCompany);

    /**
     * 获取文件类型统计
     */
    @Query("SELECT a.fileType, COUNT(a), SUM(a.fileSize) FROM AssetAttachment a " +
           "WHERE a.managementCompany = :managementCompany " +
           "GROUP BY a.fileType")
    List<Object[]> getFileTypeStats(@Param("managementCompany") String managementCompany);

    /**
     * 获取同步状态统计
     */
    @Query("SELECT a.syncStatus, COUNT(a) FROM AssetAttachment a " +
           "WHERE a.managementCompany = :managementCompany " +
           "GROUP BY a.syncStatus")
    List<Object[]> getSyncStatusStats(@Param("managementCompany") String managementCompany);

    /**
     * 检查文件路径是否已存在
     */
    boolean existsByFilePathAndIdNot(String filePath, Long id);

    /**
     * 删除指定资产的所有附件
     */
    void deleteByAssetId(Long assetId);

    /**
     * 获取指定资产的附件数量
     */
    long countByAssetId(Long assetId);

    /**
     * 获取指定资产指定类型的附件数量
     */
    long countByAssetIdAndAttachmentType(Long assetId, AssetAttachment.AttachmentType attachmentType);

    /**
     * 根据上传人查询附件
     */
    List<AssetAttachment> findByUploadedByAndManagementCompanyOrderByUploadTimeDesc(String uploadedBy, String managementCompany);

    /**
     * 获取总文件大小
     */
    @Query("SELECT COALESCE(SUM(a.fileSize), 0) FROM AssetAttachment a WHERE a.managementCompany = :managementCompany")
    Long getTotalFileSizeByManagementCompany(@Param("managementCompany") String managementCompany);

    /**
     * 根据多个条件查询附件
     */
    @Query("SELECT a FROM AssetAttachment a WHERE " +
           "(:managementCompany IS NULL OR a.managementCompany = :managementCompany) AND " +
           "(:assetId IS NULL OR a.assetId = :assetId) AND " +
           "(:attachmentType IS NULL OR a.attachmentType = :attachmentType) AND " +
           "(:syncStatus IS NULL OR a.syncStatus = :syncStatus) AND " +
           "(:fileName IS NULL OR a.originalFileName LIKE %:fileName%) " +
           "ORDER BY a.uploadTime DESC")
    Page<AssetAttachment> findByConditions(
        @Param("managementCompany") String managementCompany,
        @Param("assetId") Long assetId,
        @Param("attachmentType") AssetAttachment.AttachmentType attachmentType,
        @Param("syncStatus") AssetAttachment.SyncStatus syncStatus,
        @Param("fileName") String fileName,
        Pageable pageable);
}
