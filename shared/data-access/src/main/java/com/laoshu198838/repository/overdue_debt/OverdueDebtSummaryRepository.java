package com.laoshu198838.repository.overdue_debt;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary.OverdueDebtSummaryKey;

/**
 * 逾期债权汇总数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OverdueDebtSummaryRepository extends JpaRepository<OverdueDebtSummary, OverdueDebtSummaryKey> {

    // 获取本年处置债权金额
    @Query(value = "SELECT COALESCE(SUM(本年减少债权金额), 0) FROM 汇总表", nativeQuery = true)
    Double findTotalReductionAmount();

    // 获取期末债权余额
    @Query(value = "SELECT COALESCE(SUM(本年期末债权余额), 0) FROM 汇总表", nativeQuery = true)
    Double findTotalDebtBalance();

    // 获取年初逾期债权余额
    @Query(value = "SELECT COALESCE(SUM(年初逾期债权余额), 0) FROM 汇总表", nativeQuery = true)
    Double findInitialDebtBalance();

    // 获取存量债权处置金额
    @Query(value = """
                   SELECT
                       (COALESCE((SELECT SUM(本年减少债权金额) FROM 汇总表), 0) -
                        COALESCE((SELECT SUM(处置金额) FROM 新增表), 0)) AS result;""", nativeQuery = true)
    Double findInitialDebtReductionAmount();

    // 获取存量债权期末余额
    @Query(value = """
                   SELECT
                       (COALESCE((SELECT SUM(本年期末债权余额) FROM 汇总表), 0) -
                        COALESCE((SELECT SUM(债权余额) FROM 新增表), 0)) AS result;""", nativeQuery = true)
    Double findInitialDebtEndingBalance();

    // 获取新增债权金额
    @Query(value = "SELECT COALESCE(SUM(新增金额), 0) FROM 新增表", nativeQuery = true)
    Double findNewDebtAmount();

    // 获取新增减少债权金额
    @Query(value = "SELECT COALESCE(SUM(处置金额), 0) FROM 新增表", nativeQuery = true)
    Double findNewDebtReductionAmount();

    //  获得存量债权处置金额=本年处置金额-新增处置金额
    @Query(value = "SELECT COALESCE(SUM(债权余额), 0) FROM 新增表", nativeQuery = true)
    Double findNewDebtBalance();

    @Query(value = """
                       SELECT
                           管理公司 AS companyName,
                           SUM(IFNULL(新增金额, 0)) AS newAmountSum,
                           SUM(IFNULL(处置金额, 0)) AS reductionAmountSum
                       FROM
                           新增表
                       WHERE
                           (:year IS NULL OR 年份 = :year)
                       GROUP BY
                           管理公司
                   """, nativeQuery = true)
    List<Map<String, Object>> findNewDebtSummaryByCompany(@Param("year") String year);

    @Query(value = """
                       SELECT
                         sub1.companyName,
                         sub1.newAmountSum,
                         (sub1.totalReduction - COALESCE(sub2.disposalSum, 0)) AS reductionAmountSum
                       FROM
                       (
                         SELECT
                           管理公司 AS companyName,
                           SUM(`年初逾期债权余额`)   AS newAmountSum,
                           SUM(`本年减少债权金额`)   AS totalReduction
                         FROM `汇总表`
                         WHERE
                           (:year IS NULL OR year = :year)
                         GROUP BY 管理公司
                       ) AS sub1
                       LEFT JOIN
                       (
                         SELECT
                           管理公司 AS companyName,
                           SUM(`处置金额`) AS disposalSum
                         FROM `新增表`
                         WHERE
                           (:year IS NULL OR 年份 = :year)
                         GROUP BY 管理公司
                       ) AS sub2
                         ON sub1.companyName = sub2.companyName
                       WHERE
                         NOT (
                           sub1.newAmountSum = 0
                           AND (sub1.totalReduction - COALESCE(sub2.disposalSum, 0)) = 0
                         )
                   """, nativeQuery = true)
    List<Map<String, Object>> findExistingDebtSummaryByCompany(@Param("year") String year);

    //    从处置表里面查询月份汇总数据（管理公司月度新增处置情况表）
    @Query(value = """
                              SELECT
                                  COALESCE(a.管理公司, b.管理公司) AS companyName,
                                  IFNULL(a.新增月份金额, 0) AS newAmount,
                                  IFNULL(b.处置月份金额, 0) AS reductionAmount
                              FROM (
                                  SELECT
                                      管理公司,
                                      SUM(
                                          CASE :month
                                              WHEN '1月' THEN 1月
                                              WHEN '2月' THEN 2月
                                              WHEN '3月' THEN 3月
                                              WHEN '4月' THEN 4月
                                              WHEN '5月' THEN 5月
                                              WHEN '6月' THEN 6月
                                              WHEN '7月' THEN 7月
                                              WHEN '8月' THEN 8月
                                              WHEN '9月' THEN 9月
                                              WHEN '10月' THEN 10月
                                              WHEN '11月' THEN 11月
                                              WHEN '12月' THEN 12月
                                          END
                                      ) AS 新增月份金额
                                  FROM 新增表
                                  WHERE
                                      年份 = :year
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                                  GROUP BY 管理公司
                              ) a
                              LEFT JOIN (
                                  SELECT
                                      管理公司,
                                      SUM(每月处置金额) AS 处置月份金额
                                  FROM 处置表
                                  WHERE
                                      年份 = :year
                                      AND CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 月份
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                                  GROUP BY 管理公司
                              ) b ON a.管理公司 = b.管理公司

                              UNION

                              SELECT
                                  b.管理公司,
                                  0 AS newAmount,
                                  b.处置月份金额 AS reductionAmount
                              FROM (
                                  SELECT
                                      管理公司,
                                      SUM(每月处置金额) AS 处置月份金额
                                  FROM 处置表
                                  WHERE
                                      年份 = :year
                                      AND CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 月份
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                                  GROUP BY 管理公司
                              ) b
                              WHERE b.管理公司 NOT IN (
                                  SELECT 管理公司
                                  FROM 新增表
                                  WHERE
                                      年份 = :year
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                              )
                              ORDER BY companyName
                   """, nativeQuery = true)
    List<Map<String, Object>> findMonthNewReductionDebtByCompany(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);

    @Query(value = """
                           SELECT
                               管理公司,
                               债权人,
                               债务人,
                               新增金额,
                               处置金额,
                               债权余额
                           FROM
                               新增表
                           WHERE
                               (:year IS NULL OR 年份 = :year)
                               AND (:company IS NULL OR :company = '所有公司' OR :company = '全部' OR 管理公司 = :company)
                               AND (
                                   :month IS NULL
                                   OR :month = '所有月份'
                                   OR :month = '全部'
                                   OR (
                                       :month = '1月' AND 1月 > 0
                                       OR :month = '2月' AND 2月 > 0
                                       OR :month = '3月' AND 3月 > 0
                                       OR :month = '4月' AND 4月 > 0
                                       OR :month = '5月' AND 5月 > 0
                                       OR :month = '6月' AND 6月 > 0
                                       OR :month = '7月' AND 7月 > 0
                                       OR :month = '8月' AND 8月 > 0
                                       OR :month = '9月' AND 9月 > 0
                                       OR :month = '10月' AND 10月 > 0
                                       OR :month = '11月' AND 11月 > 0
                                       OR :month = '12月' AND 12月 > 0
                                   )
                               )
                           ORDER BY 新增金额 DESC
                   """, nativeQuery = true)
    List<Map<String, Object>> findNewDebtDetailList(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);

    // 新增查询方法：获取月度处置金额
    @Query(value = """
        SELECT COALESCE(SUM(每月处置金额), 0) 
        FROM 处置表 
        WHERE 年份 = :year 
          AND 月份 = CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
          AND (:company = '全部' OR 管理公司 = :company)
    """, nativeQuery = true)
    java.math.BigDecimal findMonthDisposalAmount(@Param("year") String year, 
                                               @Param("month") String month, 
                                               @Param("company") String company);

    // 新增查询方法：获取年度累计处置金额
    @Query(value = """
        SELECT COALESCE(SUM(每月处置金额), 0) 
        FROM 处置表 
        WHERE 年份 = :year 
          AND 月份 <= CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
          AND (:company = '全部' OR 管理公司 = :company)
    """, nativeQuery = true)
    java.math.BigDecimal findYearCumulativeDisposalAmount(@Param("year") String year, 
                                                        @Param("company") String company);

    // 新增查询方法：获取处置方式统计
    @Query(value = """
        SELECT 
            '现金处置' as method,
            COALESCE(SUM(现金处置), 0) as amount
        FROM 处置表 
        WHERE 年份 = :year 
          AND 月份 <= CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
          AND (:company = '全部' OR 管理公司 = :company)
          AND 现金处置 > 0
        UNION ALL
        SELECT 
            '分期还款' as method,
            COALESCE(SUM(分期还款), 0) as amount
        FROM 处置表 
        WHERE 年份 = :year 
          AND 月份 <= CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
          AND (:company = '全部' OR 管理公司 = :company)
          AND 分期还款 > 0
        UNION ALL
        SELECT 
            '资产抵债' as method,
            COALESCE(SUM(资产抵债), 0) as amount
        FROM 处置表 
        WHERE 年份 = :year 
          AND 月份 <= CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
          AND (:company = '全部' OR 管理公司 = :company)
          AND 资产抵债 > 0
        UNION ALL
        SELECT 
            '其他方式' as method,
            COALESCE(SUM(其他方式), 0) as amount
        FROM 处置表 
        WHERE 年份 = :year 
          AND 月份 <= CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
          AND (:company = '全部' OR 管理公司 = :company)
          AND 其他方式 > 0
    """, nativeQuery = true)
    List<Map<String, Object>> findDisposalMethodStats(@Param("year") String year, 
                                                     @Param("month") String month, 
                                                     @Param("company") String company);

    // 新增查询方法：获取公司上年末债权余额
    @Query(value = """
        SELECT COALESCE(SUM(本年期末债权余额), 0) 
        FROM 汇总表 
        WHERE 管理公司 = :companyName 
          AND 年份 = :year
    """, nativeQuery = true)
    java.math.BigDecimal findCompanyYearEndAmount(@Param("companyName") String companyName, 
                                                @Param("year") String year);

    // 新增查询方法：获取公司累计回收金额
    @Query(value = """
        SELECT COALESCE(SUM(每月处置金额), 0) 
        FROM 处置表 
        WHERE 管理公司 = :companyName 
          AND 年份 = :year 
          AND 月份 <= CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED)
    """, nativeQuery = true)
    java.math.BigDecimal findCompanyCumulativeRecovery(@Param("companyName") String companyName, 
                                                     @Param("year") String year, 
                                                     @Param("month") String month);

    // 新增查询方法：获取公司期末债权余额
    @Query(value = """
        SELECT COALESCE(SUM(本年期末债权余额), 0) 
        FROM 汇总表 
        WHERE 管理公司 = :companyName 
          AND 年份 = :year
    """, nativeQuery = true)
    java.math.BigDecimal findCompanyPeriodEndAmount(@Param("companyName") String companyName, 
                                                  @Param("year") String year);

    // ==================== 新增债权统计相关查询方法 ====================

    /**
     * 获取新增债权统计数据（参数化年份和月份）
     * 基于用户提供的SQL，将硬编码的年份和月份改为参数化
     * @param year 当前年份
     * @param month 当前月份
     * @param lastYear 去年年份
     * @param company 公司名称
     * @return 新增债权统计数据
     */
    @Query(value = """
        WITH 新增债权数据 AS (
            -- 当年累计新增债权（截至指定月份）
            SELECT 
                '当年累计新增债权' as 数据类型,
                管理公司,
                债权人,
                债务人,
                是否涉诉,
                科目名称,
                期间,
                SUM(
                    CASE 
                        WHEN MONTH(期间) <= :month THEN 新增金额 
                        ELSE 0 
                    END
                ) as 新增债权,
                SUM(
                    CASE 
                        WHEN MONTH(期间) <= :month THEN 处置金额 
                        ELSE 0 
                    END
                ) as 累计处置债权,
                SUM(
                    CASE 
                        WHEN MONTH(期间) <= :month THEN 债权余额 
                        ELSE 0 
                    END
                ) as 债权余额,
                SUM(现金处置) as 现金处置,
                SUM(资产抵债) as 资产抵债,
                SUM(分期还款) as 分期还款,
                SUM(其他方式) as 其他方式,
                '当年新增债权汇总' as 备注
            FROM 新增表 
            WHERE 年份 = :year
              AND (:company = '全部' OR 管理公司 = :company)
              AND 期间 > '2022-04-30'  -- 新增债权基准日期
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING 新增债权 > 0 OR 累计处置债权 > 0 OR 债权余额 > 0

            UNION ALL

            -- 上年末结转债权余额（用于对比）
            SELECT 
                '上年末结转余额' as 数据类型,
                管理公司,
                债权人,
                债务人,
                是否涉诉,
                科目名称,
                期间,
                0 as 新增债权,
                0 as 累计处置债权,
                SUM(债权余额) as 债权余额,
                0 as 现金处置,
                0 as 资产抵债,
                0 as 分期还款,
                0 as 其他方式,
                '上年末债权余额' as 备注
            FROM 新增表 
            WHERE 年份 = :lastYear 
              AND 月份 = 12
              AND (:company = '全部' OR 管理公司 = :company)
              AND 期间 > '2022-04-30'
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING 债权余额 > 0

            UNION ALL

            -- 指定月份新增债权
            SELECT 
                '当月新增债权' as 数据类型,
                管理公司,
                债权人,
                债务人,
                是否涉诉,
                科目名称,
                期间,
                SUM(
                    CASE 
                        WHEN MONTH(期间) = :month AND 年份 = :year THEN 新增金额 
                        ELSE 0 
                    END
                ) as 新增债权,
                SUM(
                    CASE 
                        WHEN MONTH(期间) = :month AND 年份 = :year THEN 处置金额 
                        ELSE 0 
                    END
                ) as 累计处置债权,
                SUM(
                    CASE 
                        WHEN MONTH(期间) = :month AND 年份 = :year THEN 债权余额 
                        ELSE 0 
                    END
                ) as 债权余额,
                SUM(现金处置) as 现金处置,
                SUM(资产抵债) as 资产抵债,
                SUM(分期还款) as 分期还款,
                SUM(其他方式) as 其他方式,
                CONCAT(:year, '年', :month, '月新增') as 备注
            FROM 新增表 
            WHERE 年份 = :year 
              AND MONTH(期间) = :month
              AND (:company = '全部' OR 管理公司 = :company)
              AND 期间 > '2022-04-30'
            GROUP BY 管理公司, 债权人, 债务人, 是否涉诉, 科目名称, 期间
            HAVING 新增债权 > 0 OR 累计处置债权 > 0 OR 债权余额 > 0
        )
        SELECT 
            数据类型,
            管理公司,
            债权人,
            债务人,
            是否涉诉,
            科目名称,
            期间,
            新增债权,
            累计处置债权,
            债权余额,
            现金处置,
            资产抵债,
            分期还款,
            其他方式,
            备注
        FROM 新增债权数据
        ORDER BY 数据类型, 管理公司, 债权人, 债务人
    """, nativeQuery = true)
    List<Object[]> findNewDebtStatistics(
        @Param("year") Integer year,
        @Param("month") Integer month,
        @Param("lastYear") Integer lastYear,
        @Param("company") String company
    );

    /**
     * 获取各公司新增债权汇总统计
     * @param year 年份
     * @param month 月份
     * @param company 公司筛选条件
     * @return 各公司新增债权汇总数据
     */
    @Query(value = """
        SELECT 
            管理公司,
            SUM(CASE WHEN MONTH(期间) <= :month THEN 新增金额 ELSE 0 END) as 累计新增金额,
            SUM(CASE WHEN MONTH(期间) <= :month THEN 处置金额 ELSE 0 END) as 累计处置金额,
            SUM(CASE WHEN MONTH(期间) <= :month THEN 债权余额 ELSE 0 END) as 当前债权余额,
            SUM(CASE WHEN MONTH(期间) = :month THEN 新增金额 ELSE 0 END) as 本月新增金额,
            COUNT(*) as 债权记录数
        FROM 新增表 
        WHERE 年份 = :year
          AND (:company = '全部' OR 管理公司 = :company)
          AND 期间 > '2022-04-30'  -- 新增债权基准日期
        GROUP BY 管理公司
        HAVING 累计新增金额 > 0 OR 累计处置金额 > 0 OR 当前债权余额 > 0
        ORDER BY 累计新增金额 DESC
    """, nativeQuery = true)
    List<Object[]> findNewDebtStatisticsByCompany(
        @Param("year") Integer year,
        @Param("month") Integer month,
        @Param("company") String company
    );

    /**
     * 获取新增债权处置方式统计
     * @param year 年份
     * @param month 月份
     * @param company 公司筛选条件
     * @return 处置方式统计数据
     */
    @Query(value = """
        SELECT 
            '现金处置' as 处置方式,
            SUM(现金处置) as 处置金额,
            COUNT(CASE WHEN 现金处置 > 0 THEN 1 END) as 记录数
        FROM 新增表 
        WHERE 年份 = :year
          AND MONTH(期间) <= :month
          AND (:company = '全部' OR 管理公司 = :company)
          AND 期间 > '2022-04-30'
          AND 现金处置 > 0
        UNION ALL
        SELECT 
            '资产抵债' as 处置方式,
            SUM(资产抵债) as 处置金额,
            COUNT(CASE WHEN 资产抵债 > 0 THEN 1 END) as 记录数
        FROM 新增表 
        WHERE 年份 = :year
          AND MONTH(期间) <= :month
          AND (:company = '全部' OR 管理公司 = :company)
          AND 期间 > '2022-04-30'
          AND 资产抵债 > 0
        UNION ALL
        SELECT 
            '分期还款' as 处置方式,
            SUM(分期还款) as 处置金额,
            COUNT(CASE WHEN 分期还款 > 0 THEN 1 END) as 记录数
        FROM 新增表 
        WHERE 年份 = :year
          AND MONTH(期间) <= :month
          AND (:company = '全部' OR 管理公司 = :company)
          AND 期间 > '2022-04-30'
          AND 分期还款 > 0
        UNION ALL
        SELECT 
            '其他方式' as 处置方式,
            SUM(其他方式) as 处置金额,
            COUNT(CASE WHEN 其他方式 > 0 THEN 1 END) as 记录数
        FROM 新增表 
        WHERE 年份 = :year
          AND MONTH(期间) <= :month
          AND (:company = '全部' OR 管理公司 = :company)
          AND 期间 > '2022-04-30'
          AND 其他方式 > 0
        ORDER BY 处置金额 DESC
    """, nativeQuery = true)
    List<Object[]> findNewDebtDisposalMethodStats(
        @Param("year") Integer year,
        @Param("month") Integer month,
        @Param("company") String company
    );
}
