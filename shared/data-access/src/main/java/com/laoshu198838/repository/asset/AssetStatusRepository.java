package com.laoshu198838.repository.asset;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.asset.AssetStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 资产状态Repository
 * 
 * <AUTHOR>
 */
@Repository
@DataSource("asset-management")
public interface AssetStatusRepository extends JpaRepository<AssetStatus, Long> {

    /**
     * 根据资产ID和年月查询状态
     */
    Optional<AssetStatus> findByAssetIdAndStatusYearAndStatusMonth(Long assetId, Integer statusYear, Integer statusMonth);

    /**
     * 根据资产ID查询所有状态记录
     */
    List<AssetStatus> findByAssetIdOrderByStatusYearDescStatusMonthDesc(Long assetId);

    /**
     * 根据管理公司和年月查询状态列表
     */
    List<AssetStatus> findByManagementCompanyAndStatusYearAndStatusMonth(String managementCompany, Integer statusYear, Integer statusMonth);

    /**
     * 根据管理公司和年月分页查询状态列表
     */
    Page<AssetStatus> findByManagementCompanyAndStatusYearAndStatusMonth(String managementCompany, Integer statusYear, Integer statusMonth, Pageable pageable);

    /**
     * 获取指定年月的面积分布统计
     */
    @Query("SELECT SUM(s.totalArea), SUM(s.selfUseArea), SUM(s.rentalArea), SUM(s.idleArea) " +
           "FROM AssetStatus s " +
           "WHERE s.managementCompany = :managementCompany AND s.statusYear = :year AND s.statusMonth = :month")
    List<Object[]> getAreaDistributionByYearMonth(@Param("managementCompany") String managementCompany, 
                                                 @Param("year") Integer year, 
                                                 @Param("month") Integer month);

    /**
     * 获取各管理公司的盘活统计
     */
    @Query("SELECT s.managementCompany, " +
           "SUM(s.totalArea) as totalArea, " +
           "SUM(s.selfUseArea + s.rentalArea) as activatedArea, " +
           "ROUND(SUM(s.selfUseArea + s.rentalArea) / SUM(s.totalArea) * 100, 2) as activationRate " +
           "FROM AssetStatus s " +
           "WHERE s.statusYear = :year AND s.statusMonth = :month " +
           "GROUP BY s.managementCompany")
    List<Object[]> getActivationStatsByYearMonth(@Param("year") Integer year, @Param("month") Integer month);

    /**
     * 获取资产的最新状态
     */
    @Query("SELECT s FROM AssetStatus s WHERE s.assetId = :assetId " +
           "ORDER BY s.statusYear DESC, s.statusMonth DESC LIMIT 1")
    Optional<AssetStatus> findLatestStatusByAssetId(@Param("assetId") Long assetId);

    /**
     * 获取指定资产在指定年份的所有状态记录
     */
    List<AssetStatus> findByAssetIdAndStatusYearOrderByStatusMonth(Long assetId, Integer statusYear);

    /**
     * 检查指定资产在指定年月是否已有状态记录
     */
    boolean existsByAssetIdAndStatusYearAndStatusMonth(Long assetId, Integer statusYear, Integer statusMonth);

    /**
     * 获取当前月份的面积分布（用于圆饼图）
     */
    @Query("SELECT s.managementCompany, SUM(s.totalArea), SUM(s.selfUseArea), SUM(s.rentalArea), SUM(s.idleArea) " +
           "FROM AssetStatus s " +
           "WHERE s.statusYear = YEAR(CURRENT_DATE) AND s.statusMonth = MONTH(CURRENT_DATE) " +
           "GROUP BY s.managementCompany")
    List<Object[]> getCurrentMonthAreaDistribution();

    /**
     * 获取指定管理公司当前月份的面积分布
     */
    @Query("SELECT SUM(s.totalArea), SUM(s.selfUseArea), SUM(s.rentalArea), SUM(s.idleArea) " +
           "FROM AssetStatus s " +
           "WHERE s.managementCompany = :managementCompany " +
           "AND s.statusYear = YEAR(CURRENT_DATE) AND s.statusMonth = MONTH(CURRENT_DATE)")
    List<Object[]> getCurrentMonthAreaDistributionByCompany(@Param("managementCompany") String managementCompany);

    /**
     * 删除指定资产的所有状态记录
     */
    void deleteByAssetId(Long assetId);

    /**
     * 获取有状态记录的年月列表
     */
    @Query("SELECT DISTINCT s.statusYear, s.statusMonth FROM AssetStatus s " +
           "WHERE s.managementCompany = :managementCompany " +
           "ORDER BY s.statusYear DESC, s.statusMonth DESC")
    List<Object[]> findDistinctYearMonthByManagementCompany(@Param("managementCompany") String managementCompany);

    /**
     * 获取闲置面积大于0的资产状态（待盘活资产）
     */
    @Query("SELECT s FROM AssetStatus s " +
           "WHERE s.managementCompany = :managementCompany " +
           "AND s.statusYear = :year AND s.statusMonth = :month " +
           "AND s.idleArea > 0")
    List<AssetStatus> findIdleAssetsByYearMonth(@Param("managementCompany") String managementCompany, 
                                               @Param("year") Integer year, 
                                               @Param("month") Integer month);
}
