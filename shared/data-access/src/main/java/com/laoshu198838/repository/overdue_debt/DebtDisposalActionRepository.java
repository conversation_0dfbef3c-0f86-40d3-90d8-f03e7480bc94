package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease.OverdueDebtDecreaseKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 债权处置记录数据访问接口
 * 用于查询债权的处置明细记录
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Repository
@DataSource("primary")
public interface DebtDisposalActionRepository extends JpaRepository<OverdueDebtDecrease, OverdueDebtDecreaseKey> {
    
    /**
     * 根据债权ID和年月查询处置记录
     * @param debtId 债权ID（序号）
     * @param year 年份
     * @param month 月份
     * @return 处置记录列表
     */
    @Query(value = """
        SELECT 
            d.每月处置金额 as disposal_amount,
            d.现金处置 as cash_disposal,
            d.分期还款 as installment_repayment,
            d.资产抵债 as asset_debt,
            d.其他方式 as other_ways,
            d.是否涉诉 as is_litigation,
            d.债权人 as creditor,
            d.债务人 as debtor,
            d.年份 as year,
            d.月份 as month
        FROM 处置表 d
        WHERE d.序号 = :debtId
          AND (:year IS NULL OR d.年份 = :year)
          AND (:month IS NULL OR d.月份 <= :month)
          AND d.每月处置金额 > 0
        ORDER BY d.年份, d.月份
    """, nativeQuery = true)
    List<Map<String, Object>> findByDebtIdAndYearMonth(
            @Param("debtId") Long debtId,
            @Param("year") String year,
            @Param("month") String month);
    
    /**
     * 根据债权人、债务人和年月查询处置记录
     * 用于没有明确债权ID时的查询
     * @param creditor 债权人
     * @param debtor 债务人
     * @param year 年份
     * @param month 月份
     * @return 处置记录列表
     */
    @Query(value = """
        SELECT 
            d.每月处置金额 as disposal_amount,
            d.现金处置 as cash_disposal,
            d.分期还款 as installment_repayment,
            d.资产抵债 as asset_debt,
            d.其他方式 as other_ways,
            d.是否涉诉 as is_litigation,
            d.序号 as debt_id,
            d.年份 as year,
            d.月份 as month
        FROM 处置表 d
        WHERE d.债权人 = :creditor
          AND d.债务人 = :debtor
          AND (:year IS NULL OR d.年份 = :year)
          AND (:month IS NULL OR d.月份 <= :month)
          AND d.每月处置金额 > 0
        ORDER BY d.年份, d.月份
    """, nativeQuery = true)
    List<Map<String, Object>> findByCreditorDebtorAndYearMonth(
            @Param("creditor") String creditor,
            @Param("debtor") String debtor,
            @Param("year") String year,
            @Param("month") String month);
}