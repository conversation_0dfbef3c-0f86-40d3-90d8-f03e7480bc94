package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.entity.overdue_debt.CollectionTarget;
import com.laoshu198838.entity.overdue_debt.CollectionTargetKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 清收目标表数据访问接口
 * 
 * <p>提供清收目标数据的CRUD操作和查询功能</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-11
 */
@Repository
public interface CollectionTargetRepository extends JpaRepository<CollectionTarget, CollectionTargetKey> {
    
    /**
     * 根据年份查询所有公司的清收目标
     * 
     * @param year 年份
     * @return 清收目标列表
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.year = :year ORDER BY ct.sequence")
    List<CollectionTarget> findByYear(@Param("year") Integer year);
    
    /**
     * 根据管理公司和年份查询清收目标
     * 
     * @param managementCompany 管理公司
     * @param year 年份
     * @return 清收目标
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.managementCompany = :company AND ct.id.year = :year")
    Optional<CollectionTarget> findByCompanyAndYear(@Param("company") String company, @Param("year") Integer year);
    
    /**
     * 查询指定公司的所有年份清收目标
     * 
     * @param managementCompany 管理公司
     * @return 清收目标列表
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.managementCompany = :company ORDER BY ct.id.year DESC")
    List<CollectionTarget> findByCompany(@Param("company") String company);
    
    /**
     * 批量查询多个公司指定年份的清收目标
     * 
     * @param companies 公司列表
     * @param year 年份
     * @return 清收目标列表
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.managementCompany IN :companies AND ct.id.year = :year ORDER BY ct.sequence")
    List<CollectionTarget> findByCompaniesAndYear(@Param("companies") List<String> companies, @Param("year") Integer year);
    
    /**
     * 获取所有不重复的管理公司列表
     * 
     * @return 管理公司列表
     */
    @Query("SELECT DISTINCT ct.id.managementCompany FROM CollectionTarget ct ORDER BY ct.id.managementCompany")
    List<String> findDistinctCompanies();
    
    /**
     * 检查指定年份是否有清收目标数据
     * 
     * @param year 年份
     * @return 是否存在数据
     */
    @Query("SELECT COUNT(ct) > 0 FROM CollectionTarget ct WHERE ct.id.year = :year")
    boolean existsByYear(@Param("year") Integer year);
}