package com.laoshu198838.config;

import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 资产管理数据源配置
 *
 * 专门为asset_management数据库提供数据源配置，支持JPA Repository。
 * 独立的EntityManagerFactory避免与主数据源的实体类冲突。
 *
 * 主要用途：
 * - 为资产管理相关实体提供JPA Repository支持
 * - 支持资产基本信息、状态、财务信息、出租信息、附件管理
 * - 独立的事务管理
 *
 * <AUTHOR>
 */
@Configuration
@EnableJpaRepositories(
    basePackages = "com.laoshu198838.repository.asset",
    entityManagerFactoryRef = "assetManagementEntityManagerFactory",
    transactionManagerRef = "assetManagementTransactionManager"
)
public class AssetManagementDataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(AssetManagementDataSourceConfig.class);

    public AssetManagementDataSourceConfig() {
        logger.info("=== AssetManagementDataSourceConfig 配置类已初始化 - 提供asset_management数据库数据源 ===");
        logger.info("=== @EnableJpaRepositories 配置: basePackages=com.laoshu198838.repository.asset ===");
    }

    /**
     * 资产管理数据源属性配置
     */
    @Bean("assetManagementDataSourceProperties")
    @ConfigurationProperties("spring.datasource.asset-management")
    public DataSourceProperties assetManagementDataSourceProperties() {
        logger.info("=== 创建资产管理数据源属性配置 ===");
        return new DataSourceProperties();
    }

    /**
     * 资产管理数据源
     */
    @Bean("assetManagementDataSource")
    public DataSource assetManagementDataSource() {
        DataSourceProperties properties = assetManagementDataSourceProperties();
        
        logger.info("=== 创建资产管理数据源，URL: {} ===", properties.getUrl());
        
        // 使用HikariDataSource以确保autoCommit=false配置生效
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(properties.getUrl());
        dataSource.setUsername(properties.getUsername());
        dataSource.setPassword(properties.getPassword());
        dataSource.setDriverClassName(properties.getDriverClassName());

        // 连接池配置
        dataSource.setMaximumPoolSize(10);
        dataSource.setMinimumIdle(2);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);

        // 关键：强制禁用自动提交
        dataSource.setAutoCommit(false);

        // 添加连接初始化SQL，确保每个连接都禁用自动提交
        dataSource.setConnectionInitSql("SET autocommit=0");

        logger.info("=== 资产管理数据源创建完成，连接池配置: maxPoolSize=10, minIdle=2, autoCommit=false ===");
        
        return dataSource;
    }

    /**
     * 资产管理实体管理器工厂
     */
    @Bean("assetManagementEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean assetManagementEntityManagerFactory(
            @Qualifier("assetManagementDataSource") DataSource dataSource) {

        logger.info("=== 创建资产管理EntityManagerFactory ===");

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setDataSource(dataSource);
        factory.setPackagesToScan("com.laoshu198838.entity.asset");
        factory.setPersistenceUnitName("assetManagementPersistenceUnit");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);
        vendorAdapter.setShowSql(false);
        factory.setJpaVendorAdapter(vendorAdapter);

        // JPA属性配置
        Map<String, Object> jpaProperties = new HashMap<>();
        jpaProperties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        jpaProperties.put("hibernate.hbm2ddl.auto", "update");
        jpaProperties.put("hibernate.show_sql", "true");
        jpaProperties.put("hibernate.format_sql", "true");
        jpaProperties.put("hibernate.physical_naming_strategy",
            "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");

        // 禁用缓存
        jpaProperties.put("hibernate.cache.use_second_level_cache", "false");
        jpaProperties.put("hibernate.cache.use_query_cache", "false");

        // 连接配置
        jpaProperties.put("hibernate.connection.autocommit", "false");
        jpaProperties.put("hibernate.current_session_context_class", "thread");

        factory.setJpaPropertyMap(jpaProperties);

        logger.info("=== 资产管理EntityManagerFactory配置完成，扫描包: com.laoshu198838.entity.asset ===");

        return factory;
    }

    /**
     * 资产管理事务管理器
     */
    @Bean("assetManagementTransactionManager")
    public PlatformTransactionManager assetManagementTransactionManager(
            @Qualifier("assetManagementEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        
        logger.info("=== 创建资产管理事务管理器 ===");
        
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory.getObject());
        
        logger.info("=== 资产管理事务管理器创建完成 ===");
        
        return transactionManager;
    }
}
